<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.company.CompanyMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="avatar_url" jdbcType="VARCHAR" property="avatar_url" />
    <result column="certificate_num" jdbcType="CHAR" property="certificateNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="office_address" jdbcType="VARCHAR" property="officeAddress" />
    <result column="super_admin_id" jdbcType="CHAR" property="superAdminId" />
    <result column="account_manager_id" jdbcType="CHAR" property="accountManagerId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="third_corp_id" jdbcType="VARCHAR" property="thirdCorpId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name,avatar_url, certificate_num , status, alias, office_address, super_admin_id, account_manager_id,
    remark, third_corp_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.CompanyExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from company
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.CompanyExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company (id, name, certificate_num, 
      status, alias, office_address, 
      super_admin_id, account_manager_id, remark, 
      third_corp_id)
    values (#{id,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{certificateNum,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{alias,jdbcType=VARCHAR}, #{officeAddress,jdbcType=VARCHAR}, 
      #{superAdminId,jdbcType=CHAR}, #{accountManagerId,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, 
      #{thirdCorpId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="certificateNum != null">
        certificate_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="alias != null">
        alias,
      </if>
      <if test="officeAddress != null">
        office_address,
      </if>
      <if test="superAdminId != null">
        super_admin_id,
      </if>
      <if test="accountManagerId != null">
        account_manager_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="thirdCorpId != null">
        third_corp_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="certificateNum != null">
        #{certificateNum,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="officeAddress != null">
        #{officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="superAdminId != null">
        #{superAdminId,jdbcType=CHAR},
      </if>
      <if test="accountManagerId != null">
        #{accountManagerId,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="thirdCorpId != null">
        #{thirdCorpId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.CompanyExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.certificateNum != null">
        certificate_num = #{record.certificateNum,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.alias != null">
        alias = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.officeAddress != null">
        office_address = #{record.officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.superAdminId != null">
        super_admin_id = #{record.superAdminId,jdbcType=CHAR},
      </if>
      <if test="record.accountManagerId != null">
        account_manager_id = #{record.accountManagerId,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdCorpId != null">
        third_corp_id = #{record.thirdCorpId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company
    set id = #{record.id,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      certificate_num = #{record.certificateNum,jdbcType=CHAR},
      status = #{record.status,jdbcType=INTEGER},
      alias = #{record.alias,jdbcType=VARCHAR},
      office_address = #{record.officeAddress,jdbcType=VARCHAR},
      super_admin_id = #{record.superAdminId,jdbcType=CHAR},
      account_manager_id = #{record.accountManagerId,jdbcType=CHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      third_corp_id = #{record.thirdCorpId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="avatar_url != null">
        avatar_url = #{avatar_url,jdbcType=VARCHAR},
      </if>

      <if test="certificateNum != null">
        certificate_num = #{certificateNum,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="alias != null">
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="officeAddress != null">
        office_address = #{officeAddress,jdbcType=VARCHAR},
      </if>
      <if test="superAdminId != null">
        super_admin_id = #{superAdminId,jdbcType=CHAR},
      </if>
      <if test="accountManagerId != null">
        account_manager_id = #{accountManagerId,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="thirdCorpId != null">
        third_corp_id = #{thirdCorpId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update company
    set name = #{name,jdbcType=VARCHAR},
       avatar_url = #{avatar_url,jdbcType=VARCHAR},
      certificate_num = #{certificateNum,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      alias = #{alias,jdbcType=VARCHAR},
      office_address = #{officeAddress,jdbcType=VARCHAR},
      super_admin_id = #{superAdminId,jdbcType=CHAR},
      account_manager_id = #{accountManagerId,jdbcType=CHAR},
      remark = #{remark,jdbcType=VARCHAR},
      third_corp_id = #{thirdCorpId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>