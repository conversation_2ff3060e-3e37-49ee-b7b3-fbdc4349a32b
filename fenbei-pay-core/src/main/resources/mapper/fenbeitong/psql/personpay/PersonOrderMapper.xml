<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.personpay.PersonOrderMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="device" jdbcType="VARCHAR" property="device" />
    <result column="live_mode" jdbcType="BIT" property="liveMode" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="body" jdbcType="VARCHAR" property="body" />
    <result column="optional" jdbcType="VARCHAR" property="optional" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="auth_code" jdbcType="VARCHAR" property="authCode" />
    <result column="time_expire" jdbcType="VARCHAR" property="timeExpire" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="sdk_mark" jdbcType="VARCHAR" property="sdkMark" />
    <result column="sign_type" jdbcType="VARCHAR" property="signType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="account_type" jdbcType="SMALLINT" property="accountType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, fb_order_id, client_ip, device, live_mode, currency, amount, subject, body, optional, 
    extra, notify_url, auth_code, time_expire, version, sdk_mark, sign_type, create_time, 
    update_time, account_type
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrderExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_order (id, fb_order_id, client_ip, 
      device, live_mode, currency, 
      amount, subject, body, 
      optional, extra, notify_url, 
      auth_code, time_expire, version, 
      sdk_mark, sign_type, create_time, 
      update_time, account_type)
    values (#{id,jdbcType=VARCHAR}, #{fbOrderId,jdbcType=VARCHAR}, #{clientIp,jdbcType=VARCHAR}, 
      #{device,jdbcType=VARCHAR}, #{liveMode,jdbcType=BIT}, #{currency,jdbcType=VARCHAR}, 
      #{amount,jdbcType=BIGINT}, #{subject,jdbcType=VARCHAR}, #{body,jdbcType=VARCHAR}, 
      #{optional,jdbcType=VARCHAR}, #{extra,jdbcType=VARCHAR}, #{notifyUrl,jdbcType=VARCHAR}, 
      #{authCode,jdbcType=VARCHAR}, #{timeExpire,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{sdkMark,jdbcType=VARCHAR}, #{signType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{accountType,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="clientIp != null">
        client_ip,
      </if>
      <if test="device != null">
        device,
      </if>
      <if test="liveMode != null">
        live_mode,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="subject != null">
        subject,
      </if>
      <if test="body != null">
        body,
      </if>
      <if test="optional != null">
        optional,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="notifyUrl != null">
        notify_url,
      </if>
      <if test="authCode != null">
        auth_code,
      </if>
      <if test="timeExpire != null">
        time_expire,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="sdkMark != null">
        sdk_mark,
      </if>
      <if test="signType != null">
        sign_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        #{device,jdbcType=VARCHAR},
      </if>
      <if test="liveMode != null">
        #{liveMode,jdbcType=BIT},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="subject != null">
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="body != null">
        #{body,jdbcType=VARCHAR},
      </if>
      <if test="optional != null">
        #{optional,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="authCode != null">
        #{authCode,jdbcType=VARCHAR},
      </if>
      <if test="timeExpire != null">
        #{timeExpire,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="sdkMark != null">
        #{sdkMark,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        #{signType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.clientIp != null">
        client_ip = #{record.clientIp,jdbcType=VARCHAR},
      </if>
      <if test="record.device != null">
        device = #{record.device,jdbcType=VARCHAR},
      </if>
      <if test="record.liveMode != null">
        live_mode = #{record.liveMode,jdbcType=BIT},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.subject != null">
        subject = #{record.subject,jdbcType=VARCHAR},
      </if>
      <if test="record.body != null">
        body = #{record.body,jdbcType=VARCHAR},
      </if>
      <if test="record.optional != null">
        optional = #{record.optional,jdbcType=VARCHAR},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyUrl != null">
        notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.authCode != null">
        auth_code = #{record.authCode,jdbcType=VARCHAR},
      </if>
      <if test="record.timeExpire != null">
        time_expire = #{record.timeExpire,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.sdkMark != null">
        sdk_mark = #{record.sdkMark,jdbcType=VARCHAR},
      </if>
      <if test="record.signType != null">
        sign_type = #{record.signType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=SMALLINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_order
    set id = #{record.id,jdbcType=VARCHAR},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      client_ip = #{record.clientIp,jdbcType=VARCHAR},
      device = #{record.device,jdbcType=VARCHAR},
      live_mode = #{record.liveMode,jdbcType=BIT},
      currency = #{record.currency,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      subject = #{record.subject,jdbcType=VARCHAR},
      body = #{record.body,jdbcType=VARCHAR},
      optional = #{record.optional,jdbcType=VARCHAR},
      extra = #{record.extra,jdbcType=VARCHAR},
      notify_url = #{record.notifyUrl,jdbcType=VARCHAR},
      auth_code = #{record.authCode,jdbcType=VARCHAR},
      time_expire = #{record.timeExpire,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      sdk_mark = #{record.sdkMark,jdbcType=VARCHAR},
      sign_type = #{record.signType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      account_type = #{record.accountType,jdbcType=SMALLINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_order
    <set>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        client_ip = #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="device != null">
        device = #{device,jdbcType=VARCHAR},
      </if>
      <if test="liveMode != null">
        live_mode = #{liveMode,jdbcType=BIT},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="subject != null">
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="body != null">
        body = #{body,jdbcType=VARCHAR},
      </if>
      <if test="optional != null">
        optional = #{optional,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="notifyUrl != null">
        notify_url = #{notifyUrl,jdbcType=VARCHAR},
      </if>
      <if test="authCode != null">
        auth_code = #{authCode,jdbcType=VARCHAR},
      </if>
      <if test="timeExpire != null">
        time_expire = #{timeExpire,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="sdkMark != null">
        sdk_mark = #{sdkMark,jdbcType=VARCHAR},
      </if>
      <if test="signType != null">
        sign_type = #{signType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_order
    set fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      device = #{device,jdbcType=VARCHAR},
      live_mode = #{liveMode,jdbcType=BIT},
      currency = #{currency,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      subject = #{subject,jdbcType=VARCHAR},
      body = #{body,jdbcType=VARCHAR},
      optional = #{optional,jdbcType=VARCHAR},
      extra = #{extra,jdbcType=VARCHAR},
      notify_url = #{notifyUrl,jdbcType=VARCHAR},
      auth_code = #{authCode,jdbcType=VARCHAR},
      time_expire = #{timeExpire,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      sdk_mark = #{sdkMark,jdbcType=VARCHAR},
      sign_type = #{signType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      account_type = #{accountType,jdbcType=SMALLINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>