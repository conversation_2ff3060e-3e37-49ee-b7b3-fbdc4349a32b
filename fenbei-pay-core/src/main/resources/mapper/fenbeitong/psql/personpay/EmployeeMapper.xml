<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.employee.EmployeeMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.usercenter.api.model.po.employee.Employee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="first_name" jdbcType="VARCHAR" property="first_name" />
    <result column="last_name" jdbcType="VARCHAR" property="last_name" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone_num" jdbcType="VARCHAR" property="phone_num" />
    <result column="id_number" jdbcType="VARCHAR" property="id_number" />
    <result column="id_type" jdbcType="INTEGER" property="id_type" />
    <result column="avatar_url" jdbcType="VARCHAR" property="avatar_url" />
    <result column="role" jdbcType="INTEGER" property="role" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modify_time" />
    <result column="fixed_phone" jdbcType="VARCHAR" property="fixed_phone" />
    <result column="birth_date" jdbcType="TIMESTAMP" property="birth_date" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="pinyin" jdbcType="VARCHAR" property="pinyin" />
    <result column="current_company_id" jdbcType="VARCHAR" property="current_company_id" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, first_name, last_name, email, phone_num, id_number, id_type, avatar_url, 
    role, status, modify_time, fixed_phone, birth_date, gender, pinyin, current_company_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.usercenter.api.model.po.employee.EmployeeExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from employee
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from employee
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.usercenter.api.model.po.employee.EmployeeExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.usercenter.api.model.po.employee.Employee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into employee (id, name, first_name, 
      last_name, email, phone_num, 
      id_number, id_type, avatar_url, 
      role, status, modify_time, 
      fixed_phone, birth_date, gender, 
      pinyin, current_company_id)
    values (#{id,jdbcType=CHAR}, #{name,jdbcType=VARCHAR}, #{first_name,jdbcType=VARCHAR}, 
      #{last_name,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{phone_num,jdbcType=VARCHAR}, 
      #{id_number,jdbcType=VARCHAR}, #{id_type,jdbcType=INTEGER}, #{avatar_url,jdbcType=VARCHAR}, 
      #{role,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{modify_time,jdbcType=TIMESTAMP}, 
      #{fixed_phone,jdbcType=VARCHAR}, #{birth_date,jdbcType=TIMESTAMP}, #{gender,jdbcType=INTEGER}, 
      #{pinyin,jdbcType=VARCHAR}, #{current_company_id,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.usercenter.api.model.po.employee.Employee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into employee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="first_name != null">
        first_name,
      </if>
      <if test="last_name != null">
        last_name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="phone_num != null">
        phone_num,
      </if>
      <if test="id_number != null">
        id_number,
      </if>
      <if test="id_type != null">
        id_type,
      </if>
      <if test="avatar_url != null">
        avatar_url,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="modify_time != null">
        modify_time,
      </if>
      <if test="fixed_phone != null">
        fixed_phone,
      </if>
      <if test="birth_date != null">
        birth_date,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="pinyin != null">
        pinyin,
      </if>
      <if test="current_company_id != null">
        current_company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="first_name != null">
        #{first_name,jdbcType=VARCHAR},
      </if>
      <if test="last_name != null">
        #{last_name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone_num != null">
        #{phone_num,jdbcType=VARCHAR},
      </if>
      <if test="id_number != null">
        #{id_number,jdbcType=VARCHAR},
      </if>
      <if test="id_type != null">
        #{id_type,jdbcType=INTEGER},
      </if>
      <if test="avatar_url != null">
        #{avatar_url,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="modify_time != null">
        #{modify_time,jdbcType=TIMESTAMP},
      </if>
      <if test="fixed_phone != null">
        #{fixed_phone,jdbcType=VARCHAR},
      </if>
      <if test="birth_date != null">
        #{birth_date,jdbcType=TIMESTAMP},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="pinyin != null">
        #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="current_company_id != null">
        #{current_company_id,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.usercenter.api.model.po.employee.EmployeeExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update employee
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=CHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.first_name != null">
        first_name = #{record.first_name,jdbcType=VARCHAR},
      </if>
      <if test="record.last_name != null">
        last_name = #{record.last_name,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.phone_num != null">
        phone_num = #{record.phone_num,jdbcType=VARCHAR},
      </if>
      <if test="record.id_number != null">
        id_number = #{record.id_number,jdbcType=VARCHAR},
      </if>
      <if test="record.id_type != null">
        id_type = #{record.id_type,jdbcType=INTEGER},
      </if>
      <if test="record.avatar_url != null">
        avatar_url = #{record.avatar_url,jdbcType=VARCHAR},
      </if>
      <if test="record.role != null">
        role = #{record.role,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.modify_time != null">
        modify_time = #{record.modify_time,jdbcType=TIMESTAMP},
      </if>
      <if test="record.fixed_phone != null">
        fixed_phone = #{record.fixed_phone,jdbcType=VARCHAR},
      </if>
      <if test="record.birth_date != null">
        birth_date = #{record.birth_date,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=INTEGER},
      </if>
      <if test="record.pinyin != null">
        pinyin = #{record.pinyin,jdbcType=VARCHAR},
      </if>
      <if test="record.current_company_id != null">
        current_company_id = #{record.current_company_id,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update employee
    set id = #{record.id,jdbcType=CHAR},
      name = #{record.name,jdbcType=VARCHAR},
      first_name = #{record.first_name,jdbcType=VARCHAR},
      last_name = #{record.last_name,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      phone_num = #{record.phone_num,jdbcType=VARCHAR},
      id_number = #{record.id_number,jdbcType=VARCHAR},
      id_type = #{record.id_type,jdbcType=INTEGER},
      avatar_url = #{record.avatar_url,jdbcType=VARCHAR},
      role = #{record.role,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      modify_time = #{record.modify_time,jdbcType=TIMESTAMP},
      fixed_phone = #{record.fixed_phone,jdbcType=VARCHAR},
      birth_date = #{record.birth_date,jdbcType=TIMESTAMP},
      gender = #{record.gender,jdbcType=INTEGER},
      pinyin = #{record.pinyin,jdbcType=VARCHAR},
      current_company_id = #{record.current_company_id,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.usercenter.api.model.po.employee.Employee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update employee
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="first_name != null">
        first_name = #{first_name,jdbcType=VARCHAR},
      </if>
      <if test="last_name != null">
        last_name = #{last_name,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone_num != null">
        phone_num = #{phone_num,jdbcType=VARCHAR},
      </if>
      <if test="id_number != null">
        id_number = #{id_number,jdbcType=VARCHAR},
      </if>
      <if test="id_type != null">
        id_type = #{id_type,jdbcType=INTEGER},
      </if>
      <if test="avatar_url != null">
        avatar_url = #{avatar_url,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="modify_time != null">
        modify_time = #{modify_time,jdbcType=TIMESTAMP},
      </if>
      <if test="fixed_phone != null">
        fixed_phone = #{fixed_phone,jdbcType=VARCHAR},
      </if>
      <if test="birth_date != null">
        birth_date = #{birth_date,jdbcType=TIMESTAMP},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="pinyin != null">
        pinyin = #{pinyin,jdbcType=VARCHAR},
      </if>
      <if test="current_company_id != null">
        current_company_id = #{current_company_id,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.usercenter.api.model.po.employee.Employee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update employee
    set name = #{name,jdbcType=VARCHAR},
      first_name = #{first_name,jdbcType=VARCHAR},
      last_name = #{last_name,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      phone_num = #{phone_num,jdbcType=VARCHAR},
      id_number = #{id_number,jdbcType=VARCHAR},
      id_type = #{id_type,jdbcType=INTEGER},
      avatar_url = #{avatar_url,jdbcType=VARCHAR},
      role = #{role,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      modify_time = #{modify_time,jdbcType=TIMESTAMP},
      fixed_phone = #{fixed_phone,jdbcType=VARCHAR},
      birth_date = #{birth_date,jdbcType=TIMESTAMP},
      gender = #{gender,jdbcType=INTEGER},
      pinyin = #{pinyin,jdbcType=VARCHAR},
      current_company_id = #{current_company_id,jdbcType=VARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
</mapper>