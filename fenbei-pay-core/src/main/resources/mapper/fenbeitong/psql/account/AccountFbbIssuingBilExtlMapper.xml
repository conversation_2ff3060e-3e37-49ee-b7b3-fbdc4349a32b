<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbIssuingBillExtMapper">
    <resultMap id="CompanyFbbIssuingBillVoResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.fenbeitong.account.CompanyFbbIssuingBillVo">
        <id column="order_id" jdbcType="VARCHAR" property="orderId" />
        <result column="number" jdbcType="NUMERIC" property="number" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
        <result column="bill_type" jdbcType="INTEGER" property="billType" />
        <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    </resultMap>

  <resultMap id="AccountFbbIssuingBillExtResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="fbb_amount" jdbcType="NUMERIC" property="fbbAmount" />
    <result column="grant_employee_count" jdbcType="BIGINT" property="grantEmployeeCount" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_phone" jdbcType="VARCHAR" property="operatorPhone" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="revoke_amount" jdbcType="VARCHAR" property="revokeAmount" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="state" jdbcType="SMALLINT" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>


    <resultMap id="DetailResultMap" type="com.fenbeitong.fenbeipay.core.model.vo.fenbeitong.account.AccountFbbIssuingBillVo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="order_id" jdbcType="VARCHAR" property="orderId" />
        <result column="account_id" jdbcType="VARCHAR" property="accountId" />
        <result column="number" jdbcType="NUMERIC" property="number" />
        <result column="recall_number" jdbcType="NUMERIC" property="recallNumber" />
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />

    </resultMap>

    <select id="selectBill" parameterType="map" resultMap="CompanyFbbIssuingBillVoResultMap">

        select  bill.id as order_id, bill.create_time , c.id as company_id, c.name as company_name , bill.operator_name, bill.fbb_amount as number,bill.bill_type

        from account_fbb_issuing_bill bill

        left join account acc on acc.id=bill.account_id

        left join company c on c.id=acc.company_id

        where 1=1

        <if test="companyId != null and companyId != '' ">
            and c.id = #{companyId}
        </if>

        <if test="operator_name != null and operator_name != '' ">
            and bill.operator_name = #{operator_name}
        </if>

        <if test="bill_type != null ">
            and bill.bill_type = #{bill_type}
        </if>

        <if test="bill_type == null ">
            and bill.bill_type in (1,2)
        </if>

        <if test="number != null ">
            and bill.fbb_amount = #{number}
        </if>

        <if test="time_start != null">
            and bill.create_time  >= #{time_start, jdbcType=TIMESTAMP}

        </if>

        <if test="time_end != null">
            and bill.create_time  <![CDATA[ <= ]]>  #{time_end, jdbcType=TIMESTAMP}
        </if>

        order by bill.create_time desc

        <if test="pageSize!=null and pageSize >0">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>

    </select>


    <select id="selectBillCount" parameterType="map" resultType="java.lang.Integer">

        select  count(0)

        from account_fbb_issuing_bill bill

        left join account acc on acc.id=bill.account_id

        left join company c on c.id=acc.company_id

        where 1=1

        <if test="companyId != null and companyId != '' ">
            and c.id = #{companyId}
        </if>

        <if test="operator_name != null and operator_name != '' ">
            and bill.operator_name = #{operator_name}
        </if>

        <if test="bill_type != null ">
            and bill.bill_type = #{bill_type}
        </if>

        <if test="bill_type == null ">
            and bill.bill_type in (1,2)
        </if>

        <if test="number != null ">
            and bill.fbb_amount = #{number}
        </if>

        <if test="time_start != null">
            and bill.create_time  >= #{time_start, jdbcType=TIMESTAMP}

        </if>

        <if test="time_end != null">
            and bill.create_time  <![CDATA[ <= ]]>  #{time_end, jdbcType=TIMESTAMP}
        </if>

    </select>



  <select id="selectByParams" parameterType="map" resultMap="AccountFbbIssuingBillExtResultMap">

      select bill.*  from  account_fbb_issuing_bill bill

      left join account acc   on acc.id=bill.account_id where 1=1

      <if test="bill_type != null ">
          and bill.bill_type = #{bill_type}
      </if>

      <if test="operator_name != null and operator_name != '' ">
          and bill.operator_name = #{operator_name}
      </if>

      <if test="status != null ">
          and bill.status = #{status}
      </if>

      <if test="operator_phone != null and operator_phone != '' ">
          and bill.operator_phone = #{operator_phone}
      </if>


      <if test="companyId != null and companyId != '' ">
          and acc.company_id = #{companyId}
      </if>

     <if test="time_start != null">
       and bill.create_time  >= #{time_start, jdbcType=TIMESTAMP}
     </if>

    <if test="time_end != null">
        and bill.create_time  <![CDATA[ <= ]]>  #{time_end, jdbcType=TIMESTAMP}
    </if>

    order by bill.create_time desc

     <if test="pageSize!=null and pageSize >0">
          LIMIT #{pageSize} OFFSET #{offset}
      </if>

  </select>


  <select id="count" parameterType="map" resultType="java.lang.Integer">

      select  count(0)  from  account_fbb_issuing_bill bill

      left join account acc   on acc.id=bill.account_id where 1=1

      <if test="bill_type != null ">
          and bill.bill_type = #{bill_type}
      </if>

      <if test="operator_name != null and operator_name != '' ">
          and bill.operator_name = #{operator_name}
      </if>

      <if test="status != null ">
          and bill.status = #{status}
      </if>

      <if test="operator_phone != null and operator_phone != '' ">
          and bill.operator_phone = #{operator_phone}
      </if>


      <if test="companyId != null and companyId != '' ">
          and acc.company_id = #{companyId}
      </if>

      <if test="time_start != null">
          and bill.create_time  >= #{time_start, jdbcType=TIMESTAMP}
      </if>

      <if test="time_end != null">
          and bill.create_time  <![CDATA[ <= ]]>  #{time_end, jdbcType=TIMESTAMP}
      </if>

  </select>


    <select id="getDeatil" parameterType="map" resultMap="DetailResultMap">

        select bill.id as order_id, bill.status, bill.create_time, bill.fbb_amount as number, bill.operator_id as id,bill.operator_name as name,bill.operator_phone as phone,
              bill.comment, bill.reason,bill.account_id,rel.org_unit_id as dept_id, dept.name as dept_name

        from account_fbb_issuing_bill bill

        left join account acc on bill.account_id= acc.id

        left join org_unit_employee_simple rel on rel.company_id=acc.company_id and rel.employee_id=bill.operator_id

        left join org_unit dept on dept.id=rel.org_unit_id

        where bill.id = #{order_id}

        <if test="company_id != null and company_id != '' ">
            and acc.company_id = #{company_id}
        </if>

    </select>



    <select id="selectDispatchEmployee" parameterType="map" resultMap="DetailResultMap">
        select e.id as id ,ce.name, e.phone_num as phone, pf.amount as number ,org_rel.org_unit_id as dept_id, org.name as dept_name

        from person_account_flow pf

        left join person_account  pa on pa.id=pf.account_id

        left join employee e on e.id=pa.employee_id

        left join org_unit_employee_simple org_rel on org_rel.company_id =pf.company_id and org_rel.employee_id = pa.employee_id

        left join org_unit org on org."id"=org_rel.org_unit_id

        left join company_employee ce on ce.company_id=  org_rel.company_id and  ce.employee_id= org_rel.employee_id

        where 1=1

        <if test="order_id != null and order_id != '' ">
            and pf.order_id = #{order_id}
        </if>

        <if test="business_type != null">
            and pf.business_type = #{business_type}
        </if>

        <if test="company_id != null and company_id != '' ">
            and pf.company_id= #{company_id}
        </if>

        <if test="employeeIdList != null ">
            and pa.employee_id not in
            <foreach collection="employeeIdList" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="pageSize!=null and pageSize >0">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>

    </select>


    <select id="selectDispatchEmployeeCount" parameterType="map" resultType="java.lang.Integer">
        select  count(0)

        from person_account_flow pf

        left join person_account  pa on pa.id=pf.account_id

        left join employee e on e.id=pa.employee_id

        left join org_unit_employee_simple org_rel on org_rel.company_id =pf.company_id and org_rel.employee_id = pa.employee_id

        left join org_unit org on org."id"=org_rel.org_unit_id

        left join company_employee ce on ce.company_id=  org_rel.company_id and  ce.employee_id= org_rel.employee_id

        where 1=1
        <if test="order_id != null and order_id != '' ">
            and  pf.order_id = #{order_id}
        </if>

        <if test="company_id != null and company_id != '' ">
            and pf.company_id = #{company_id}
        </if>

        <if test="business_type != null ">
            and pf.business_type = #{business_type}
        </if>

        <if test="employeeIdList != null">
            and pa.employee_id not in

            <foreach collection="employeeIdList" item="id" index="index"
                     open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

    </select>



    <select id="selectRecallEmployee" parameterType="map" resultMap="DetailResultMap">

        select e.id , ce.name , e.phone_num as phone, pf.grant_amount as number, pf.amount as recall_number ,org_rel.org_unit_id as dept_id,org.name  as dept_name

        from person_account_flow pf

        left join person_account pa on pa.id= pf.account_id

        left join employee e on e.id=pa.employee_id

        left join org_unit_employee_simple org_rel on org_rel.company_id=pf.company_id and org_rel.employee_id=pa.employee_id

        left join org_unit org on org.id=org_rel.org_unit_id

        left join company_employee ce on ce.company_id=  org_rel.company_id and  ce.employee_id= org_rel.employee_id

        where 1=1

        <if test="order_id != null and order_id != '' ">
            and pf.order_id= #{order_id}
        </if>

        <if test="company_id != null and company_id != '' ">
            and pf.company_id= #{company_id}
        </if>

        <if test="business_type != null ">
            and pf.business_type= #{business_type}
        </if>

        <if test="pageSize!=null and pageSize >0">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>


    </select>



    <select id="recallEmployeeCount" parameterType="map" resultType="java.lang.Integer">


        select count(0)

        from person_account_flow pf

        left join person_account pa on pa.id= pf.account_id

        left join employee e on e.id=pa.employee_id

        left join org_unit_employee_simple org_rel on org_rel.company_id=pf.company_id and org_rel.employee_id=pa.employee_id

        left join org_unit org on org.id=org_rel.org_unit_id

        left join company_employee ce on ce.company_id=  org_rel.company_id and  ce.employee_id= org_rel.employee_id

        where 1=1

        <if test="order_id != null and order_id != '' ">
            and pf.order_id= #{order_id}
        </if>

        <if test="company_id != null and company_id != '' ">
            and pf.company_id= #{company_id}
        </if>

        <if test="business_type != null ">
            and pf.business_type= #{business_type}
        </if>

    </select>


      <select id="getRecallDeatil" parameterType="map" resultMap="DetailResultMap">
        select bill.id as order_id,  bill.update_time as create_time, bill.revoke_operator_id as id,bill.revoke_operator_name as name,bill.revoke_operator_phone as phone,

        bill.revoke_reason as comment,bill.revoke_amount as number,rel.org_unit_id as dept_id, dept.name as dept_name

        from account_fbb_issuing_bill bill

        left join account acc on bill.account_id= acc.id

        left join org_unit_employee_simple rel on rel.company_id=acc.company_id and rel.employee_id=bill.operator_id

        left join org_unit dept on dept.id=rel.org_unit_id

        where  bill.status in (3,4,5)  and bill.id = #{order_id}

        <if test="company_id != null and company_id != '' ">
            and acc.company_id = #{company_id}
        </if>

    </select>


     <select id="selectbuySum" parameterType="map" resultType="java.lang.Long">

        select sum(bill.fbb_amount) from account_fbb_issuing_bill  bill where bill.bill_type=1;

     </select>

     <select id="selectgrantSum" parameterType="map" resultType="java.lang.Long">

        select sum(bill.fbb_amount) from account_fbb_issuing_bill  bill where bill.bill_type=2;

     </select>

     <select id="selectrecallSum" parameterType="map" resultType="java.lang.Long">

        select sum(bill.revoke_amount) from account_fbb_issuing_bill  bill where bill.bill_type=2;

     </select>

</mapper>