<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.account.AccountExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.api.model.po.account.Account"
               extends="com.fenbeitong.fenbeipay.core.dao.fenbeitong.account.AccountMapper.BaseResultMap">
    </resultMap>

    <!--修改公司账户余额，扣款，校验余额-->
    <update id="updateAccountPay">
        update account set credit = credit - #{amount} where lock=0 and id = #{accountId} and (credit - #{amount} )>=0
    </update>

    <!--修改公司账户余额，退款，校验余额-->
    <update id="updateAccountPayAdd">
        update account set credit = credit + #{amount} where id = #{accountId}
    </update>

    <!--普通余额变动扣除，不校验余额-->
    <update id="updateCommonAccountPay">
        update account set credit = credit - #{amount}  where id = #{accountId}
    </update>

    <!--普通余额变动退款，不校验余额-->
    <update id="updateCommonAccountPayAdd">
        update account set credit = credit + #{amount}  where id = #{accountId}
    </update>

    <update id="updateAccountBalance" parameterType="map">
        <!--发放 修改账户分贝币余额，总发放币量 解锁-->
        update account set
        fbb_balance = (fbb_balance - #{number}),
        grant_fbb_amount = grant_fbb_amount + #{number},
        lock =0,
        lock_reason = ''
        where company_id = #{company_id} and fbb_balance >= #{number}
    </update>

    <update id="updateAccountBuyBalance" parameterType="map">
        <!--购买 修改分贝币余额，账户资金余额， 总兑换量，解锁-->
        update account set
        fbb_balance = (fbb_balance + #{number}),
        credit = (credit - #{number}),
        exchange_fbb_amount = (exchange_fbb_amount + #{number}),
        lock =0,
        lock_reason = ''
        where id = #{id} and credit >= #{number}
    </update>

    <update id="lockAccount" parameterType="map">
        <!--锁定账户 -->
        update account set lock = #{lock}, lock_reason = #{reason} where lock =0 and company_id = #{company_id}
    </update>

    <!--撤回 修该分贝币余额，发放币数量-->
    <update id="updateAccountRecallBalance" parameterType="map">
        update account set
        fbb_balance = (fbb_balance + #{number}),
        grant_fbb_amount = (grant_fbb_amount - #{number}),
        lock =0,
        lock_reason = ''
        where company_id = #{company_id}  and  grant_fbb_amount >= #{number}
    </update>

    <!--调整账户固定额度-->
    <update id="updateAccountCredit">
        update account set init_credit = #{initCredit},credit = (credit+ #{initCredit}-init_credit)
        where id = #{accountId} and (credit + #{initCredit} - init_credit) >= 0
    </update>

    <!--企业充值操作-->
    <update id="rechargeAccountCredit">
        update account set credit=(credit+ #{credit})
        where id = #{accountId}
    </update>

    <!--获取个人消费信息-->
    <select id="queryTotalConsumeAmount" resultType="java.math.BigDecimal">
        select sum(amount) saveAmount from account_flow
        where employee_id = #{employeeId} and account_id = #{accountId}
        and business_type in (1,3)
    </select>

    <!--修改公司账户余额和冻结，扣款，校验余额-->
    <update id="updateAccountFreeze">
        update account set credit = credit - #{amount},frozen_amount = frozen_amount +  #{amount}  where lock=0 and id = #{accountId} and (credit - #{amount} )>=0
    </update>

    <!--修改公司账户余额和解冻，还款，校验余额-->
    <update id="updateAccountUnfreeze">
        update account set credit = credit + #{amount},frozen_amount = frozen_amount -  #{amount}  where lock=0 and id = #{accountId} and (frozen_amount - #{amount} )>=0
    </update>

    <!--修改公司账户余额，扣款同时解冻金额，校验余额和解冻金额-->
    <update id="updateAccountUnfreezePayMore">
        update account set credit = credit - #{subAmount},frozen_amount = frozen_amount -  #{frozenAmount} where lock=0 and id = #{accountId} and (credit - #{subAmount} )>=0 and (frozen_amount - #{frozenAmount} )>=0
    </update>

    <!--修改公司冻结金额，解冻金额，校验解冻金额-->
    <update id="updateAccountUnfreezePay">
        update account set frozen_amount = frozen_amount - #{frozenAmount} where id = #{accountId} and (frozen_amount - #{frozenAmount} )>=0
    </update>

    <!--修改公司冻结金额，返还企业余额同时解冻金额，校验解冻金额-->
    <update id="updateAccountUnfreezePayLess">
        update account set credit = credit + #{subAmount}, frozen_amount = frozen_amount - #{frozenAmount} where id = #{accountId} and (frozen_amount - #{frozenAmount} )>=0
    </update>

</mapper>