<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.account.AccountMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.api.model.po.account.Account">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="CHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="credit" jdbcType="NUMERIC" property="credit" />
    <result column="init_credit" jdbcType="NUMERIC" property="initCredit" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="period_day" jdbcType="INTEGER" property="periodDay" />
    <result column="repay_day" jdbcType="INTEGER" property="repayDay" />
    <result column="lock" jdbcType="SMALLINT" property="lock" />
    <result column="lock_reason" jdbcType="VARCHAR" property="lockReason" />
    <result column="fbb_balance" jdbcType="NUMERIC" property="fbbBalance" />
    <result column="grant_fbb_amount" jdbcType="NUMERIC" property="grantFbbAmount" />
    <result column="exchange_fbb_amount" jdbcType="NUMERIC" property="exchangeFbbAmount" />
    <result column="frozen_amount" jdbcType="NUMERIC" property="frozenAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, company_name, type, credit, init_credit, balance, period_day, repay_day, 
    lock, lock_reason, fbb_balance, grant_fbb_amount, exchange_fbb_amount, frozen_amount
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit #{limit}
      </if>
      <if test="offset != null">
        limit #{offset}, #{limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.api.model.po.account.Account" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into account (company_id, company_name, type, 
      credit, init_credit, balance, 
      period_day, repay_day, lock, 
      lock_reason, fbb_balance, grant_fbb_amount, 
      exchange_fbb_amount, frozen_amount)
    values (#{companyId,jdbcType=CHAR}, #{companyName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{credit,jdbcType=NUMERIC}, #{initCredit,jdbcType=NUMERIC}, #{balance,jdbcType=NUMERIC}, 
      #{periodDay,jdbcType=INTEGER}, #{repayDay,jdbcType=INTEGER}, #{lock,jdbcType=SMALLINT}, 
      #{lockReason,jdbcType=VARCHAR}, #{fbbBalance,jdbcType=NUMERIC}, #{grantFbbAmount,jdbcType=NUMERIC}, 
      #{exchangeFbbAmount,jdbcType=NUMERIC}, #{frozenAmount,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.api.model.po.account.Account" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="credit != null">
        credit,
      </if>
      <if test="initCredit != null">
        init_credit,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="periodDay != null">
        period_day,
      </if>
      <if test="repayDay != null">
        repay_day,
      </if>
      <if test="lock != null">
        lock,
      </if>
      <if test="lockReason != null">
        lock_reason,
      </if>
      <if test="fbbBalance != null">
        fbb_balance,
      </if>
      <if test="grantFbbAmount != null">
        grant_fbb_amount,
      </if>
      <if test="exchangeFbbAmount != null">
        exchange_fbb_amount,
      </if>
      <if test="frozenAmount != null">
        frozen_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=CHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="credit != null">
        #{credit,jdbcType=NUMERIC},
      </if>
      <if test="initCredit != null">
        #{initCredit,jdbcType=NUMERIC},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=NUMERIC},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="repayDay != null">
        #{repayDay,jdbcType=INTEGER},
      </if>
      <if test="lock != null">
        #{lock,jdbcType=SMALLINT},
      </if>
      <if test="lockReason != null">
        #{lockReason,jdbcType=VARCHAR},
      </if>
      <if test="fbbBalance != null">
        #{fbbBalance,jdbcType=NUMERIC},
      </if>
      <if test="grantFbbAmount != null">
        #{grantFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="exchangeFbbAmount != null">
        #{exchangeFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="frozenAmount != null">
        #{frozenAmount,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=CHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.credit != null">
        credit = #{record.credit,jdbcType=NUMERIC},
      </if>
      <if test="record.initCredit != null">
        init_credit = #{record.initCredit,jdbcType=NUMERIC},
      </if>
      <if test="record.balance != null">
        balance = #{record.balance,jdbcType=NUMERIC},
      </if>
      <if test="record.periodDay != null">
        period_day = #{record.periodDay,jdbcType=INTEGER},
      </if>
      <if test="record.repayDay != null">
        repay_day = #{record.repayDay,jdbcType=INTEGER},
      </if>
      <if test="record.lock != null">
        lock = #{record.lock,jdbcType=SMALLINT},
      </if>
      <if test="record.lockReason != null">
        lock_reason = #{record.lockReason,jdbcType=VARCHAR},
      </if>
      <if test="record.fbbBalance != null">
        fbb_balance = #{record.fbbBalance,jdbcType=NUMERIC},
      </if>
      <if test="record.grantFbbAmount != null">
        grant_fbb_amount = #{record.grantFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.exchangeFbbAmount != null">
        exchange_fbb_amount = #{record.exchangeFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.frozenAmount != null">
        frozen_amount = #{record.frozenAmount,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account
    set id = #{record.id,jdbcType=INTEGER},
      company_id = #{record.companyId,jdbcType=CHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      credit = #{record.credit,jdbcType=NUMERIC},
      init_credit = #{record.initCredit,jdbcType=NUMERIC},
      balance = #{record.balance,jdbcType=NUMERIC},
      period_day = #{record.periodDay,jdbcType=INTEGER},
      repay_day = #{record.repayDay,jdbcType=INTEGER},
      lock = #{record.lock,jdbcType=SMALLINT},
      lock_reason = #{record.lockReason,jdbcType=VARCHAR},
      fbb_balance = #{record.fbbBalance,jdbcType=NUMERIC},
      grant_fbb_amount = #{record.grantFbbAmount,jdbcType=NUMERIC},
      exchange_fbb_amount = #{record.exchangeFbbAmount,jdbcType=NUMERIC},
      frozen_amount = #{record.frozenAmount,jdbcType=NUMERIC}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.api.model.po.account.Account">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=CHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="credit != null">
        credit = #{credit,jdbcType=NUMERIC},
      </if>
      <if test="initCredit != null">
        init_credit = #{initCredit,jdbcType=NUMERIC},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=NUMERIC},
      </if>
      <if test="periodDay != null">
        period_day = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="repayDay != null">
        repay_day = #{repayDay,jdbcType=INTEGER},
      </if>
      <if test="lock != null">
        lock = #{lock,jdbcType=SMALLINT},
      </if>
      <if test="lockReason != null">
        lock_reason = #{lockReason,jdbcType=VARCHAR},
      </if>
      <if test="fbbBalance != null">
        fbb_balance = #{fbbBalance,jdbcType=NUMERIC},
      </if>
      <if test="grantFbbAmount != null">
        grant_fbb_amount = #{grantFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="exchangeFbbAmount != null">
        exchange_fbb_amount = #{exchangeFbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="frozenAmount != null">
        frozen_amount = #{frozenAmount,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.api.model.po.account.Account">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account
    set company_id = #{companyId,jdbcType=CHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      credit = #{credit,jdbcType=NUMERIC},
      init_credit = #{initCredit,jdbcType=NUMERIC},
      balance = #{balance,jdbcType=NUMERIC},
      period_day = #{periodDay,jdbcType=INTEGER},
      repay_day = #{repayDay,jdbcType=INTEGER},
      lock = #{lock,jdbcType=SMALLINT},
      lock_reason = #{lockReason,jdbcType=VARCHAR},
      fbb_balance = #{fbbBalance,jdbcType=NUMERIC},
      grant_fbb_amount = #{grantFbbAmount,jdbcType=NUMERIC},
      exchange_fbb_amount = #{exchangeFbbAmount,jdbcType=NUMERIC},
      frozen_amount = #{frozenAmount,jdbcType=NUMERIC}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>