<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbIssuingBillMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="fbb_amount" jdbcType="NUMERIC" property="fbbAmount" />
    <result column="grant_employee_count" jdbcType="BIGINT" property="grantEmployeeCount" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_phone" jdbcType="VARCHAR" property="operatorPhone" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="revoke_amount" jdbcType="NUMERIC" property="revokeAmount" />
    <result column="revoke_operator_id" jdbcType="VARCHAR" property="revokeOperatorId" />
    <result column="revoke_operator_name" jdbcType="VARCHAR" property="revokeOperatorName" />
    <result column="revoke_operator_phone" jdbcType="VARCHAR" property="revokeOperatorPhone" />
    <result column="revoke_reason" jdbcType="VARCHAR" property="revokeReason" />
    <result column="state" jdbcType="SMALLINT" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, account_id, amount, fbb_amount, grant_employee_count, operator_id, operator_name, 
    operator_phone, bill_type, status, reason, comment, revoke_amount, revoke_operator_id, 
    revoke_operator_name, revoke_operator_phone, revoke_reason, state, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBillExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from account_fbb_issuing_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from account_fbb_issuing_bill
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from account_fbb_issuing_bill
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBillExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from account_fbb_issuing_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into account_fbb_issuing_bill (id, account_id, amount, 
      fbb_amount, grant_employee_count, operator_id, 
      operator_name, operator_phone, bill_type, 
      status, reason, comment, 
      revoke_amount, revoke_operator_id, revoke_operator_name, 
      revoke_operator_phone, revoke_reason, state, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{amount,jdbcType=NUMERIC}, 
      #{fbbAmount,jdbcType=NUMERIC}, #{grantEmployeeCount,jdbcType=BIGINT}, #{operatorId,jdbcType=VARCHAR}, 
      #{operatorName,jdbcType=VARCHAR}, #{operatorPhone,jdbcType=VARCHAR}, #{billType,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{revokeAmount,jdbcType=BIGINT}, #{revokeOperatorId,jdbcType=VARCHAR}, #{revokeOperatorName,jdbcType=VARCHAR}, 
      #{revokeOperatorPhone,jdbcType=VARCHAR}, #{revokeReason,jdbcType=VARCHAR}, #{state,jdbcType=SMALLINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into account_fbb_issuing_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="fbbAmount != null">
        fbb_amount,
      </if>
      <if test="grantEmployeeCount != null">
        grant_employee_count,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorPhone != null">
        operator_phone,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="revokeAmount != null">
        revoke_amount,
      </if>
      <if test="revokeOperatorId != null">
        revoke_operator_id,
      </if>
      <if test="revokeOperatorName != null">
        revoke_operator_name,
      </if>
      <if test="revokeOperatorPhone != null">
        revoke_operator_phone,
      </if>
      <if test="revokeReason != null">
        revoke_reason,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="fbbAmount != null">
        #{fbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="grantEmployeeCount != null">
        #{grantEmployeeCount,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorPhone != null">
        #{operatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="revokeAmount != null">
        #{revokeAmount,jdbcType=BIGINT},
      </if>
      <if test="revokeOperatorId != null">
        #{revokeOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="revokeOperatorName != null">
        #{revokeOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="revokeOperatorPhone != null">
        #{revokeOperatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBillExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from account_fbb_issuing_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account_fbb_issuing_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=NUMERIC},
      </if>
      <if test="record.fbbAmount != null">
        fbb_amount = #{record.fbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.grantEmployeeCount != null">
        grant_employee_count = #{record.grantEmployeeCount,jdbcType=BIGINT},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorPhone != null">
        operator_phone = #{record.operatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        comment = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.revokeAmount != null">
        revoke_amount = #{record.revokeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.revokeOperatorId != null">
        revoke_operator_id = #{record.revokeOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.revokeOperatorName != null">
        revoke_operator_name = #{record.revokeOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.revokeOperatorPhone != null">
        revoke_operator_phone = #{record.revokeOperatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.revokeReason != null">
        revoke_reason = #{record.revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account_fbb_issuing_bill
    set id = #{record.id,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      amount = #{record.amount,jdbcType=NUMERIC},
      fbb_amount = #{record.fbbAmount,jdbcType=NUMERIC},
      grant_employee_count = #{record.grantEmployeeCount,jdbcType=BIGINT},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      operator_phone = #{record.operatorPhone,jdbcType=VARCHAR},
      bill_type = #{record.billType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      reason = #{record.reason,jdbcType=VARCHAR},
      comment = #{record.comment,jdbcType=VARCHAR},
      revoke_amount = #{record.revokeAmount,jdbcType=BIGINT},
      revoke_operator_id = #{record.revokeOperatorId,jdbcType=VARCHAR},
      revoke_operator_name = #{record.revokeOperatorName,jdbcType=VARCHAR},
      revoke_operator_phone = #{record.revokeOperatorPhone,jdbcType=VARCHAR},
      revoke_reason = #{record.revokeReason,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account_fbb_issuing_bill
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="fbbAmount != null">
        fbb_amount = #{fbbAmount,jdbcType=NUMERIC},
      </if>
      <if test="grantEmployeeCount != null">
        grant_employee_count = #{grantEmployeeCount,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorPhone != null">
        operator_phone = #{operatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="revokeAmount != null">
        revoke_amount = #{revokeAmount,jdbcType=BIGINT},
      </if>
      <if test="revokeOperatorId != null">
        revoke_operator_id = #{revokeOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="revokeOperatorName != null">
        revoke_operator_name = #{revokeOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="revokeOperatorPhone != null">
        revoke_operator_phone = #{revokeOperatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="revokeReason != null">
        revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update account_fbb_issuing_bill
    set account_id = #{accountId,jdbcType=INTEGER},
      amount = #{amount,jdbcType=NUMERIC},
      fbb_amount = #{fbbAmount,jdbcType=NUMERIC},
      grant_employee_count = #{grantEmployeeCount,jdbcType=BIGINT},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_phone = #{operatorPhone,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      comment = #{comment,jdbcType=VARCHAR},
      revoke_amount = #{revokeAmount,jdbcType=BIGINT},
      revoke_operator_id = #{revokeOperatorId,jdbcType=VARCHAR},
      revoke_operator_name = #{revokeOperatorName,jdbcType=VARCHAR},
      revoke_operator_phone = #{revokeOperatorPhone,jdbcType=VARCHAR},
      revoke_reason = #{revokeReason,jdbcType=VARCHAR},
      state = #{state,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>