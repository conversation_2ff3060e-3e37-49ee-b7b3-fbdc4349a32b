<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbFlowExtMapper">
  <resultMap id="AccountFbbFlowExtResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="grant_amount" jdbcType="NUMERIC" property="grantAmount" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="person_account_id" jdbcType="VARCHAR" property="personAccountId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <!-- 批量插入生成企业账户分贝币发放流水 -->
        <insert id ="batchInsert" parameterType="java.util.List" >

          insert into account_fbb_flow (id, create_time, account_id, amount,grant_amount,business_type,person_account_id,order_id,reason,comment,operator_id,operator_name,balance)
          values
               <foreach collection ="accountFbbFlowList" item="accountFbbFlow" index= "index" separator =",">
                   (
                   #{accountFbbFlow.id},
                   #{accountFbbFlow.createTime},
                   #{accountFbbFlow.accountId},
                   #{accountFbbFlow.amount},
                   #{accountFbbFlow.grantAmount},
                   #{accountFbbFlow.businessType},
                   #{accountFbbFlow.personAccountId},
                   #{accountFbbFlow.orderId},
                   #{accountFbbFlow.reason},
                   #{accountFbbFlow.comment},
                   #{accountFbbFlow.operatorId},
                   #{accountFbbFlow.operatorName},
                   #{accountFbbFlow.balance}

                 )
               </foreach >
        </insert >
</mapper>