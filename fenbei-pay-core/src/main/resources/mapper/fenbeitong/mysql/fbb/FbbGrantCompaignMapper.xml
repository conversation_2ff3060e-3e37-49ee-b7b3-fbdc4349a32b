<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantCompaignMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantCompaign">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="campaign_code" jdbcType="VARCHAR" property="campaignCode" />
    <result column="campaign_name" jdbcType="VARCHAR" property="campaignName" />
    <result column="budget_amount" jdbcType="DECIMAL" property="budgetAmount" />
    <result column="grant_amount" jdbcType="DECIMAL" property="grantAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <update id="updateGrantAmount">
  	update fbb_grant_compaign set grant_amount = grant_amount + #{amount} where campaign_code = #{campaignCode}
  </update>
  
</mapper>