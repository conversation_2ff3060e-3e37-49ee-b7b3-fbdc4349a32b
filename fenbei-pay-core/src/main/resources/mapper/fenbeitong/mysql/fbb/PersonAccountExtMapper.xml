<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountExtMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
        <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="balance" jdbcType="DECIMAL" property="balance" />
        <result column="consume_fbb_amount" jdbcType="DECIMAL" property="consumeFbbAmount" />
        <result column="achieve_fbb_amount" jdbcType="DECIMAL" property="achieveFbbAmount" />
        <result column="lock_account" jdbcType="INTEGER" property="lockAccount" />
        <result column="lock_reason" jdbcType="VARCHAR" property="lockReason" />
    </resultMap>
    <!--修改个人账户余额,扣款-->
    <update id="updatePersonAccountPay">
        update person_account set balance= balance - #{amount} ,update_time = now() where id= #{accountId} and (balance - #{amount})>=0
    </update>

    <!--部分撤回 修改个人账户余额-->
    <update id="updateRecallAmount" >

        update person_account set balance = balance - #{amount} ,

        achieve_fbb_amount = (achieve_fbb_amount - #{amount}),

        update_time = now(), lock_account =0

        where  id= #{accountId} and (balance - #{amount})>=0
    </update>

    <!--修改个人账户余额,退款-->
    <update id="updatePersonAccountPayAdd">
        update person_account set balance= balance + #{amount} ,update_time = now() where id= #{accountId}
    </update>

    <!--企业发币 修改个人账户余额-->
    <update id="updateAccount" parameterType="map">

        update person_account set

        balance = balance + #{number},

        achieve_fbb_amount = achieve_fbb_amount + #{number},

        update_time = now()

        where employee_id in

        <foreach collection="employeeIdList" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>

    </update>

    <!--企业发币 修改个人账户余额-->
    <update id="updateAccountById" parameterType="map">

        update person_account set

        balance = balance + #{number},

        achieve_fbb_amount = achieve_fbb_amount + #{number},

        update_time = now()

        where id= #{accountId}

    </update>



    <!--企业发币 校验员工/账户信息-->
    <select id="checkAccountInfo" parameterType="map" resultMap="BaseResultMap">

        select pa.*

        from person_account pa, employee e

        where e.id=pa.employee_id and e.status !=4 and e.id in

        <foreach collection="employeeIdList" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>


    </select>

    <!--锁定员工账户-->
    <update id="lockAccount" parameterType="map">
        update person_account set

        update_time = now(),

        lock_account = #{lock},

        lock_reason = #{reason}

        where lock_account=0

        and employee_id in

        <foreach collection="employeeIdList" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!--全额撤回 修改个人账户余额-->
    <update id="fullDeducting" parameterType="map">
        update person_account set balance = (balance - #{number}),

        achieve_fbb_amount = (achieve_fbb_amount - #{number}),

        update_time = now(), lock_account =0, lock_reason = ''

        where balance >= #{number} and achieve_fbb_amount >= #{number} and employee_id in

        <foreach collection="employeeIdList" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>

    </update>

    <!--部分撤回 修改个人账户余额-->
    <update id="partDeducting" parameterType="map">

        update person_account set balance = (balance-balance) ,

        achieve_fbb_amount = (achieve_fbb_amount - balance),

        update_time = now(), lock_account =0, lock_reason = ''

        where balance >=0 and balance <![CDATA[ <= ]]> #{number} and employee_id in

        <foreach collection="employeeIdList" item="id" index="index"
                 open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!--修改用户总消费分贝币额-->
    <update id="updateConsumeFbbAmount">
        update person_account set consume_fbb_amount= consume_fbb_amount + #{amount} ,update_time = now() where id= #{accountId}
    </update>

    <!--修改用户总消费分贝币额-->
    <update id="updateConsumeFbbAmountDeduct">
        update person_account set consume_fbb_amount= consume_fbb_amount - #{amount} ,update_time = now() where id = #{accountId} and  (consume_fbb_amount - #{amount})>=0
    </update>

</mapper>