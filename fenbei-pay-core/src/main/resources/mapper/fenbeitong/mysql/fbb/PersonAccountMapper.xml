<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="consume_fbb_amount" jdbcType="DECIMAL" property="consumeFbbAmount" />
    <result column="achieve_fbb_amount" jdbcType="DECIMAL" property="achieveFbbAmount" />
    <result column="lock_account" jdbcType="INTEGER" property="lockAccount" />
    <result column="lock_reason" jdbcType="VARCHAR" property="lockReason" />
    <result column="lock_reason" jdbcType="VARCHAR" property="lockReason" />
    <result column="upgrade_flag" jdbcType="TINYINT" property="upgradeFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, employee_id, employee_name, employee_phone, status, create_time, update_time, 
    balance, consume_fbb_amount, achieve_fbb_amount, lock_account, lock_reason,upgrade_flag
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_account
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_account
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_account (id, employee_id, employee_name, 
      employee_phone, status, create_time, 
      update_time, balance, consume_fbb_amount, 
      achieve_fbb_amount, lock_account, lock_reason,upgrade_flag
      )
    values (#{id,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR}, 
      #{employeePhone,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{balance,jdbcType=DECIMAL}, #{consumeFbbAmount,jdbcType=DECIMAL}, 
      #{achieveFbbAmount,jdbcType=DECIMAL}, #{lockAccount,jdbcType=INTEGER}, #{lockReason,jdbcType=VARCHAR},
      #{upgradeFlag,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="consumeFbbAmount != null">
        consume_fbb_amount,
      </if>
      <if test="achieveFbbAmount != null">
        achieve_fbb_amount,
      </if>
      <if test="lockAccount != null">
        lock_account,
      </if>
      <if test="lockReason != null">
        lock_reason,
      </if>
      <if test="upgradeFlag != null">
        upgrade_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="consumeFbbAmount != null">
        #{consumeFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="achieveFbbAmount != null">
        #{achieveFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="lockAccount != null">
        #{lockAccount,jdbcType=INTEGER},
      </if>
      <if test="lockReason != null">
        #{lockReason,jdbcType=VARCHAR},
      </if>
      <if test="upgradeFlag != null">
        #{upgradeFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.balance != null">
        balance = #{record.balance,jdbcType=DECIMAL},
      </if>
      <if test="record.consumeFbbAmount != null">
        consume_fbb_amount = #{record.consumeFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.achieveFbbAmount != null">
        achieve_fbb_amount = #{record.achieveFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.lockAccount != null">
        lock_account = #{record.lockAccount,jdbcType=INTEGER},
      </if>
      <if test="record.lockReason != null">
        lock_reason = #{record.lockReason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account
    set id = #{record.id,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      balance = #{record.balance,jdbcType=DECIMAL},
      consume_fbb_amount = #{record.consumeFbbAmount,jdbcType=DECIMAL},
      achieve_fbb_amount = #{record.achieveFbbAmount,jdbcType=DECIMAL},
      lock_account = #{record.lockAccount,jdbcType=INTEGER},
      lock_reason = #{record.lockReason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="consumeFbbAmount != null">
        consume_fbb_amount = #{consumeFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="achieveFbbAmount != null">
        achieve_fbb_amount = #{achieveFbbAmount,jdbcType=DECIMAL},
      </if>
      <if test="lockAccount != null">
        lock_account = #{lockAccount,jdbcType=INTEGER},
      </if>
      <if test="lockReason != null">
        lock_reason = #{lockReason,jdbcType=VARCHAR},
      </if>
      <if test="upgradeFlag != null">
        upgrade_flag = #{upgradeFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account
    set employee_id = #{employeeId,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      balance = #{balance,jdbcType=DECIMAL},
      consume_fbb_amount = #{consumeFbbAmount,jdbcType=DECIMAL},
      achieve_fbb_amount = #{achieveFbbAmount,jdbcType=DECIMAL},
      lock_account = #{lockAccount,jdbcType=INTEGER},
      lock_reason = #{lockReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>