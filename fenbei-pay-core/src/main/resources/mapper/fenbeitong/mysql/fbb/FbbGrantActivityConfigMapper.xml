<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantActivityConfigMapper">
    <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfig">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_no" jdbcType="VARCHAR" property="activityNo"/>
        <result column="daily_limit" jdbcType="DECIMAL" property="dailyLimit"/>
        <result column="total_limit" jdbcType="DECIMAL" property="totalLimit"/>
        <result column="enable_status" jdbcType="TINYINT" property="enableStatus"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, activity_no, daily_limit, total_limit, enable_status
    </sql>
    <select id="selectByExample"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfigExample"
            resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from fbb_grant_activity_config
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <choose>
                <when test="offset == null">
                    limit ${limit}
                </when>
                <otherwise>
                    limit ${offset}, ${limit}
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from fbb_grant_activity_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from fbb_grant_activity_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfigExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from fbb_grant_activity_config
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfig"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into fbb_grant_activity_config (activity_no, daily_limit, total_limit,
        enable_status)
        values (#{activityNo,jdbcType=VARCHAR}, #{dailyLimit,jdbcType=DECIMAL}, #{totalLimit,jdbcType=DECIMAL},
        #{enableStatus,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfig"
            useGeneratedKeys="true">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        insert into fbb_grant_activity_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityNo != null">
                activity_no,
            </if>
            <if test="dailyLimit != null">
                daily_limit,
            </if>
            <if test="totalLimit != null">
                total_limit,
            </if>
            <if test="enableStatus != null">
                enable_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityNo != null">
                #{activityNo,jdbcType=VARCHAR},
            </if>
            <if test="dailyLimit != null">
                #{dailyLimit,jdbcType=DECIMAL},
            </if>
            <if test="totalLimit != null">
                #{totalLimit,jdbcType=DECIMAL},
            </if>
            <if test="enableStatus != null">
                #{enableStatus,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfigExample"
            resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select count(*) from fbb_grant_activity_config
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update fbb_grant_activity_config
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.activityNo != null">
                activity_no = #{record.activityNo,jdbcType=VARCHAR},
            </if>
            <if test="record.dailyLimit != null">
                daily_limit = #{record.dailyLimit,jdbcType=DECIMAL},
            </if>
            <if test="record.totalLimit != null">
                total_limit = #{record.totalLimit,jdbcType=DECIMAL},
            </if>
            <if test="record.enableStatus != null">
                enable_status = #{record.enableStatus,jdbcType=TINYINT},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update fbb_grant_activity_config
        set id = #{record.id,jdbcType=BIGINT},
        activity_no = #{record.activityNo,jdbcType=VARCHAR},
        daily_limit = #{record.dailyLimit,jdbcType=DECIMAL},
        total_limit = #{record.totalLimit,jdbcType=DECIMAL},
        enable_status = #{record.enableStatus,jdbcType=TINYINT}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfig">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update fbb_grant_activity_config
        <set>
            <if test="activityNo != null">
                activity_no = #{activityNo,jdbcType=VARCHAR},
            </if>
            <if test="dailyLimit != null">
                daily_limit = #{dailyLimit,jdbcType=DECIMAL},
            </if>
            <if test="totalLimit != null">
                total_limit = #{totalLimit,jdbcType=DECIMAL},
            </if>
            <if test="enableStatus != null">
                enable_status = #{enableStatus,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantActivityConfig">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update fbb_grant_activity_config
        set activity_no = #{activityNo,jdbcType=VARCHAR},
        daily_limit = #{dailyLimit,jdbcType=DECIMAL},
        total_limit = #{totalLimit,jdbcType=DECIMAL},
        enable_status = #{enableStatus,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>