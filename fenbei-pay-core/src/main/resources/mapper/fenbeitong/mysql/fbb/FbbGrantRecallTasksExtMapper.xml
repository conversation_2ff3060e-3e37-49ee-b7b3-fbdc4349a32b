<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantRecallTasksExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="activity_description" jdbcType="VARCHAR" property="activityDescription" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_type" jdbcType="SMALLINT" property="projectType" />
    <result column="task_type" jdbcType="SMALLINT" property="taskType" />
    <result column="task_status" jdbcType="SMALLINT" property="taskStatus" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="success_amount" jdbcType="DECIMAL" property="successAmount" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="recall_task_id" jdbcType="VARCHAR" property="recallTaskId" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_department_id" jdbcType="VARCHAR" property="applyUserDepartmentId" />
    <result column="apply_user_department_name" jdbcType="VARCHAR" property="applyUserDepartmentName" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_user_company_id" jdbcType="VARCHAR" property="applyUserCompanyId" />
    <result column="apply_user_company_name" jdbcType="VARCHAR" property="applyUserCompanyName" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
  </resultMap>
  <select id="totalRecallSuccessAmountByStatusAndTasksId" parameterType="map" resultType="java.lang.Double">

    select sum(success_amount) AS recallSuccessAmount from fbb_grant_recall_tasks where  recall_task_id =#{recallTaskId}  and
    task_status IN
    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
      #{status}
    </foreach>

  </select>
</mapper>