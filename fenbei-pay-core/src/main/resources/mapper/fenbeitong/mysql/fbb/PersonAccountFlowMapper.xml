<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountFlowMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="grant_amount" jdbcType="DECIMAL" property="grantAmount" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="common_json" jdbcType="VARCHAR" property="commonJson" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operator_type" jdbcType="SMALLINT" property="operatorType" />
    <result column="customer_service_id" jdbcType="VARCHAR" property="customerServiceId" />
    <result column="customer_service_name" jdbcType="VARCHAR" property="customerServiceName" />
    <result column="activity_no" jdbcType="VARCHAR" property="activityNo"/>
    <result column="grant_record_id" jdbcType="VARCHAR" property="grantRecordId"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, account_id, amount, grant_amount, account_type, order_type, business_type, order_id,
    company_id, balance, operator_id, operator_name, common_json, reason, create_time,
    operator_type, customer_service_id, customer_service_name, activity_no,grant_record_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlowExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from person_account_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset == null">
        limit ${limit}
      </if>
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from person_account_flow
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_account_flow
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlowExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from person_account_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_account_flow (id, account_id, amount,
    grant_amount, account_type, order_type,
    business_type, order_id, company_id,
    balance, operator_id, operator_name,
    common_json, reason, create_time,
    operator_type, customer_service_id, customer_service_name,activity_no,grant_record_id
    )
    values (#{id,jdbcType=VARCHAR}, #{accountId,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL},
    #{grantAmount,jdbcType=DECIMAL}, #{accountType,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER},
    #{businessType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR},
    #{balance,jdbcType=DECIMAL}, #{operatorId,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR},
    #{commonJson,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{operatorType,jdbcType=SMALLINT}, #{customerServiceId,jdbcType=VARCHAR}, #{customerServiceName,jdbcType=VARCHAR}
    , #{activityNo,jdbcType=VARCHAR} ,#{grantRecordId,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into person_account_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="grantAmount != null">
        grant_amount,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="commonJson != null">
        common_json,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="customerServiceId != null">
        customer_service_id,
      </if>
      <if test="customerServiceName != null">
        customer_service_name,
      </if>
      <if test="activityNo != null">
        activity_no,
      </if>
      <if test="grantRecordId !=null" >
        grant_record_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="grantAmount != null">
        #{grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="commonJson != null">
        #{commonJson,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=SMALLINT},
      </if>
      <if test="customerServiceId != null">
        #{customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerServiceName != null">
        #{customerServiceName,jdbcType=VARCHAR},
      </if>
      <if test="activityNo != null">
        #{activityNo,jdbcType=VARCHAR},
      </if>
      <if test="grantRecordId !=null" >
        #{grantRecordId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlowExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from person_account_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.grantAmount != null">
        grant_amount = #{record.grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.balance != null">
        balance = #{record.balance,jdbcType=DECIMAL},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.commonJson != null">
        common_json = #{record.commonJson,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=SMALLINT},
      </if>
      <if test="record.customerServiceId != null">
        customer_service_id = #{record.customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerServiceName != null">
        customer_service_name = #{record.customerServiceName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account_flow
    set id = #{record.id,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DECIMAL},
      grant_amount = #{record.grantAmount,jdbcType=DECIMAL},
      account_type = #{record.accountType,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      balance = #{record.balance,jdbcType=DECIMAL},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      common_json = #{record.commonJson,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      operator_type = #{record.operatorType,jdbcType=SMALLINT},
      customer_service_id = #{record.customerServiceId,jdbcType=VARCHAR},
      customer_service_name = #{record.customerServiceName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account_flow
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="grantAmount != null">
        grant_amount = #{grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="commonJson != null">
        common_json = #{commonJson,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=SMALLINT},
      </if>
      <if test="customerServiceId != null">
        customer_service_id = #{customerServiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerServiceName != null">
        customer_service_name = #{customerServiceName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update person_account_flow
    set account_id = #{accountId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      grant_amount = #{grantAmount,jdbcType=DECIMAL},
      account_type = #{accountType,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      balance = #{balance,jdbcType=DECIMAL},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      common_json = #{commonJson,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      operator_type = #{operatorType,jdbcType=SMALLINT},
      customer_service_id = #{customerServiceId,jdbcType=VARCHAR},
      customer_service_name = #{customerServiceName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>