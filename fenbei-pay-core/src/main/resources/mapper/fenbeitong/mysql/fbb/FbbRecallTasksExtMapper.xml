<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbRecallTasksExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="recall_amount" jdbcType="DECIMAL" property="recallAmount" />
    <result column="recall_success_amount" jdbcType="DECIMAL" property="recallSuccessAmount" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="grant_id" jdbcType="VARCHAR" property="grantId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_platform" jdbcType="VARCHAR" property="companyPlatform" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
  </resultMap>

  <insert id="batchInsert" parameterType="java.util.List">

    insert into fbb_recall_tasks (id, tasks_id, employee_id,
    recall_amount, recall_success_amount, status,
    reason, create_time, complete_time,
    update_time, grant_id, employee_name,
    company_name, company_platform, phone_num
    )
    values
    <foreach collection="recallTasksList" item="recallTasksList" index="index" separator=",">
      (
      #{recallTasksList.id},
      #{recallTasksList.tasksId},
      #{recallTasksList.employeeId},
      #{recallTasksList.recallAmount},
      #{recallTasksList.recallSuccessAmount},
      #{recallTasksList.status},
      #{recallTasksList.reason},
      now(),
      #{recallTasksList.completeTime},
      now(),
      #{recallTasksList.grantId},
      #{recallTasksList.employeeName},
      #{recallTasksList.companyName},
      #{recallTasksList.companyPlatform},
      #{recallTasksList.phoneNum}
      )
    </foreach>
  </insert>


  <select id="recallAmountByStatusAndTasks" parameterType="map"
          resultType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasksExt">

    select count(*) AS successCount, sum(recall_amount) AS perAmount ,sum(recall_success_amount) AS successAmount from fbb_recall_tasks where tasks_id
    =#{tasksId}
    and status in
    <foreach item="recallTasksList" index="index" collection="recallTasksList" open="(" separator="," close=")">
      #{recallTasksList}
    </foreach>

  </select>

</mapper>