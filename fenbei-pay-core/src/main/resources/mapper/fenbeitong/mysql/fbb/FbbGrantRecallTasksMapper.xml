<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantRecallTasksMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="activity_description" jdbcType="VARCHAR" property="activityDescription" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_type" jdbcType="SMALLINT" property="projectType" />
    <result column="task_type" jdbcType="SMALLINT" property="taskType" />
    <result column="task_status" jdbcType="SMALLINT" property="taskStatus" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="success_amount" jdbcType="DECIMAL" property="successAmount" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="success_count" jdbcType="INTEGER" property="successCount" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="recall_task_id" jdbcType="VARCHAR" property="recallTaskId" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_department_id" jdbcType="VARCHAR" property="applyUserDepartmentId" />
    <result column="apply_user_department_name" jdbcType="VARCHAR" property="applyUserDepartmentName" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_user_company_id" jdbcType="VARCHAR" property="applyUserCompanyId" />
    <result column="apply_user_company_name" jdbcType="VARCHAR" property="applyUserCompanyName" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="dd_apply_id" jdbcType="VARCHAR" property="ddApplyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    tasks_id, activity_id, title, activity_description, project_id, project_type, task_type, 
    task_status, total_amount, success_amount, total_count, remark, operation_user_name, 
    operation_user_phone, project_name, create_time, dispatch_time, complete_time, update_time, 
    success_count, operation_user_id, recall_task_id, apply_user_id, apply_user_name, 
    apply_user_department_id, apply_user_department_name, apply_reason, apply_user_company_id, 
    apply_user_company_name, apply_time, dd_apply_id
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasksExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fbb_grant_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from fbb_grant_recall_tasks
    where tasks_id = #{tasksId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_grant_recall_tasks
    where tasks_id = #{tasksId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasksExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_grant_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_grant_recall_tasks (tasks_id, activity_id, title, 
      activity_description, project_id, project_type, 
      task_type, task_status, total_amount, 
      success_amount, total_count, remark, 
      operation_user_name, operation_user_phone, 
      project_name, create_time, dispatch_time, 
      complete_time, update_time, success_count, 
      operation_user_id, recall_task_id, apply_user_id, 
      apply_user_name, apply_user_department_id, apply_user_department_name, 
      apply_reason, apply_user_company_id, apply_user_company_name, 
      apply_time, dd_apply_id)
    values (#{tasksId,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{activityDescription,jdbcType=VARCHAR}, #{projectId,jdbcType=VARCHAR}, #{projectType,jdbcType=SMALLINT}, 
      #{taskType,jdbcType=SMALLINT}, #{taskStatus,jdbcType=SMALLINT}, #{totalAmount,jdbcType=DECIMAL}, 
      #{successAmount,jdbcType=DECIMAL}, #{totalCount,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, 
      #{operationUserName,jdbcType=VARCHAR}, #{operationUserPhone,jdbcType=VARCHAR}, 
      #{projectName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{dispatchTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{successCount,jdbcType=INTEGER}, 
      #{operationUserId,jdbcType=VARCHAR}, #{recallTaskId,jdbcType=VARCHAR}, #{applyUserId,jdbcType=VARCHAR}, 
      #{applyUserName,jdbcType=VARCHAR}, #{applyUserDepartmentId,jdbcType=VARCHAR}, #{applyUserDepartmentName,jdbcType=VARCHAR}, 
      #{applyReason,jdbcType=VARCHAR}, #{applyUserCompanyId,jdbcType=VARCHAR}, #{applyUserCompanyName,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{ddApplyId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_grant_recall_tasks
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tasksId != null">
        tasks_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="activityDescription != null">
        activity_description,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectType != null">
        project_type,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="successAmount != null">
        success_amount,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="operationUserName != null">
        operation_user_name,
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="dispatchTime != null">
        dispatch_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="successCount != null">
        success_count,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="recallTaskId != null">
        recall_task_id,
      </if>
      <if test="applyUserId != null">
        apply_user_id,
      </if>
      <if test="applyUserName != null">
        apply_user_name,
      </if>
      <if test="applyUserDepartmentId != null">
        apply_user_department_id,
      </if>
      <if test="applyUserDepartmentName != null">
        apply_user_department_name,
      </if>
      <if test="applyReason != null">
        apply_reason,
      </if>
      <if test="applyUserCompanyId != null">
        apply_user_company_id,
      </if>
      <if test="applyUserCompanyName != null">
        apply_user_company_name,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="ddApplyId != null">
        dd_apply_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tasksId != null">
        #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="activityDescription != null">
        #{activityDescription,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        #{projectType,jdbcType=SMALLINT},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=SMALLINT},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="successAmount != null">
        #{successAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dispatchTime != null">
        #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successCount != null">
        #{successCount,jdbcType=INTEGER},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="recallTaskId != null">
        #{recallTaskId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentId != null">
        #{applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentName != null">
        #{applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyId != null">
        #{applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyName != null">
        #{applyUserCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ddApplyId != null">
        #{ddApplyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasksExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from fbb_grant_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_recall_tasks
    <set>
      <if test="record.tasksId != null">
        tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.activityDescription != null">
        activity_description = #{record.activityDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectType != null">
        project_type = #{record.projectType,jdbcType=SMALLINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=SMALLINT},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.successAmount != null">
        success_amount = #{record.successAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalCount != null">
        total_count = #{record.totalCount,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserName != null">
        operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserPhone != null">
        operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dispatchTime != null">
        dispatch_time = #{record.dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.successCount != null">
        success_count = #{record.successCount,jdbcType=INTEGER},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.recallTaskId != null">
        recall_task_id = #{record.recallTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserId != null">
        apply_user_id = #{record.applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserName != null">
        apply_user_name = #{record.applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserDepartmentId != null">
        apply_user_department_id = #{record.applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserDepartmentName != null">
        apply_user_department_name = #{record.applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyReason != null">
        apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserCompanyId != null">
        apply_user_company_id = #{record.applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserCompanyName != null">
        apply_user_company_name = #{record.applyUserCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ddApplyId != null">
        dd_apply_id = #{record.ddApplyId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_recall_tasks
    set tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      activity_description = #{record.activityDescription,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      project_type = #{record.projectType,jdbcType=SMALLINT},
      task_type = #{record.taskType,jdbcType=SMALLINT},
      task_status = #{record.taskStatus,jdbcType=SMALLINT},
      total_amount = #{record.totalAmount,jdbcType=DECIMAL},
      success_amount = #{record.successAmount,jdbcType=DECIMAL},
      total_count = #{record.totalCount,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      dispatch_time = #{record.dispatchTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      success_count = #{record.successCount,jdbcType=INTEGER},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      recall_task_id = #{record.recallTaskId,jdbcType=VARCHAR},
      apply_user_id = #{record.applyUserId,jdbcType=VARCHAR},
      apply_user_name = #{record.applyUserName,jdbcType=VARCHAR},
      apply_user_department_id = #{record.applyUserDepartmentId,jdbcType=VARCHAR},
      apply_user_department_name = #{record.applyUserDepartmentName,jdbcType=VARCHAR},
      apply_reason = #{record.applyReason,jdbcType=VARCHAR},
      apply_user_company_id = #{record.applyUserCompanyId,jdbcType=VARCHAR},
      apply_user_company_name = #{record.applyUserCompanyName,jdbcType=VARCHAR},
      apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      dd_apply_id = #{record.ddApplyId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_recall_tasks
    <set>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="activityDescription != null">
        activity_description = #{activityDescription,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        project_type = #{projectType,jdbcType=SMALLINT},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=SMALLINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=SMALLINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="successAmount != null">
        success_amount = #{successAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dispatchTime != null">
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successCount != null">
        success_count = #{successCount,jdbcType=INTEGER},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="recallTaskId != null">
        recall_task_id = #{recallTaskId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentId != null">
        apply_user_department_id = #{applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentName != null">
        apply_user_department_name = #{applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null">
        apply_reason = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyId != null">
        apply_user_company_id = #{applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyName != null">
        apply_user_company_name = #{applyUserCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ddApplyId != null">
        dd_apply_id = #{ddApplyId,jdbcType=VARCHAR},
      </if>
    </set>
    where tasks_id = #{tasksId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_recall_tasks
    set activity_id = #{activityId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      activity_description = #{activityDescription,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      project_type = #{projectType,jdbcType=SMALLINT},
      task_type = #{taskType,jdbcType=SMALLINT},
      task_status = #{taskStatus,jdbcType=SMALLINT},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      success_amount = #{successAmount,jdbcType=DECIMAL},
      total_count = #{totalCount,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      success_count = #{successCount,jdbcType=INTEGER},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      recall_task_id = #{recallTaskId,jdbcType=VARCHAR},
      apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      apply_user_department_id = #{applyUserDepartmentId,jdbcType=VARCHAR},
      apply_user_department_name = #{applyUserDepartmentName,jdbcType=VARCHAR},
      apply_reason = #{applyReason,jdbcType=VARCHAR},
      apply_user_company_id = #{applyUserCompanyId,jdbcType=VARCHAR},
      apply_user_company_name = #{applyUserCompanyName,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      dd_apply_id = #{ddApplyId,jdbcType=VARCHAR}
    where tasks_id = #{tasksId,jdbcType=VARCHAR}
  </update>
</mapper>