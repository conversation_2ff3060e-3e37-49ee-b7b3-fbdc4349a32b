<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantTasksExtMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_platform" jdbcType="VARCHAR" property="companyPlatform" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="grant_amount" jdbcType="DECIMAL" property="grantAmount" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="recall_status" jdbcType="SMALLINT" property="recallStatus" />
    <result column="recall_success_amount" jdbcType="DECIMAL" property="recallSuccessAmount" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_type" jdbcType="SMALLINT" property="projectType" />
    <result column="grant_user_id" jdbcType="VARCHAR" property="grantUserId" />
    <result column="grant_user_name" jdbcType="VARCHAR" property="grantUserName" />
    <result column="grant_user_phone" jdbcType="VARCHAR" property="grantUserPhone" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_department_id" jdbcType="VARCHAR" property="applyUserDepartmentId" />
    <result column="apply_user_department_name" jdbcType="VARCHAR" property="applyUserDepartmentName" />
    <result column="apply_user_company_id" jdbcType="VARCHAR" property="applyUserCompanyId" />
    <result column="apply_user_company_name" jdbcType="VARCHAR" property="applyUserCompanyName" />
  </resultMap>

  <select id="queryByTaskIdAndStatus" parameterType="map" resultType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasksExt">

    select count(*) AS successCount, sum(grant_amount) AS successAmount from fbb_grant_tasks where  tasks_id =#{tasksId}  and status = #{status}

  </select>



  <!-- 批量插入分贝币任务发放 -->
  <insert id ="batchInsert" parameterType="java.util.List" >

    insert into fbb_grant_tasks (id, tasks_id, company_id,
    company_name, company_platform, employee_id,
    grant_amount, status, create_time,
    complete_time, update_time, reason,
    phone_num, name, recall_status,
    recall_success_amount, fb_order_id, activity_id,
    project_id, project_type, grant_user_id,
    grant_user_name, grant_user_phone, apply_user_id,
    apply_time, apply_user_name, apply_user_department_id,
    apply_user_department_name, apply_user_company_id,
    apply_user_company_name)
    values
    <foreach collection ="fbbGrantTasksList" item="fbbGrantTasks" index= "index" separator =",">
      (
      #{fbbGrantTasks.id}, #{fbbGrantTasks.tasksId}, #{fbbGrantTasks.companyId},
      #{fbbGrantTasks.companyName}, #{fbbGrantTasks.companyPlatform}, #{fbbGrantTasks.employeeId},
      #{fbbGrantTasks.grantAmount}, #{fbbGrantTasks.status}, now(),
      #{fbbGrantTasks.completeTime}, now(), #{fbbGrantTasks.reason},
      #{fbbGrantTasks.phoneNum}, #{fbbGrantTasks.name}, #{fbbGrantTasks.recallStatus},
      #{fbbGrantTasks.recallSuccessAmount}, #{fbbGrantTasks.fbOrderId}, #{fbbGrantTasks.activityId},
      #{fbbGrantTasks.projectId}, #{fbbGrantTasks.projectType}, #{fbbGrantTasks.grantUserId},
      #{fbbGrantTasks.grantUserName}, #{fbbGrantTasks.grantUserPhone}, #{fbbGrantTasks.applyUserId},
      #{fbbGrantTasks.applyTime}, #{fbbGrantTasks.applyUserName}, #{fbbGrantTasks.applyUserDepartmentId},
      #{fbbGrantTasks.applyUserDepartmentName}, #{fbbGrantTasks.applyUserCompanyId},
      #{fbbGrantTasks.applyUserCompanyName}
      )
    </foreach >
  </insert >

  <!--发放币批量 修改撤回的状态-->
  <update id="batchUpdateRecallIng" parameterType="map">

    update fbb_grant_tasks set

    recall_status = #{status},

    update_time = now()

    where id in

    <foreach collection="grantIdList" item="id" index="index"
             open="(" close=")" separator=",">
      #{id}
    </foreach>

  </update>

</mapper>