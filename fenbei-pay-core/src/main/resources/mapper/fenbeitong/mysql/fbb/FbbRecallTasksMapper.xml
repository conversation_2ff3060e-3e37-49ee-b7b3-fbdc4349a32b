<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbRecallTasksMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="recall_amount" jdbcType="DECIMAL" property="recallAmount" />
    <result column="recall_success_amount" jdbcType="DECIMAL" property="recallSuccessAmount" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="grant_id" jdbcType="VARCHAR" property="grantId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_platform" jdbcType="VARCHAR" property="companyPlatform" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tasks_id, employee_id, recall_amount, recall_success_amount, status, reason, 
    create_time, complete_time, update_time, grant_id, employee_name, company_name, company_platform, 
    phone_num
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasksExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fbb_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from fbb_recall_tasks
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_recall_tasks
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasksExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_recall_tasks (id, tasks_id, employee_id, 
      recall_amount, recall_success_amount, status, 
      reason, create_time, complete_time, 
      update_time, grant_id, employee_name, 
      company_name, company_platform, phone_num
      )
    values (#{id,jdbcType=VARCHAR}, #{tasksId,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{recallAmount,jdbcType=DECIMAL}, #{recallSuccessAmount,jdbcType=DECIMAL}, #{status,jdbcType=SMALLINT}, 
      #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{grantId,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{companyPlatform,jdbcType=VARCHAR}, #{phoneNum,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_recall_tasks
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tasksId != null">
        tasks_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="recallAmount != null">
        recall_amount,
      </if>
      <if test="recallSuccessAmount != null">
        recall_success_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="grantId != null">
        grant_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyPlatform != null">
        company_platform,
      </if>
      <if test="phoneNum != null">
        phone_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tasksId != null">
        #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="recallAmount != null">
        #{recallAmount,jdbcType=DECIMAL},
      </if>
      <if test="recallSuccessAmount != null">
        #{recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantId != null">
        #{grantId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyPlatform != null">
        #{companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasksExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from fbb_recall_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_recall_tasks
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.tasksId != null">
        tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.recallAmount != null">
        recall_amount = #{record.recallAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.recallSuccessAmount != null">
        recall_success_amount = #{record.recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantId != null">
        grant_id = #{record.grantId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyPlatform != null">
        company_platform = #{record.companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNum != null">
        phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_recall_tasks
    set id = #{record.id,jdbcType=VARCHAR},
      tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      recall_amount = #{record.recallAmount,jdbcType=DECIMAL},
      recall_success_amount = #{record.recallSuccessAmount,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=SMALLINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      grant_id = #{record.grantId,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_platform = #{record.companyPlatform,jdbcType=VARCHAR},
      phone_num = #{record.phoneNum,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_recall_tasks
    <set>
      <if test="tasksId != null">
        tasks_id = #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="recallAmount != null">
        recall_amount = #{recallAmount,jdbcType=DECIMAL},
      </if>
      <if test="recallSuccessAmount != null">
        recall_success_amount = #{recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantId != null">
        grant_id = #{grantId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyPlatform != null">
        company_platform = #{companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbRecallTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_recall_tasks
    set tasks_id = #{tasksId,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      recall_amount = #{recallAmount,jdbcType=DECIMAL},
      recall_success_amount = #{recallSuccessAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=SMALLINT},
      reason = #{reason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      grant_id = #{grantId,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      company_platform = #{companyPlatform,jdbcType=VARCHAR},
      phone_num = #{phoneNum,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>