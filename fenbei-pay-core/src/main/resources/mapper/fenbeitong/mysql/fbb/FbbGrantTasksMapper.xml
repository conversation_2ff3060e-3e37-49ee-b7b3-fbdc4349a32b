<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.FbbGrantTasksMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tasks_id" jdbcType="VARCHAR" property="tasksId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_platform" jdbcType="VARCHAR" property="companyPlatform" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="grant_amount" jdbcType="DECIMAL" property="grantAmount" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="recall_status" jdbcType="SMALLINT" property="recallStatus" />
    <result column="recall_success_amount" jdbcType="DECIMAL" property="recallSuccessAmount" />
    <result column="fb_order_id" jdbcType="VARCHAR" property="fbOrderId" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="project_type" jdbcType="SMALLINT" property="projectType" />
    <result column="grant_user_id" jdbcType="VARCHAR" property="grantUserId" />
    <result column="grant_user_name" jdbcType="VARCHAR" property="grantUserName" />
    <result column="grant_user_phone" jdbcType="VARCHAR" property="grantUserPhone" />
    <result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_department_id" jdbcType="VARCHAR" property="applyUserDepartmentId" />
    <result column="apply_user_department_name" jdbcType="VARCHAR" property="applyUserDepartmentName" />
    <result column="apply_user_company_id" jdbcType="VARCHAR" property="applyUserCompanyId" />
    <result column="apply_user_company_name" jdbcType="VARCHAR" property="applyUserCompanyName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tasks_id, company_id, company_name, company_platform, employee_id, grant_amount, 
    status, create_time, complete_time, update_time, reason, phone_num, name, recall_status, 
    recall_success_amount, fb_order_id, activity_id, project_id, project_type, grant_user_id, 
    grant_user_name, grant_user_phone, apply_user_id, apply_time, apply_user_name, apply_user_department_id, 
    apply_user_department_name, apply_user_company_id, apply_user_company_name
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasksExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fbb_grant_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from fbb_grant_tasks
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_grant_tasks
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasksExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from fbb_grant_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_grant_tasks (id, tasks_id, company_id, 
      company_name, company_platform, employee_id, 
      grant_amount, status, create_time, 
      complete_time, update_time, reason, 
      phone_num, name, recall_status, 
      recall_success_amount, fb_order_id, activity_id, 
      project_id, project_type, grant_user_id, 
      grant_user_name, grant_user_phone, apply_user_id, 
      apply_time, apply_user_name, apply_user_department_id, 
      apply_user_department_name, apply_user_company_id, 
      apply_user_company_name)
    values (#{id,jdbcType=VARCHAR}, #{tasksId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{companyPlatform,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, 
      #{grantAmount,jdbcType=DECIMAL}, #{status,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{completeTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{reason,jdbcType=VARCHAR}, 
      #{phoneNum,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{recallStatus,jdbcType=SMALLINT}, 
      #{recallSuccessAmount,jdbcType=DECIMAL}, #{fbOrderId,jdbcType=VARCHAR}, #{activityId,jdbcType=VARCHAR}, 
      #{projectId,jdbcType=VARCHAR}, #{projectType,jdbcType=SMALLINT}, #{grantUserId,jdbcType=VARCHAR}, 
      #{grantUserName,jdbcType=VARCHAR}, #{grantUserPhone,jdbcType=VARCHAR}, #{applyUserId,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{applyUserName,jdbcType=VARCHAR}, #{applyUserDepartmentId,jdbcType=VARCHAR}, 
      #{applyUserDepartmentName,jdbcType=VARCHAR}, #{applyUserCompanyId,jdbcType=VARCHAR}, 
      #{applyUserCompanyName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into fbb_grant_tasks
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tasksId != null">
        tasks_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyPlatform != null">
        company_platform,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="grantAmount != null">
        grant_amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="phoneNum != null">
        phone_num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="recallStatus != null">
        recall_status,
      </if>
      <if test="recallSuccessAmount != null">
        recall_success_amount,
      </if>
      <if test="fbOrderId != null">
        fb_order_id,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectType != null">
        project_type,
      </if>
      <if test="grantUserId != null">
        grant_user_id,
      </if>
      <if test="grantUserName != null">
        grant_user_name,
      </if>
      <if test="grantUserPhone != null">
        grant_user_phone,
      </if>
      <if test="applyUserId != null">
        apply_user_id,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="applyUserName != null">
        apply_user_name,
      </if>
      <if test="applyUserDepartmentId != null">
        apply_user_department_id,
      </if>
      <if test="applyUserDepartmentName != null">
        apply_user_department_name,
      </if>
      <if test="applyUserCompanyId != null">
        apply_user_company_id,
      </if>
      <if test="applyUserCompanyName != null">
        apply_user_company_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tasksId != null">
        #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyPlatform != null">
        #{companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="grantAmount != null">
        #{grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="recallStatus != null">
        #{recallStatus,jdbcType=SMALLINT},
      </if>
      <if test="recallSuccessAmount != null">
        #{recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="fbOrderId != null">
        #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        #{projectType,jdbcType=SMALLINT},
      </if>
      <if test="grantUserId != null">
        #{grantUserId,jdbcType=VARCHAR},
      </if>
      <if test="grantUserName != null">
        #{grantUserName,jdbcType=VARCHAR},
      </if>
      <if test="grantUserPhone != null">
        #{grantUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentId != null">
        #{applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentName != null">
        #{applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyId != null">
        #{applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyName != null">
        #{applyUserCompanyName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasksExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from fbb_grant_tasks
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_tasks
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.tasksId != null">
        tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyPlatform != null">
        company_platform = #{record.companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.grantAmount != null">
        grant_amount = #{record.grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNum != null">
        phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.recallStatus != null">
        recall_status = #{record.recallStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.recallSuccessAmount != null">
        recall_success_amount = #{record.recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.fbOrderId != null">
        fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectType != null">
        project_type = #{record.projectType,jdbcType=SMALLINT},
      </if>
      <if test="record.grantUserId != null">
        grant_user_id = #{record.grantUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.grantUserName != null">
        grant_user_name = #{record.grantUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.grantUserPhone != null">
        grant_user_phone = #{record.grantUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserId != null">
        apply_user_id = #{record.applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.applyUserName != null">
        apply_user_name = #{record.applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserDepartmentId != null">
        apply_user_department_id = #{record.applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserDepartmentName != null">
        apply_user_department_name = #{record.applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserCompanyId != null">
        apply_user_company_id = #{record.applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="record.applyUserCompanyName != null">
        apply_user_company_name = #{record.applyUserCompanyName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_tasks
    set id = #{record.id,jdbcType=VARCHAR},
      tasks_id = #{record.tasksId,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_platform = #{record.companyPlatform,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      grant_amount = #{record.grantAmount,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      reason = #{record.reason,jdbcType=VARCHAR},
      phone_num = #{record.phoneNum,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      recall_status = #{record.recallStatus,jdbcType=SMALLINT},
      recall_success_amount = #{record.recallSuccessAmount,jdbcType=DECIMAL},
      fb_order_id = #{record.fbOrderId,jdbcType=VARCHAR},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      project_type = #{record.projectType,jdbcType=SMALLINT},
      grant_user_id = #{record.grantUserId,jdbcType=VARCHAR},
      grant_user_name = #{record.grantUserName,jdbcType=VARCHAR},
      grant_user_phone = #{record.grantUserPhone,jdbcType=VARCHAR},
      apply_user_id = #{record.applyUserId,jdbcType=VARCHAR},
      apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      apply_user_name = #{record.applyUserName,jdbcType=VARCHAR},
      apply_user_department_id = #{record.applyUserDepartmentId,jdbcType=VARCHAR},
      apply_user_department_name = #{record.applyUserDepartmentName,jdbcType=VARCHAR},
      apply_user_company_id = #{record.applyUserCompanyId,jdbcType=VARCHAR},
      apply_user_company_name = #{record.applyUserCompanyName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_tasks
    <set>
      <if test="tasksId != null">
        tasks_id = #{tasksId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyPlatform != null">
        company_platform = #{companyPlatform,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="grantAmount != null">
        grant_amount = #{grantAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="recallStatus != null">
        recall_status = #{recallStatus,jdbcType=SMALLINT},
      </if>
      <if test="recallSuccessAmount != null">
        recall_success_amount = #{recallSuccessAmount,jdbcType=DECIMAL},
      </if>
      <if test="fbOrderId != null">
        fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        project_type = #{projectType,jdbcType=SMALLINT},
      </if>
      <if test="grantUserId != null">
        grant_user_id = #{grantUserId,jdbcType=VARCHAR},
      </if>
      <if test="grantUserName != null">
        grant_user_name = #{grantUserName,jdbcType=VARCHAR},
      </if>
      <if test="grantUserPhone != null">
        grant_user_phone = #{grantUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentId != null">
        apply_user_department_id = #{applyUserDepartmentId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserDepartmentName != null">
        apply_user_department_name = #{applyUserDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyId != null">
        apply_user_company_id = #{applyUserCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="applyUserCompanyName != null">
        apply_user_company_name = #{applyUserCompanyName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantTasks">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update fbb_grant_tasks
    set tasks_id = #{tasksId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      company_platform = #{companyPlatform,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      grant_amount = #{grantAmount,jdbcType=DECIMAL},
      status = #{status,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reason = #{reason,jdbcType=VARCHAR},
      phone_num = #{phoneNum,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      recall_status = #{recallStatus,jdbcType=SMALLINT},
      recall_success_amount = #{recallSuccessAmount,jdbcType=DECIMAL},
      fb_order_id = #{fbOrderId,jdbcType=VARCHAR},
      activity_id = #{activityId,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=VARCHAR},
      project_type = #{projectType,jdbcType=SMALLINT},
      grant_user_id = #{grantUserId,jdbcType=VARCHAR},
      grant_user_name = #{grantUserName,jdbcType=VARCHAR},
      grant_user_phone = #{grantUserPhone,jdbcType=VARCHAR},
      apply_user_id = #{applyUserId,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      apply_user_department_id = #{applyUserDepartmentId,jdbcType=VARCHAR},
      apply_user_department_name = #{applyUserDepartmentName,jdbcType=VARCHAR},
      apply_user_company_id = #{applyUserCompanyId,jdbcType=VARCHAR},
      apply_user_company_name = #{applyUserCompanyName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>