<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.vouchers.VouchersTaskMapper">
  <resultMap id="BaseResultMap" type="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vouchers_task_id" jdbcType="VARCHAR" property="vouchersTaskId" />
    <result column="vouchers_task_name" jdbcType="VARCHAR" property="vouchersTaskName" />
    <result column="vouchers_task_type" jdbcType="SMALLINT" property="vouchersTaskType" />
    <result column="vouchers_number" jdbcType="BIGINT" property="vouchersNumber" />
    <result column="vouchers_total_amount" jdbcType="DECIMAL" property="vouchersTotalAmount" />
    <result column="vouchers_operation_number" jdbcType="BIGINT" property="vouchersOperationNumber" />
    <result column="vouchers_operation_amount" jdbcType="DECIMAL" property="vouchersOperationAmount" />
    <result column="vouchers_info_json" jdbcType="VARCHAR" property="vouchersInfoJson" />
    <result column="voucher_recovery_type" jdbcType="SMALLINT" property="voucherRecoveryType" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="operation_user_id" jdbcType="VARCHAR" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_user_phone" jdbcType="VARCHAR" property="operationUserPhone" />
    <result column="operation_user_department" jdbcType="VARCHAR" property="operationUserDepartment" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="task_failure_reasons" jdbcType="VARCHAR" property="taskFailureReasons" />
    <result column="write_invoice_type" jdbcType="INTEGER" property="writeInvoiceType" />
    <result column="write_invoice_status" jdbcType="INTEGER" property="writeInvoiceStatus" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="can_transfer" jdbcType="INTEGER" property="canTransfer" />
    <result column="task_biz_id" jdbcType="VARCHAR" property="taskBizId" />
    <result column="total_recovery_amount" jdbcType="DECIMAL" property="totalRecoveryAmount" />
    <result column="deduction_account_type" jdbcType="INTEGER" property="deductionAccountType" />
    <result column="account_model" jdbcType="INTEGER" property="accountModel" />
    <result column="account_sub_id" jdbcType="VARCHAR" property="accountSubId" />
    <result column="account_sub_flow_id" jdbcType="VARCHAR" property="accountSubFlowId" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="timing_grant_time" jdbcType="TIMESTAMP" property="timingGrantTime" />
    <result column="grant_type" jdbcType="INTEGER" property="grantType" />
    <result column="cost_info" jdbcType="VARCHAR" property="costInfo" />
    <result column="date_of_expense" jdbcType="VARCHAR" property="dateOfExpense" />
    <result column="cost_id" jdbcType="VARCHAR" property="costId" />
    <result column="cost_status" jdbcType="INTEGER" property="costStatus" />
    <result column="push" jdbcType="INTEGER" property="push" />
    <result column="open_enable" jdbcType="INTEGER" property="openEnable" />
    <result column="background_url" jdbcType="VARCHAR" property="backgroundUrl" />
    <result column="background_name" jdbcType="VARBINARY" property="backgroundName" />
    <result column="notice_content" jdbcType="VARCHAR" property="noticeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, vouchers_task_id, vouchers_task_name, vouchers_task_type, vouchers_number, vouchers_total_amount, 
    vouchers_operation_number, vouchers_operation_amount, vouchers_info_json, voucher_recovery_type, 
    status, create_time, update_time, end_time, company_id, operation_user_id, operation_user_name, 
    operation_user_phone, operation_user_department, biz_no, task_desc, task_failure_reasons, 
    write_invoice_type, write_invoice_status, bill_status, can_transfer, task_biz_id, 
    total_recovery_amount, deduction_account_type, account_model, account_sub_id, account_sub_flow_id, 
    bank_name, bank_account_no, grant_type, timing_grant_time, cost_info, cost_status, cost_id, push, date_of_expense, open_enable, background_url, background_name,notice_content
  </sql>
  <select id="selectByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from vouchers_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <choose>
        <when test="offset == null">
          limit ${limit}
        </when>
        <otherwise>
          limit ${offset}, ${limit}
        </otherwise>
      </choose>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from vouchers_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from vouchers_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskExample">
    delete from vouchers_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask" useGeneratedKeys="true">
    insert into vouchers_task (vouchers_task_id, vouchers_task_name, 
      vouchers_task_type, vouchers_number, vouchers_total_amount, 
      vouchers_operation_number, vouchers_operation_amount, 
      vouchers_info_json, voucher_recovery_type, 
      status, create_time, update_time, 
      end_time, company_id, operation_user_id, 
      operation_user_name, operation_user_phone, 
      operation_user_department, biz_no, task_desc, 
      task_failure_reasons, write_invoice_type, write_invoice_status, 
      bill_status, can_transfer, task_biz_id, 
      total_recovery_amount, deduction_account_type, 
      account_model, account_sub_id, account_sub_flow_id, 
      bank_name, bank_account_no)
    values (#{vouchersTaskId,jdbcType=VARCHAR}, #{vouchersTaskName,jdbcType=VARCHAR}, 
      #{vouchersTaskType,jdbcType=SMALLINT}, #{vouchersNumber,jdbcType=BIGINT}, #{vouchersTotalAmount,jdbcType=DECIMAL}, 
      #{vouchersOperationNumber,jdbcType=BIGINT}, #{vouchersOperationAmount,jdbcType=DECIMAL}, 
      #{vouchersInfoJson,jdbcType=VARCHAR}, #{voucherRecoveryType,jdbcType=SMALLINT}, 
      #{status,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=VARCHAR}, #{operationUserId,jdbcType=VARCHAR}, 
      #{operationUserName,jdbcType=VARCHAR}, #{operationUserPhone,jdbcType=VARCHAR}, 
      #{operationUserDepartment,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, #{taskDesc,jdbcType=VARCHAR}, 
      #{taskFailureReasons,jdbcType=VARCHAR}, #{writeInvoiceType,jdbcType=INTEGER}, #{writeInvoiceStatus,jdbcType=INTEGER}, 
      #{billStatus,jdbcType=INTEGER}, #{canTransfer,jdbcType=INTEGER}, #{taskBizId,jdbcType=VARCHAR}, 
      #{totalRecoveryAmount,jdbcType=DECIMAL}, #{deductionAccountType,jdbcType=INTEGER}, 
      #{accountModel,jdbcType=INTEGER}, #{accountSubId,jdbcType=VARCHAR}, #{accountSubFlowId,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{bankAccountNo,jdbcType=VARCHAR}), #{timingGrantTime,jdbcType=TIMESTAMP},
      #{grantType,jdbcType=INTEGER}
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask" useGeneratedKeys="true">
    insert into vouchers_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskId != null">
        vouchers_task_id,
      </if>
      <if test="vouchersTaskName != null">
        vouchers_task_name,
      </if>
      <if test="vouchersTaskType != null">
        vouchers_task_type,
      </if>
      <if test="vouchersNumber != null">
        vouchers_number,
      </if>
      <if test="vouchersTotalAmount != null">
        vouchers_total_amount,
      </if>
      <if test="vouchersOperationNumber != null">
        vouchers_operation_number,
      </if>
      <if test="vouchersOperationAmount != null">
        vouchers_operation_amount,
      </if>
      <if test="vouchersInfoJson != null">
        vouchers_info_json,
      </if>
      <if test="voucherRecoveryType != null">
        voucher_recovery_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="operationUserName != null">
        operation_user_name,
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone,
      </if>
      <if test="operationUserDepartment != null">
        operation_user_department,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="taskFailureReasons != null">
        task_failure_reasons,
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type,
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="canTransfer != null">
        can_transfer,
      </if>
      <if test="taskBizId != null">
        task_biz_id,
      </if>
      <if test="totalRecoveryAmount != null">
        total_recovery_amount,
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type,
      </if>
      <if test="accountModel != null">
        account_model,
      </if>
      <if test="accountSubId != null">
        account_sub_id,
      </if>
      <if test="accountSubFlowId != null">
        account_sub_flow_id,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="timingGrantTime != null">
        timing_grant_time,
      </if>
      <if test="grantType != null">
        grant_type,
      </if>
      <if test="costInfo != null and costInfo != ''">
        cost_info,
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        date_of_expense,
      </if>
      <if test="costId != null and costId != ''">
        cost_id,
      </if>
      <if test="costStatus != null">
        cost_status,
      </if>
      <if test="push != null">
        push,
      </if>
      <if test="openEnable != null">
        open_enable,
      </if>
      <if test="backgroundUrl != null">
        background_url,
      </if>
      <if test="backgroundName != null">
        background_name,
      </if>
      <if test="noticeContent != null">
        notice_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vouchersTaskId != null">
        #{vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskName != null">
        #{vouchersTaskName,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskType != null">
        #{vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="vouchersNumber != null">
        #{vouchersNumber,jdbcType=BIGINT},
      </if>
      <if test="vouchersTotalAmount != null">
        #{vouchersTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vouchersOperationNumber != null">
        #{vouchersOperationNumber,jdbcType=BIGINT},
      </if>
      <if test="vouchersOperationAmount != null">
        #{vouchersOperationAmount,jdbcType=DECIMAL},
      </if>
      <if test="vouchersInfoJson != null">
        #{vouchersInfoJson,jdbcType=VARCHAR},
      </if>
      <if test="voucherRecoveryType != null">
        #{voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="operationUserDepartment != null">
        #{operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="taskFailureReasons != null">
        #{taskFailureReasons,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="taskBizId != null">
        #{taskBizId,jdbcType=VARCHAR},
      </if>
      <if test="totalRecoveryAmount != null">
        #{totalRecoveryAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductionAccountType != null">
        #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountSubFlowId != null">
        #{accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="timingGrantTime != null">
        #{timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantType != null">
        #{grantType,jdbcType=INTEGER},
      </if>
      <if test="costInfo != null and costInfo != ''">
        #{costInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        #{dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="costId != null and costId != ''">
        #{costId,jdbcType=VARCHAR},
      </if>
      <if test="costStatus != null ">
        #{costStatus,jdbcType=INTEGER},
      </if>
      <if test="push != null">
        #{push,jdbcType=INTEGER},
      </if>
      <if test="openEnable != null">
        #{openEnable,jdbcType=INTEGER},
      </if>
      <if test="backgroundUrl != null">
        #{backgroundUrl,jdbcType=INTEGER},
      </if>
      <if test="backgroundName != null">
        #{backgroundName,jdbcType=INTEGER},
      </if>
      <if test="noticeContent != null">
        #{noticeContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTaskExample" resultType="java.lang.Long">
    select count(*) from vouchers_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update vouchers_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTaskId != null">
        vouchers_task_id = #{record.vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersTaskName != null">
        vouchers_task_name = #{record.vouchersTaskName,jdbcType=VARCHAR},
      </if>
      <if test="record.vouchersTaskType != null">
        vouchers_task_type = #{record.vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="record.vouchersNumber != null">
        vouchers_number = #{record.vouchersNumber,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersTotalAmount != null">
        vouchers_total_amount = #{record.vouchersTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.vouchersOperationNumber != null">
        vouchers_operation_number = #{record.vouchersOperationNumber,jdbcType=BIGINT},
      </if>
      <if test="record.vouchersOperationAmount != null">
        vouchers_operation_amount = #{record.vouchersOperationAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.vouchersInfoJson != null">
        vouchers_info_json = #{record.vouchersInfoJson,jdbcType=VARCHAR},
      </if>
      <if test="record.voucherRecoveryType != null">
        voucher_recovery_type = #{record.voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserId != null">
        operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserName != null">
        operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserPhone != null">
        operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.operationUserDepartment != null">
        operation_user_department = #{record.operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.bizNo != null">
        biz_no = #{record.bizNo,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDesc != null">
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.taskFailureReasons != null">
        task_failure_reasons = #{record.taskFailureReasons,jdbcType=VARCHAR},
      </if>
      <if test="record.writeInvoiceType != null">
        write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.writeInvoiceStatus != null">
        write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="record.billStatus != null">
        bill_status = #{record.billStatus,jdbcType=INTEGER},
      </if>
      <if test="record.canTransfer != null">
        can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      </if>
      <if test="record.taskBizId != null">
        task_biz_id = #{record.taskBizId,jdbcType=VARCHAR},
      </if>
      <if test="record.totalRecoveryAmount != null">
        total_recovery_amount = #{record.totalRecoveryAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.deductionAccountType != null">
        deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountModel != null">
        account_model = #{record.accountModel,jdbcType=INTEGER},
      </if>
      <if test="record.accountSubId != null">
        account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSubFlowId != null">
        account_sub_flow_id = #{record.accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.timingGrantTime != null">
        create_time = #{record.timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.grantType != null">
        grant_type = #{record.grantType,jdbcType=INTEGER},
      </if>
      <if test="record.costInfo != null and record.costInfo != ''">
        cost_info = #{record.costInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOfExpense != null and record.dateOfExpense != ''">
        date_of_expense = #{record.dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="record.costStatus != null ">
        cost_status = #{record.costStatus,jdbcType=INTEGER},
      </if>
      <if test="record.costId != null and record.costId != ''">
        cost_id = #{record.costId,jdbcType=VARCHAR},
      </if>
      <if test="record.push != null">
        push = #{record.push,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update vouchers_task
    set id = #{record.id,jdbcType=BIGINT},
      vouchers_task_id = #{record.vouchersTaskId,jdbcType=VARCHAR},
      vouchers_task_name = #{record.vouchersTaskName,jdbcType=VARCHAR},
      vouchers_task_type = #{record.vouchersTaskType,jdbcType=SMALLINT},
      vouchers_number = #{record.vouchersNumber,jdbcType=BIGINT},
      vouchers_total_amount = #{record.vouchersTotalAmount,jdbcType=DECIMAL},
      vouchers_operation_number = #{record.vouchersOperationNumber,jdbcType=BIGINT},
      vouchers_operation_amount = #{record.vouchersOperationAmount,jdbcType=DECIMAL},
      vouchers_info_json = #{record.vouchersInfoJson,jdbcType=VARCHAR},
      voucher_recovery_type = #{record.voucherRecoveryType,jdbcType=SMALLINT},
      status = #{record.status,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      operation_user_id = #{record.operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{record.operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{record.operationUserPhone,jdbcType=VARCHAR},
      operation_user_department = #{record.operationUserDepartment,jdbcType=VARCHAR},
      biz_no = #{record.bizNo,jdbcType=VARCHAR},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      task_failure_reasons = #{record.taskFailureReasons,jdbcType=VARCHAR},
      write_invoice_type = #{record.writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{record.writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{record.billStatus,jdbcType=INTEGER},
      can_transfer = #{record.canTransfer,jdbcType=INTEGER},
      task_biz_id = #{record.taskBizId,jdbcType=VARCHAR},
      total_recovery_amount = #{record.totalRecoveryAmount,jdbcType=DECIMAL},
      deduction_account_type = #{record.deductionAccountType,jdbcType=INTEGER},
      account_model = #{record.accountModel,jdbcType=INTEGER},
      account_sub_id = #{record.accountSubId,jdbcType=VARCHAR},
      account_sub_flow_id = #{record.accountSubFlowId,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      timing_grant_time = #{record.timingGrantTime,jdbcType=TIMESTAMP},
      grant_type = #{record.grantType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask">
    update vouchers_task
    <set>
      <if test="vouchersTaskId != null">
        vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskName != null">
        vouchers_task_name = #{vouchersTaskName,jdbcType=VARCHAR},
      </if>
      <if test="vouchersTaskType != null">
        vouchers_task_type = #{vouchersTaskType,jdbcType=SMALLINT},
      </if>
      <if test="vouchersNumber != null">
        vouchers_number = #{vouchersNumber,jdbcType=BIGINT},
      </if>
      <if test="vouchersTotalAmount != null">
        vouchers_total_amount = #{vouchersTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vouchersOperationNumber != null">
        vouchers_operation_number = #{vouchersOperationNumber,jdbcType=BIGINT},
      </if>
      <if test="vouchersOperationAmount != null">
        vouchers_operation_amount = #{vouchersOperationAmount,jdbcType=DECIMAL},
      </if>
      <if test="vouchersInfoJson != null">
        vouchers_info_json = #{vouchersInfoJson,jdbcType=VARCHAR},
      </if>
      <if test="voucherRecoveryType != null">
        voucher_recovery_type = #{voucherRecoveryType,jdbcType=SMALLINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      </if>
      <if test="operationUserName != null">
        operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationUserPhone != null">
        operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="operationUserDepartment != null">
        operation_user_department = #{operationUserDepartment,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="taskFailureReasons != null">
        task_failure_reasons = #{taskFailureReasons,jdbcType=VARCHAR},
      </if>
      <if test="writeInvoiceType != null">
        write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="writeInvoiceStatus != null">
        write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="canTransfer != null">
        can_transfer = #{canTransfer,jdbcType=INTEGER},
      </if>
      <if test="taskBizId != null">
        task_biz_id = #{taskBizId,jdbcType=VARCHAR},
      </if>
      <if test="totalRecoveryAmount != null">
        total_recovery_amount = #{totalRecoveryAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductionAccountType != null">
        deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      </if>
      <if test="accountModel != null">
        account_model = #{accountModel,jdbcType=INTEGER},
      </if>
      <if test="accountSubId != null">
        account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      </if>
      <if test="accountSubFlowId != null">
        account_sub_flow_id = #{accountSubFlowId,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="timingGrantTime != null">
        timing_grant_time = #{timingGrantTime,jdbcType=TIMESTAMP},
      </if>
      <if test="grantType != null">
        grant_type = #{grantType,jdbcType=INTEGER},
      </if>
      <if test="costInfo != null and costInfo != ''">
        cost_info = #{costInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateOfExpense != null and dateOfExpense != ''">
        date_of_expense = #{dateOfExpense,jdbcType=VARCHAR},
      </if>
      <if test="costStatus != null ">
        cost_status = #{costStatus,jdbcType=INTEGER},
      </if>
      <if test="costId != null and costId != ''">
        cost_id = #{costId,jdbcType=VARCHAR},
      </if>
      <if test="push != null">
        push = #{push,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask">
    update vouchers_task
    set vouchers_task_id = #{vouchersTaskId,jdbcType=VARCHAR},
      vouchers_task_name = #{vouchersTaskName,jdbcType=VARCHAR},
      vouchers_task_type = #{vouchersTaskType,jdbcType=SMALLINT},
      vouchers_number = #{vouchersNumber,jdbcType=BIGINT},
      vouchers_total_amount = #{vouchersTotalAmount,jdbcType=DECIMAL},
      vouchers_operation_number = #{vouchersOperationNumber,jdbcType=BIGINT},
      vouchers_operation_amount = #{vouchersOperationAmount,jdbcType=DECIMAL},
      vouchers_info_json = #{vouchersInfoJson,jdbcType=VARCHAR},
      voucher_recovery_type = #{voucherRecoveryType,jdbcType=SMALLINT},
      status = #{status,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=VARCHAR},
      operation_user_id = #{operationUserId,jdbcType=VARCHAR},
      operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      operation_user_phone = #{operationUserPhone,jdbcType=VARCHAR},
      operation_user_department = #{operationUserDepartment,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      task_failure_reasons = #{taskFailureReasons,jdbcType=VARCHAR},
      write_invoice_type = #{writeInvoiceType,jdbcType=INTEGER},
      write_invoice_status = #{writeInvoiceStatus,jdbcType=INTEGER},
      bill_status = #{billStatus,jdbcType=INTEGER},
      can_transfer = #{canTransfer,jdbcType=INTEGER},
      task_biz_id = #{taskBizId,jdbcType=VARCHAR},
      total_recovery_amount = #{totalRecoveryAmount,jdbcType=DECIMAL},
      deduction_account_type = #{deductionAccountType,jdbcType=INTEGER},
      account_model = #{accountModel,jdbcType=INTEGER},
      account_sub_id = #{accountSubId,jdbcType=VARCHAR},
      account_sub_flow_id = #{accountSubFlowId,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      timing_grant_time = #{timingGrantTime,jdbcType=TIMESTAMP},
      grant_type = #{grantType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>