package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import lombok.Data;
import lombok.ToString;


@Data
@ToString(callSuper = true)
public class CashierUpdatePayTradeRespVo extends CashierPayTradeBaseRespVo {

    //更新结果描述
    private String  updateResultDesc;

    /**
     * 各项金额
     */
    private String  amountResultDesc;

    /**
     * 支付状态描述
     */
    private String payStatusDesc;

    private String getPayStatusDesc(){
        payStatusDesc = CashierPayStatus.getNameFromCode(getPayStatus());
        return payStatusDesc;
    }

    public void toPayResultStr(CashierOrderSettlement cashier){
        StringBuffer sb = new StringBuffer();
        sb.append("正向订单号：");
        sb.append(cashier.getFbOrderId());
        sb.append("\n");
        sb.append("修正后，支付状态为:");
        sb.append(CashierPayStatus.getNameFromCode(cashier.getPayStatus()));
        sb.append("\n");
        if(CashierPayStatus.paySuccessRecord(getPayStatus())){
            sb.append("实际付款总金额：");
            sb.append(cashier.getAmountAll());
            sb.append("\n");
            sb.append("企业实际付款金额：");
            sb.append(cashier.getAmountPublic());
            sb.append("\n");
            sb.append("个人实际付款金额：");
            sb.append(cashier.getAmountCompany());
            sb.append("\n");
        }else{
            sb.append("需要付款总金额：");
            sb.append(cashier.getAmountAll());
            sb.append("\n");
            sb.append("企业需要付款金额：");
            sb.append(cashier.getAmountPublic());
            sb.append("\n");
            sb.append("个人需要付款金额：");
            sb.append(cashier.getAmountCompany());
            sb.append("\n");
            sb.append("请在两小时内完成支付");

        }
        amountResultDesc = sb.toString();
    }

    /**
     * 仍需退款金额描述
     * @return
     */
    public void toRefundResultStr(CashierRefundSettlementRemainReturnStatVo remainRefundStat){
        StringBuffer sb = new StringBuffer();
        sb.append("正向订单号：");
        sb.append(remainRefundStat.getFbOrderId());
        sb.append("\n");
        sb.append("仍需退款总金额：");
        sb.append(remainRefundStat.getRefundRemainAmount());
        sb.append("\n");
        sb.append("企业仍需退款金额：");
        sb.append(remainRefundStat.getRefundPublicRemainAmount());
        sb.append("\n");
        sb.append("个人仍需退款金额：");
        sb.append(remainRefundStat.getRefundPersonalRemainAmount());
        amountResultDesc = sb.toString();
    }

}
