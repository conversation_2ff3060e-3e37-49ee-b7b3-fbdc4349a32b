package com.fenbeitong.fenbeipay.core.constant.personpay;

/**
 * Created by mac on 17/12/29.
 */
public class PayConstant {
    //用车订单头
    @Deprecated
    public static String FUQIANLA_FB = "fb";
    //用车标题
    @Deprecated
    public static String FUQIANLA_TAXI_SUBJIECT = "用车支付";
    //付钱拉接口默认版本号
    @Deprecated
    public static String FUQIANLA_VERSION = "V2.1.1";
    //签名方式
    @Deprecated
    public static String FUQIANLA_RSA = "RSA";
    
    @Deprecated
    public static String FUQIANLA_MD5 = "MD5";

    //人民币
    public static String CNY = "cny";

    //业务类型 1:用车
    @Deprecated
    public static Integer FUQIANLA_TAXI = 3;

    //订单状态  支付状态：0-初始化、1-待支付、2-支付成功、3-支付失败、4-部分支付、5-支付失效、6－订单不存在
    public static Integer payDefault = 0;
    public static Integer payInit = 1;
    public static Integer paySuccess = 2;
    public static Integer payFail = 3;
    public static Integer payLess = 4;
    public static Integer payInvalid = 5;
    public static Integer payOrderNotExist = 6;
    public static Integer payOrderRefundIng = 7;
    public static Integer payOrderRefundSuccess = 8;
    public static Integer payOrderRefundFail = 9;
    public static Integer payOrderRefundError = 10;
    public static Integer payOrderRefundPartSuccess = 11;

    public static String ali_pay_app="ali_pay_app";
    public static String wx_pay_app="wx_pay_app";



    //支付回调状态
    // "0000":支付成功 “0002”:订单不存在
    public static String orderSuccessStatusCode = "0000";
    public static String orderNotExistStatusCode = "0002";


    //付钱拉交易状态
    //交易状态 01-初始、02-成功、03-失败、04-受理中
    public static String fqlInit = "01";
    public static String fqlSuccess = "02";
    public static String fqlFail = "03";
    public static String fqlPro = "04";

    //通知业务订单数据
    //'业务订单状态：0:默认 1:成功 2:失败';
    public static Integer taxiInit = 0;
    public static Integer taxiSuccess = 1;
    public static Integer taxiFail = 2;

    public static String  PRFUND_SUCCESS="REFUND_SUCCESS";
    public static String  PRFUND_ING="ING";
    public static String  PRFUND_TOTAL="TOTAL";
    public static String  PRFUND_REQUEST="refundrequest";
    public static String  PRFUND_REPONSE="refundreponse";

    public static String  PRFUND_STATUS="status";
    public static String  PRFUND_RETMSG="retMsg";
    public static String  PRFUND_REFUNDID="refundId";
    public static String  PRFUND_ORDERID="orderNo";
    public static String  PRFUND_ORDERAMOUNT="orderAmount";
    public static String  PRFUND_REFUNDNO="refundNo";
    public static String  PRFUND_TX_NO="refundTxNo";
    public static String  PRFUND_INFO="refundINfo";


    public static String REQ_USER = "user";
    public static String  KEY_NAME_TOKEN = "X-Auth-Token";
    public static String CLIENT_VERSION = "client-version";
    public static String CLIENT_TYPE = "client-type";

    /**
     * 员工端/服务端区分
     */
    public static String BETWEEN_TYPE = "betweenType";

    /**
     * 员工端
     */
    public static int BETWEEN_TYPE_EMPLOYEE = 0;

    /**
     * 管理端
     */
    public static int BETWEEN_TYPE_MANAGE = 1;


}
