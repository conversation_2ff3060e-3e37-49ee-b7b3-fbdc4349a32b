package com.fenbeitong.fenbeipay.core.constant.personpay;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by mac on 17/12/29.
 */
public enum CompanySwitchConstant {

    UNKNOWN(0, "未知"),
    NORMAL(1, "启用"),
    DISABLE(2, "禁用"),;

    private int key;
    private String value;

    CompanySwitchConstant(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static CompanySwitchConstant getEnum(Integer key) {
        if (key == null) {
            return UNKNOWN;
        }
        for (CompanySwitchConstant item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return UNKNOWN;
    }

    public static CompanySwitchConstant getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return UNKNOWN;
        }
        for (CompanySwitchConstant item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return UNKNOWN;
    }


}
