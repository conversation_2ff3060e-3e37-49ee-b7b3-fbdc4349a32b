package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.api.model.vo.cashier.PersonPayCommonVo;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2018年12月04日14:49:19
 */
@Data
public class CashierRefundReimburseQueryRespVo {

    /**
     * 退款状态
     */
    private Integer refundStatus;

    private String cashierTxnId;

    /**
     * 退款流水号
     */
    private String refundTxnId;

    /**
     * 售后场景逆向订单号
     */
    private String refundOrderId;

    /***
     * 分贝订单id
     */
    private String fbOrderId;
    /***
     * 1:因私订单 2:因公订单
     */
    private Integer accountType;

    /**
     * 企业支付|个人垫付
     * @see PayModelEnum
     */
    private Integer orderPaymentModel;

    private BigDecimal amountAll = BigDecimal.ZERO;

    private BigDecimal refundAmountAll = BigDecimal.ZERO;
    /**
     *   报销金额
     */
    private BigDecimal refundAmountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal refundAmountReimburseSelf = BigDecimal.ZERO;

    /**
      * @Description: 完成时间
      */
    private Date completeTime;


    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;

    /**
     * 子账户id
     */
    private String accountSubId;
}
