package com.fenbeitong.fenbeipay.core.model.dto.account;

import lombok.Data;

import java.io.Serializable;

/**
 * 线下充值-绑定账户信息
 * <AUTHOR>
 * @date 2021/11/22
 */
@Data
public class BindAccountInfoDTO implements Serializable {
    /**
     * 绑定账号-账户名称（企业名称）
     */
    private String accountName;
    /**
     * 绑定账号-银行账号
     */
    private String bankCardNo;
    /**
     * 绑定账号-银行名称
     */
    private String bankCardName;
}
