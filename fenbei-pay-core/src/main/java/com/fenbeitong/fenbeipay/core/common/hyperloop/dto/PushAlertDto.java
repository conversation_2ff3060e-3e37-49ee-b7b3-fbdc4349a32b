package com.fenbeitong.fenbeipay.core.common.hyperloop.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.luastar.swift.base.json.JsonUtils;
import lombok.*;

/**
 * 推送参数：手机客户端通知
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PushAlertDto {

    /**
     * 是否让友盟处理该消息，true友盟处理，false友盟头传给应用，应用处理
     */
    @Setter(value = AccessLevel.NONE)
    private boolean alert = true;

    /**
     * 业务类型
     */
    @JsonProperty("msg_type")
    private String msgType;

    /**
     * 消息体
     */
    private String msg;

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    private String userId;

    private String title;

    private String content;

    private String desc;

    public void setMsgObj(Object msg) {
        this.msg = JsonUtils.toJson(msg);
    }

}
