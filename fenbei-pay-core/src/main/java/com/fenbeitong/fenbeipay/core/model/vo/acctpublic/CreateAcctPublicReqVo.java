package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * @CreateDate: 2020年05月10日18:07:54
 * @Version: 4.0.0
 * 支付
 */
@Data
public class CreateAcctPublicReqVo extends AcctPublicBaseReqVo {


    /**
     * 实体卡：公司实体银行卡
     */
    @NotEmpty
    private String bankCardNo;

    /**
     * 实体卡：公司实体银行名称
     */
    @NotEmpty
    private String bankCardName;

    /**
     * 操作渠道：
     *@see OperationChannelType
     */
    @NotNull
    private Integer operationChannel;

}
