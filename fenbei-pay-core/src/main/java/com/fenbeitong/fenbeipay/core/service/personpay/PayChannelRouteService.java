package com.fenbeitong.fenbeipay.core.service.personpay;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PayChannelRouteMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PayChannelRoute;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2023-02-14 05:31:49 
*/
@Service
public class PayChannelRouteService {

	@Autowired
	private PayChannelRouteMapper routeMapper;
	
	/**
	 * 根据供应商查询支付通道
	 * @param vendorCode
	 * @return
	 */
	public List<PayChannelRoute> queryByVendorCode(String vendorCode) {
		List<PayChannelRoute> list = routeMapper.selectByVendorCode(vendorCode);
		return list;
	}
	
	/**
	 * 保存或更新支付通道
	 * @param list
	 * @return
	 */
	public boolean saveOrUpdatePayChannelRoute(List<PayChannelRoute> list) {
		if (CollectionUtils.isNotEmpty(list)) {
			int result = routeMapper.batchInsert(list);
			return result > 0;
		}
		return false;
	}
}
