package com.fenbeitong.fenbeipay.core.utils;

import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * 异步任务工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 18:14:58
 */
@Slf4j
public class FutureTaskUtil {


    private FutureTaskUtil() {

    }

    /**
     * 异步执行任务
     */
    @CanIgnoreReturnValue
    public static CompletableFuture<Void> runAsync(Runnable runnable, Executor executor) {
        return CompletableFuture.runAsync(() -> {
            runnable.run();
        }, executor);
    }


    /**
     * 异步执行任务，接收返回值
     */
    @CanIgnoreReturnValue
    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier, Executor executor) {
        return CompletableFuture.supplyAsync(() -> supplier.get(), executor);
    }

}
