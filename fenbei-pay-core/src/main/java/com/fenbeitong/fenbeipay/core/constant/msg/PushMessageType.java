package com.fenbeitong.fenbeipay.core.constant.msg;

public enum PushMessageType {

    UnKnow("0","应用首页"),
    ORDER_DETAIL("100", "订单详情"),
    TRANCTION_DETAIL("260", "订单详情"),
    UNCHECKING("212","未核销交易记录列表"),
    SYSTEM_MESSAGE("222","系统消息"),
    ;
    PushMessageType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static PushMessageType getEnum(String key) {
        if (key == null) {
            return null;
        }
        for (PushMessageType item : values()) {
            if (item.getKey().equalsIgnoreCase(key) ) {
                return item;
            }
        }
        return null;
    }

}
