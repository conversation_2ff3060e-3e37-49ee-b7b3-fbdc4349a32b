package com.fenbeitong.fenbeipay.core.service.i18;

import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.utils.SpringUtils;
import com.luastar.swift.i18n.locale.LocaleManager;
import org.springframework.stereotype.Component;

import java.util.Locale;
import java.util.ResourceBundle;

/**
 * @Description:
 * @Author: liyi
 * @Date: 2022/12/23 7:55 PM
 */
@Component
public class MessageService {
    public static MessageService me() {
        return SpringUtils.getBean(MessageService.class,"messageService");
    }


    public String getMessage(String key) {
        Locale locale = LocaleManager.getLocale();
        //LocaleInterceptor里设置的locale有问题，这里转换下
        locale = convertLocale(locale);

        try {
            return ResourceBundle.getBundle("i18n/messages", locale).getString(key);
        } catch (Exception e) {
            FinhubLogger.error("【国际化】获取文案失败", e);
            return key;
        }
    }

    /**
     * LocaleInterceptor里设置的locale有问题，这里转换下
     * @param locale
     * @return
     */
    private Locale convertLocale(Locale locale) {
        if (locale == null) {
            return Locale.CHINA;
        }
        String language = locale.getLanguage();
        FinhubLogger.info("i18n language: " + language);

        String[] languages = language.split("_");
        if (languages.length > 1) {
            locale = new Locale(languages[0], languages[1].toUpperCase());
        }

        return locale;
    }

}
