package com.fenbeitong.fenbeipay.core.utils.personpay;

import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;

public class Md5VerifyUtil {

    public static String getSign(Map<String, ?> objMap) {
        String md5Hex = null;
        try {
            // 得到待签名数据
            Map<String, ?> filterMap = paraFilter(objMap);
            String linkStr = createLinkString(filterMap);
            // 拼装md5串  md5.linkStr
            String templinkStr = linkStr + "&key=8BB418FCA8A480BC3E00365AE14148A2";
            md5Hex = DigestUtils.md5Hex(templinkStr.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return md5Hex;
    }

    public static String getSignByStr(String linkStr) {
        String md5Hex = null;
        try {
            // 拼装md5串  md5.linkStr
            String templinkStr = linkStr + "&key=8BB418FCA8A480BC3E00365AE14148A2";
            md5Hex = DigestUtils.md5Hex(templinkStr.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return md5Hex;
    }

    /**
     * 验证接收付钱拉通知签名入口
     *
     * @param objMap
     * @return
     * @throws Exception
     */
    public boolean verify(Map<String, ?> objMap) {
        boolean flag = false;
        String signKey = "d2d55ca892664009b8b8be550b1c1da9";
        try {
            String hexSign = String.valueOf(objMap.get("sign_info"));
            // 得到待签名数据
            Map<String, ?> filterMap = paraFilter(objMap);
            String linkStr = createLinkString(filterMap);
            // 拼装md5串  md5.linkStr
            String templinkStr = linkStr + signKey;
            String md5Hex = DigestUtils.md5Hex(templinkStr.getBytes("UTF-8"));
            // 验证签名后数据是否相同
            flag = hexSign.equalsIgnoreCase(md5Hex);
        } catch (Exception e) {
            //验证通知签名信息error
        }
        return flag;

    }

    /**
     * 除去数组中的空值和签名参数
     *
     * @param sArray 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    public static Map<String, ?> paraFilter(Map<String, ?> sArray) {
        Map<String, Object> result = new HashMap<String, Object>();
        if ((sArray == null) || (sArray.size() <= 0)) {
            return result;
        }
        for (String key : sArray.keySet()) {
            Object value = sArray.get(key);
            if ((value == null) || value.equals("") || key.equalsIgnoreCase("sign_info")
                    || key.equalsIgnoreCase("sign_type")) {
                continue;
            }
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, ?> m = (Map<String, ?>) value;
                result.put(key, paraFilter(m));
            } else if (value instanceof List) {
                continue;// 不应包含多集合数据
            } else {
                result.put(key, value);
            }
        }
        return result;
    }

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    public static String createLinkString(Map<String, ?> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        StringBuffer prestr = new StringBuffer("");
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object o = params.get(key);
            String value = String.valueOf(o);
            if (o instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, ?> m = (Map<String, ?>) o;
                value = "{" + createLinkString(m) + "}";
            }
            if (i == (keys.size() - 1)) {
                // 拼接时，不包括最后一个&字符
                prestr.append(key + "=" + value);
            } else {
                prestr.append(key + "=" + value + "&");
            }
        }
        return prestr.toString();
    }

}
