package com.fenbeitong.fenbeipay.core.aspect;

import com.fenbeitong.activity.api.service.fbb.model.dto.req.FbbLotteryDrawReqRPCDTO;
import com.fenbeitong.activity.api.service.fbb.service.lottery.IFbbLotteryActivityService;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierPayStatus;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> renfj
 * @date : 2019年05月05日11:15:39
 */
@Aspect
@Component
public class CashierPayResultAspect {

    @Autowired
    private IFbbLotteryActivityService iFbbLotteryActivityService;

    @Pointcut("@annotation(com.fenbeitong.fenbeipay.core.annotation.CashierPayResultAnnotation)")
    public void pointcut() {
    }

    @Before("pointcut()")
    public void before(JoinPoint joinPoint) {

    }

    @AfterReturning(pointcut = "pointcut()", returning = "returnVal")
    public void afterReturning(JoinPoint joinPoint, Object returnVal) {

        if(returnVal instanceof CashierCreatePayTradeRespVo){
            CashierCreatePayTradeRespVo respVo = (CashierCreatePayTradeRespVo)returnVal;
            if(OperationChannelType.isStereo(respVo.getOperationChannelType())) {
                return;
            }
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }

        if(returnVal instanceof CashierCreateTradeRespVo){
            CashierCreateTradeRespVo respVo = (CashierCreateTradeRespVo)returnVal;
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }

        if(returnVal instanceof CashierPayTradeRespVo){
            CashierPayTradeRespVo respVo = (CashierPayTradeRespVo)returnVal;
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }

        if(returnVal instanceof CashierOrderSettlement){
            CashierOrderSettlement cashier = (CashierOrderSettlement)returnVal;
            CashierPayTradeBaseRespVo respVo = new CashierPayTradeBaseRespVo();
            BeanUtils.copyProperties(cashier,respVo);
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }

        if(returnVal instanceof CashierPayThirdTradeRespVo){
            CashierPayThirdTradeRespVo respVo = (CashierPayThirdTradeRespVo)returnVal;
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }

        if(returnVal instanceof CashierPayTradeBaseRespVo){
            CashierPayTradeBaseRespVo respVo = (CashierPayTradeBaseRespVo)returnVal;
            if(CashierPayStatus.paySuccessRecord(respVo.getPayStatus())){
                cashierPaySuccess(joinPoint,respVo);
                return;
            }
        }
    }

    /**
     * 支付交易成功
     * @param joinPoint
     * @param respVo
     */
    private void cashierPaySuccess(JoinPoint joinPoint, CashierPayTradeBaseRespVo respVo) {
        //活动发币
        FbbLotteryDrawReqRPCDTO drawReqRPCDTO = new FbbLotteryDrawReqRPCDTO();
        BeanUtils.copyProperties(respVo,drawReqRPCDTO);
        try{
            // fixme renfj 分贝币关闭，二次上下开启
            // 异步调用抽奖结果
            FinhubLogger.info("开始执行抽奖"+ JsonUtils.toJson(drawReqRPCDTO));
            CompletableFuture.runAsync(() -> iFbbLotteryActivityService.executiveDraw(drawReqRPCDTO));
        }catch (Exception e){
            FinhubLogger.error("活动抽奖结果",e);
        }
    }

    /**
     * TODO 支付交易取消
     * @param joinPoint
     * @param payTradeRespVo
     */
//    private void cashierHadCancel(JoinPoint joinPoint,CashierPayTradeBaseRespVo payTradeRespVo) {
//
//    }

    /**
     * TODO 支付交易失效
     * @param joinPoint
     * @param payTradeRespVo
     */
//    private void cashierHadInvalid(JoinPoint joinPoint,CashierPayTradeBaseRespVo payTradeRespVo) {
//
//    }

}
