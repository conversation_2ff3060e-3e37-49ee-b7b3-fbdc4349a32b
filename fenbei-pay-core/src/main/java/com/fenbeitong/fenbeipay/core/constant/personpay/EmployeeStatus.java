package com.fenbeitong.fenbeipay.core.constant.personpay;

import org.apache.commons.lang3.StringUtils;

/**
 * 员工状态
 */
public enum EmployeeStatus {

    UNKNOWN(0, "未知"),
    ACTIVE(1, "启用"),
    INACTIVE(2, "禁用"),
    PAUSE(3, "暂停"),
    SOFT_DELETE(4, "软删除"),;

    private final int key;
    private final String value;

    EmployeeStatus(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static EmployeeStatus getEnum(Integer key) {
        if (key == null) {
            return UNKNOWN;
        }
        for (EmployeeStatus item : values()) {
            if (item.getKey() == key) {
                return item;
            }
        }
        return UNKNOWN;
    }

    public static EmployeeStatus getEnum(String value) {
        if (StringUtils.isEmpty(value)) {
            return UNKNOWN;
        }
        for (EmployeeStatus item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return UNKNOWN;
    }
    public static Boolean isEnable(int key){
        return key==ACTIVE.key;
    }
}
