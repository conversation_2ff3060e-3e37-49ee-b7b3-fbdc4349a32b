package com.fenbeitong.fenbeipay.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;

import static java.math.BigDecimal.ROUND_HALF_DOWN;

/**
 * @program: fenbei-pay
 * @author: wanghua
 * @create: 2018-11-09 10:05
 **/
public class BigDecimalUtils {

    private static final Logger logger = LoggerFactory.getLogger(BigDecimalUtils.class);

    private static final String SPOT = ".";

    /**
     * string 类型价格转 BigDecimal
     *
     * @param price
     * @return BigDecimal
     */
    public static BigDecimal adapt(String price) {
        if (StringUtils.isBlank(price)) {
            return BigDecimal.ZERO;
        }

        try {
            return new BigDecimal(StringUtils.trim(price));
        } catch (Throwable t) {
            logger.warn("未知的价格类型: {}", price);
            return BigDecimal.ZERO;
        }
    }


    /***
     * 是存在价格
     * @param source
     * @return 存在true
     */
    public static boolean hasPrice(BigDecimal source) {
        return source != null && source.compareTo(BigDecimal.ZERO) > 0;
    }

    /***
     * 价格是不相等
     * @param price1
     * @param price2
     * @return true
     */
    public static boolean differentPrice(BigDecimal price1, BigDecimal price2) {
        return price1.compareTo(price2) != 0;
    }


    /***
     * 价格是相加
     * @param price1
     * @param price2
     * @return
     */
    public static BigDecimal addPrice(BigDecimal price1, BigDecimal price2) {
        return price1.add(price2);
    }

    public static BigDecimal sumPrice(BigDecimal price1, BigDecimal price2) {
        return (Objects.isNull(price1) ? BigDecimal.ZERO : price1).add(Objects.isNull(price2) ? BigDecimal.ZERO : price2);
    }

    /**
     * 不存在价格
     *
     * @param source
     * @return 不存在true
     */
    public static boolean hasNoPrice(BigDecimal source) {
        return !hasPrice(source);
    }

    public static String formatZero() {
        return BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_DOWN).toPlainString();
    }

    public static String format(BigDecimal source) {
        if (source == null) {
            return "null";
        }
        return source.setScale(2, BigDecimal.ROUND_DOWN).toPlainString();
    }

    /**
     * 元转分
     *
     * @param source
     * @return
     */
    public static BigDecimal yuanToFen(BigDecimal source) {
        if (source == null) {
            return BigDecimal.ZERO;
        }
        return source.multiply(new BigDecimal(100)).setScale(0, ROUND_HALF_DOWN);
    }

    /**
     * 分转元
     *
     * @param source
     * @return
     */
    public static BigDecimal fenToYuan(BigDecimal source) {
        if (source == null) {
            return BigDecimal.ZERO;
        }
        return source.divide(new BigDecimal(100), 2, ROUND_HALF_DOWN);

    }

    /**
     * 数据转换字符串
     *
     * @param source
     * @return
     */
    public static String formatDecimal(BigDecimal source) {
        DecimalFormat df = new DecimalFormat("#0.00");
        df.setRoundingMode(RoundingMode.HALF_DOWN);
        return df.format(source);

    }

    public static boolean nonInteger(BigDecimal source) {
        if (source == null) {
            return true;
        }
        String str = source.toString();
        if (str.contains(SPOT)) {
            String substring = str.substring(str.indexOf(SPOT) + 1);
            BigDecimal bigDecimal = new BigDecimal(substring);
            if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }
        return false;

    }

}
