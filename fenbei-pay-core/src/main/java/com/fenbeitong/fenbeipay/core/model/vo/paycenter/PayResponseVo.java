package com.fenbeitong.fenbeipay.core.model.vo.paycenter;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.PayInfoDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by mac on 2018/8/13.
 */
public class PayResponseVo extends BaseVo {

    private String orderId;
    private BigDecimal amount;
    private String currentAccountFlowId;
    private String supplierAccountFlowId;
    private List<PayInfoDto> payList;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrentAccountFlowId() {
        return currentAccountFlowId;
    }

    public void setCurrentAccountFlowId(String currentAccountFlowId) {
        this.currentAccountFlowId = currentAccountFlowId;
    }

    public String getSupplierAccountFlowId() {
        return supplierAccountFlowId;
    }

    public void setSupplierAccountFlowId(String supplierAccountFlowId) {
        this.supplierAccountFlowId = supplierAccountFlowId;
    }

    public List<PayInfoDto> getPayList() {
        return payList;
    }

    public void setPayList(List<PayInfoDto> payList) {
        this.payList = payList;
    }
}