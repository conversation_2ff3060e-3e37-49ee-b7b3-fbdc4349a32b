package com.fenbeitong.fenbeipay.core.utils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Maps;
import com.luastar.swift.base.net.HttpClientUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**  
 * @Description: 
 * <AUTHOR>
 * @date 2024-03-11 04:49:43 
*/

@Component
public class NewOssHandler {

	@Value("${host.harmony}")
    private String harmonyUrl;
	
	private static final String UPLOAD_URI = "/harmony/upload/oss";
		
	private static final String DOWNLOAD_URL_TIMEPLATE = "/harmony/download/oss/by/key?key=%s";
	
	private static final String GET_FILE_SIZE_TIMEPLATE = "/harmony/upload/oss/file/size/by/key?key=%s";
	
	/**
	 * 根据key获取文件大小
	 * @param key
	 * @return
	 */
	public long getFileLength(String key) {
		if (StringUtils.isNotBlank(key) && key.startsWith("/") && key.length() > 1) {
			key = key.substring(1);
		}
		String url = harmonyUrl + String.format(GET_FILE_SIZE_TIMEPLATE, key);
		String resp = HttpClientUtils.get(url);
		if (StringUtils.isBlank(resp)) {
			throw new FinhubException(500, "获取文件大小失败");
		}
		FinhubLogger.info("【获取OSS文件长度】响应->{}", resp);
		GetFileLengthRespDTO fileLength = JSON.parseObject(resp, GetFileLengthRespDTO.class);
		if (Objects.nonNull(fileLength) && Objects.equals(0, fileLength.getCode()) && Objects.nonNull(fileLength.getData())) {
			return fileLength.getData();
		}
		return -1;
	}
	
	/**
	 * 重载方法
	 * @param key
	 * @return
	 */
	public File downloadFileFromOSS(String key) {
		return downloadFileFromOSS(key, (String)null);
	}
	
	/**
	 * 从oss下载文件
	 * @param key
	 * @param filePath
	 * @return
	 */
	public File downloadFileFromOSS(String key, String filePath) {
		File file;
		if (StringUtils.isBlank(filePath)) {
			String tempPath = System.getProperty("java.io.tmpdir") + File.separator;
	        String tempFileName = String.format("OSS_%s", ObjectId.get().toString());
	        file = new File(tempPath, tempFileName);
		} else {
			file = new File(filePath);
		}
		
		return downloadFileFromOSS(key, file);
	}
	
	/**
	 * 从oss下载文件
	 * @param key
	 * @param filePath
	 * @return
	 */
	public File downloadFileFromOSS(String key, File file) {		
		String url = harmonyUrl + String.format(DOWNLOAD_URL_TIMEPLATE, key);
		try (ReadableByteChannel byteChannel = Channels.newChannel(new URL(url).openStream());
				FileOutputStream os = new FileOutputStream(file);
				FileChannel channel = os.getChannel()) {
			channel.transferFrom(byteChannel, 0, Long.MAX_VALUE);
		} catch (Exception e) {
			FinhubLogger.error("【从OSS下载文件】异常->{}", e.getMessage(), e);
			throw new FinhubException(500, "文件下载失败");
		}
		return file;
	}
	
	/**
	 * 上传文件到oss
	 * @param file
	 * @param userId
	 * @param orderId
	 * @param fileName
	 * @param bizCode
	 * @return
	 */
	public String uploadFileToOss(File file, String userId, String orderId, String fileName, String bizCode) {
		try {
			BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
			return uploadFileToOss(bis, userId, orderId, fileName, bizCode);
		} catch (FileNotFoundException e) {
			throw new FinhubException(500, "文件上传失败");
		}
	}
	/**
	 * 上传文件到oss
	 * @param inputStream
	 * @param userId
	 * @param orderId
	 * @param fileName
	 * @param bizCode
	 * @return
	 */
	public String uploadFileToOss(InputStream inputStream, String userId, String orderId, String fileName, String bizCode) {
        try (InputStream is = inputStream) {
            Map<String, Object> paramMap = Maps.newLinkedHashMap();
            Map<String, String> fileNameMap = Maps.newLinkedHashMap();
            paramMap.put("busi_code", bizCode);
            paramMap.put("user_id", userId);
            paramMap.put("is_save_name", 1);
            paramMap.put("file1", is);
            fileNameMap.put("file1", fileName);
            String result = HttpClientUtils.postMultipartForm(harmonyUrl + UPLOAD_URI, paramMap, null, fileNameMap);
            FinhubLogger.info("【上传文件到OSS】返回结果为{}", result);
            UploadResponseDTO uploadResp = JSON.parseObject(result, UploadResponseDTO.class);
            if (uploadResp.getCode() == 0 && CollectionUtils.isNotEmpty(uploadResp.getData())) {
            	OssDataDTO dataDto = uploadResp.getData().get(0);
                return dataDto.getUrl();
            }
            return null;
        } catch (Exception e) {
            FinhubLogger.info("【上传文件到OSS】异常userId:{}, orderId:{},fileName:{},bizCode:{}", userId, orderId, fileName, bizCode, e);
            throw new FinhubException(500, "文件上传失败");
        }
	}
	
	@Data
	public static class OssDataDTO {
		private String bucket;
		
		private String host;
		
		private String path;
		
		private String url;
		
		private String fileName;
	}
	
	@Data
	@EqualsAndHashCode(callSuper = false)
	public static class UploadResponseDTO extends PlatformRespDTO{
		private List<OssDataDTO> data;
	}
	
	@Data
	@EqualsAndHashCode(callSuper = false)
	public static class GetFileLengthRespDTO extends PlatformRespDTO {
		private Long data;
	}
	
	@Data
	public static class PlatformRespDTO {
		private String request_id;
		
		private Integer code;
		
		private String msg;
		
		private Integer type;
	}
	
}
