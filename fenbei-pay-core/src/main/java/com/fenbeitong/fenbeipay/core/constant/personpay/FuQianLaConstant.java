package com.fenbeitong.fenbeipay.core.constant.personpay;

import com.luastar.swift.base.config.PropertyUtils;

/**
 * Created by mac on 17/12/21.
 */
public class FuQianLaConstant {

    /**
     * 付钱拉支付相关参数
     */
    public static String privateKey = PropertyUtils.getString("fuqianla.private.key", "");
    public static String publicKey = PropertyUtils.getString("fuqianla.public.key", "");
    public static String appid = PropertyUtils.getString("fuqianla.appid", "");
    public static String notify_url = PropertyUtils.getString("fuqianla.notify_url", "");
    public static String host_hyperloop = PropertyUtils.getString("api.hyperloop.host", "");
    public static String host_car_biz = PropertyUtils.getString("host.car.biz.host", "");
    public static String hyperloop_order_check = host_car_biz + "/internal/taxi/order/pre_personal_pay";
    public static String hyperloop_order_finish = host_car_biz + "/internal/taxi/order/personal_pay";
    //付钱拉域名
    public static String fuqianla_host = "https://api.fuqian.la";
    //支付订单查询
    public static String fuqianla_singquery_url = fuqianla_host + "/services/order/singleQuery";
    //退款请求
    public static String fuqianla_refund_url = fuqianla_host + "/services/order/refund";
    //环境
    public static String fuqianla_fenbeitong_env = PropertyUtils.getString("fuqianla.fenbeitong.env", "bd");


}
