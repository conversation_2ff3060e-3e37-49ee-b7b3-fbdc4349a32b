package com.fenbeitong.fenbeipay.core.utils.multimain;

import org.apache.commons.lang3.StringUtils;

public class MultiMainPayUtils {
    /**
     * 获取跨主体支付的企业ID
     * @return
     */
    public static String getPaymentCompanyId(String companyId,String paymentCompanyId){
        //是否跨主体
        if (StringUtils.isNotEmpty(paymentCompanyId)
                && !paymentCompanyId.equals(companyId)){
            return paymentCompanyId;
        }
        return companyId;
    }

    public static boolean isMultiMainPay(String companyId,String paymentCompanyId){
        //是否跨主体
        if (StringUtils.isNotEmpty(paymentCompanyId)
                && !paymentCompanyId.equals(companyId)){
            return true;
        }
        return false;
    }
}
