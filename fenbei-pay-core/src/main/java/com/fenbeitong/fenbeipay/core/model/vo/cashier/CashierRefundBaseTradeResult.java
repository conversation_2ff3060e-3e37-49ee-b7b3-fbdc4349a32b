package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.finhub.common.constant.PayModelEnum;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 各种支付能力退款结果
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
public class CashierRefundBaseTradeResult {

    private String fbOrderId;

    private String cashierTxnId;

    private String refundTxnId;
    /**
     * 售后场景逆向订单号
     */
    private String refundOrderId;

    private Integer refundStatus;

    /**
     * 企业支付|个人垫付
     * @see PayModelEnum
     */
    private Integer orderPaymentModel;


    private BigDecimal publicRefundAmount = BigDecimal.ZERO;
    private BigDecimal companyRefundAmount = BigDecimal.ZERO;
    private BigDecimal personalRefundAmount = BigDecimal.ZERO;
    private BigDecimal thirdRefundAmount = BigDecimal.ZERO;
    private BigDecimal fbbRefundAmount = BigDecimal.ZERO;
    private BigDecimal fbqRefundAmount = BigDecimal.ZERO;
    private BigDecimal fbqRefundIndividualAmount = BigDecimal.ZERO;
    private BigDecimal fbqRefundRedcouponAmount = BigDecimal.ZERO;
    private BigDecimal redcouponRefundAmount = BigDecimal.ZERO;
    private Integer fbqRefundCount = 0;
    private String refundThirdTxnId;
    private BigDecimal voucherInvoiceRefundAmount;
    private BigDecimal voucherOrderInvoiceRefundAmount;
    /**
     *   报销金额
     */
    private BigDecimal refundAmountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal refundAmountReimburseSelf = BigDecimal.ZERO;

    /**
     * 个人银行卡退款金额
     */
    private BigDecimal bankPersonalRefundAmount = BigDecimal.ZERO;


    @Builder
    public CashierRefundBaseTradeResult(String fbOrderId, String cashierTxnId, String refundTxnId, String refundOrderId, Integer refundStatus, BigDecimal publicRefundAmount, BigDecimal companyRefundAmount, BigDecimal personalRefundAmount, BigDecimal thirdRefundAmount, BigDecimal fbbRefundAmount, BigDecimal fbqRefundAmount, BigDecimal fbqRefundIndividualAmount, BigDecimal fbqRefundRedcouponAmount, BigDecimal redcouponRefundAmount, Integer fbqRefundCount, String refundThirdTxnId, BigDecimal voucherInvoiceRefundAmount, BigDecimal voucherOrderInvoiceRefundAmount) {
        this.fbOrderId = fbOrderId;
        this.cashierTxnId = cashierTxnId;
        this.refundTxnId = refundTxnId;
        this.refundOrderId = refundOrderId;
        this.refundStatus = refundStatus;
        this.publicRefundAmount = publicRefundAmount;
        this.companyRefundAmount = companyRefundAmount;
        this.personalRefundAmount = personalRefundAmount;
        this.thirdRefundAmount = thirdRefundAmount;
        this.fbbRefundAmount = fbbRefundAmount;
        this.fbqRefundAmount = fbqRefundAmount;
        this.fbqRefundIndividualAmount = fbqRefundIndividualAmount;
        this.fbqRefundRedcouponAmount = fbqRefundRedcouponAmount;
        this.redcouponRefundAmount = redcouponRefundAmount;
        this.fbqRefundCount = fbqRefundCount;
        this.refundThirdTxnId = refundThirdTxnId;
        this.voucherInvoiceRefundAmount = voucherInvoiceRefundAmount;
        this.voucherOrderInvoiceRefundAmount = voucherOrderInvoiceRefundAmount;
    }

    @Builder
    public CashierRefundBaseTradeResult(String fbOrderId, String cashierTxnId, String refundTxnId, String refundOrderId, Integer refundStatus, Integer orderPaymentModel, BigDecimal publicRefundAmount, BigDecimal companyRefundAmount, BigDecimal personalRefundAmount, BigDecimal thirdRefundAmount, BigDecimal fbbRefundAmount, BigDecimal fbqRefundAmount, BigDecimal fbqRefundIndividualAmount, BigDecimal fbqRefundRedcouponAmount, BigDecimal redcouponRefundAmount, Integer fbqRefundCount, String refundThirdTxnId, BigDecimal voucherInvoiceRefundAmount, BigDecimal voucherOrderInvoiceRefundAmount, BigDecimal refundAmountReimburseCompany, BigDecimal refundAmountReimburseSelf) {
        this.fbOrderId = fbOrderId;
        this.cashierTxnId = cashierTxnId;
        this.refundTxnId = refundTxnId;
        this.refundOrderId = refundOrderId;
        this.refundStatus = refundStatus;
        this.orderPaymentModel = orderPaymentModel;
        this.publicRefundAmount = publicRefundAmount;
        this.companyRefundAmount = companyRefundAmount;
        this.personalRefundAmount = personalRefundAmount;
        this.thirdRefundAmount = thirdRefundAmount;
        this.fbbRefundAmount = fbbRefundAmount;
        this.fbqRefundAmount = fbqRefundAmount;
        this.fbqRefundIndividualAmount = fbqRefundIndividualAmount;
        this.fbqRefundRedcouponAmount = fbqRefundRedcouponAmount;
        this.redcouponRefundAmount = redcouponRefundAmount;
        this.fbqRefundCount = fbqRefundCount;
        this.refundThirdTxnId = refundThirdTxnId;
        this.voucherInvoiceRefundAmount = voucherInvoiceRefundAmount;
        this.voucherOrderInvoiceRefundAmount = voucherOrderInvoiceRefundAmount;
        this.refundAmountReimburseCompany = refundAmountReimburseCompany;
        this.refundAmountReimburseSelf = refundAmountReimburseSelf;
    }
}
