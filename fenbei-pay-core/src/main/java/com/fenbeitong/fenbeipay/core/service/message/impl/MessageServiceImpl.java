package com.fenbeitong.fenbeipay.core.service.message.impl;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.eventbus.event.common.IEvent;
import com.fenbeitong.eventbus.util.EventBus;
import com.fenbeitong.fenbeipay.core.common.hyperloop.HyperloopHttpService;
import com.fenbeitong.fenbeipay.core.common.hyperloop.dto.PushAlertDto;
import com.fenbeitong.fenbeipay.core.service.message.MessageService;
import com.fenbeitong.fenbeipay.core.service.message.dto.CommonPushDto;
import com.fenbeitong.fenbeipay.core.utils.notice.SmsContract;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.net.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 推送服务
 **/
@Slf4j
@Service
public class MessageServiceImpl<T> implements MessageService<T> {


    @Autowired
    private HyperloopHttpService hyperloopHttpService;

    @Value("${host.harmony}")
    private String harmonyHost;

    /**
     * 推送到友盟
     *
     * @param dto
     */
    @Override
    public void pushAlertMsg(PushAlertDto dto) throws IOException {
        FinhubLogger.info("发送友盟信息：{}", dto);
        Object o = hyperloopHttpService.push(dto).execute();
        FinhubLogger.info(o.toString());
    }

    /**
     * 推送Event,到kafka
     *
     * @param iEvent
     */
    @Override
    public void pushEventMsg(IEvent iEvent) {
        try {
            FinhubLogger.info("发送event消息：{}", iEvent);
            EventBus.publish(iEvent);
        } catch (Exception e) {
            FinhubLogger.error("pushEventMsg Exception", e);
        }
    }


    @Override
    public void pushSMS(SmsContract msgBody) throws IOException {
        FinhubLogger.info("发送短信信息：{}", msgBody.toString());
        HttpClientUtils.postBody(harmonyHost + "/v1/sms", JSONObject.toJSONString(msgBody));
    }

    @Override
    public boolean pushMsgAll(T entity, CommonPushDto pushDTO) {
        if (pushDTO.isPushAlert()) {
            PushAlertDto pushAlertDto = pushDTO.getPushAlertDto();
            try {
                pushAlertMsg(pushAlertDto);
            } catch (IOException e) {
                FinhubLogger.error("pushAlertMsg Error", e);
            }
        }

        if (pushDTO.isPushEvent()) {
            pushEventMsg(pushDTO.getIEvent());
        }
        if (pushDTO.isPushSms()) {
            try {
                pushSMS(pushDTO.getMsgBody());
            } catch (IOException e) {
                FinhubLogger.error("pushSMS Error", e);
            }
        }
        return true;
    }

}
