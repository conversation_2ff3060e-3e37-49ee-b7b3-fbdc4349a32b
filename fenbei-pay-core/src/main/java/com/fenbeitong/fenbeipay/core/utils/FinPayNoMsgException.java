package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;

/**
 * 为了减少，FinhubException(Integer code, Integer type, String msg) 在逻辑中太长代码，封装此异常，
 * 接受GlobalResponseCode的枚举
 */
public class FinPayNoMsgException extends FinhubException {

    public FinPayNoMsgException(GlobalResponseCode globalResponseCode) {
        super(globalResponseCode.getCode(),globalResponseCode.getType(),globalResponseCode.getMsg(),globalResponseCode.getTitle());
    }
}
