package com.fenbeitong.fenbeipay.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Maps;
import com.luastar.swift.base.config.PropertyUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.http.server.HttpRequest;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author : FQ
 * Date : 2018/8/18 下午2:50
 * Desc ：
 */
public class CommonUtils {
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);

    public static final String HYPER_LOOP_URL = PropertyUtils.getString("api.hyperloop.host", "");
    public static final String SAAS_URL = PropertyUtils.getString("api.saas.host", "");
    public static final String USER_CENTER_URL = PropertyUtils.getString("api.usercenter.url", "");
    public static final String MAIL_URL = PropertyUtils.getString("api.harmony.host");
    public static final String DINNER_URL = PropertyUtils.getString("api.dinner.url");
    public static final String AIR_TICKET_INQUIRY_CODE = PropertyUtils.getString("air.ticket.inquiry.code");
    public static final String AIR_TICKET_INQUIRY_NAME = PropertyUtils.getString("air.ticket.inquiry.name");
    public static final String API_PROFILE_NAME = PropertyUtils.getString("api.profile.name");
    public static final String API_PAY_WHITE_LIST = PropertyUtils.getString("air.pay.white.list");
    public static final String APP_GATE_URL = PropertyUtils.getString("api.app.gate.url");
    public static final String PURCHASE_TICKET_ORDER_CODE = PropertyUtils.getString("purchase.ticket.order.code");
    public static final String PURCHASE_TICKET_ORDER_NAME = PropertyUtils.getString("purchase.ticket.order.name");
    public static final String AIR_BIZ_URL = PropertyUtils.getString("api.air.biz.url");

    /**
     * 校验Post请求参数
     *
     * @param request
     * @return
     */
    public static Map<String, Object> analyzeBody(HttpRequest request) {
        String requestBody = request.getBody();
        if (StringUtils.isEmpty(requestBody)) {
            logger.info("请求参数body体为空！");
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        Map<String, Object> paramMap = JsonUtils.toObj(requestBody, HashMap.class);
        if (MapUtils.isEmpty(paramMap)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return paramMap;
    }

    /**
     * 校验get请求必填参数
     */
    public static void analyzeString(String... obj) {
        int i = 0;
        for (String temp : obj) {
            if (StringUtils.isEmpty(temp)) {
                logger.info("第{}个参数异常>>>>>>>>", i);
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
            }
            i++;
        }
    }

    /**
     * 校验接口返回数据
     *
     * @param result
     * @return
     */
    public static Map<String, Object> analyzeResponse(String result) {
        if (StringUtils.isEmpty(result)) {
            logger.info("[调用外部接口返回值为NULL]");
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        Map<String, Object> obj = JsonUtils.toObj(result, HashMap.class);
        if (MapUtils.isEmpty(obj)) {
            logger.info("[调用外部接口返回值的结果集为]{}", result);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return obj;
    }

    /**
     * 校验接口返回数据
     * 增加报错信息
     *
     * @param result
     * @return
     */
    public static Map<String, Object> analyzeResponseV2(String result, String url) {
        if (StringUtils.isEmpty(result)) {
            String msg = "[" + url + "]接口返回值为NULL";
            logger.info(msg);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        Map<String, Object> obj = JsonUtils.toObj(result, HashMap.class);
        if (MapUtils.isEmpty(obj)) {
            String msg = url + "接口返回值的结果集为{}" + result;
            logger.info(msg);
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return obj;
    }

    /**
     * 校验单个参数是否为空或者是否为null
     *
     * @param str 需要校验的参数
     * @return
     */
    public static String verifyParam(String str) {
        if (StringUtils.isEmpty(ObjUtils.toString(str))) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return str;
    }

    /**
     * 校验单个参数是否为空或者是否为null
     *
     * @param obj 需要校验的参数
     * @return
     */
    public static Object analyzeObject(Object obj) {
        if (obj == null || "".equals(obj)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return obj;
    }




    /**
     * 给字符串去空格
     */
    public static String trimString(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        return str.trim();
    }

    /**
     * 验证邮箱
     *
     * @param email
     * @return
     */
    public static boolean checkEmail(String email) {
        boolean flag = false;
        try {
            String check = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
            Pattern regex = Pattern.compile(check);
            Matcher matcher = regex.matcher(email);
            flag = matcher.matches();
        } catch (Exception e) {
            flag = false;
        }
        return flag;
    }

    /**
     * 金额格式化
     *
     * @param decimal
     * @return
     */
    public static String decimalFormat(BigDecimal decimal) {
        if (Objects.isNull(decimal)) {
            return "";
        }
        DecimalFormat df = new DecimalFormat("###,##0.00");
        return df.format(decimal);
    }

    public static String decimalFormatStr(BigDecimal decimal) {
        if (Objects.isNull(decimal)) {
            decimal = BigDecimal.ZERO;
        }
        NumberFormat nf = NumberFormat.getCurrencyInstance(Locale.CHINA);
        return nf.format(decimal);
    }

    /**
     * decimal转百分比
     *
     * @param decimal
     * @return
     */
    public static String decimalFormatPercent(BigDecimal decimal) {
        if (Objects.isNull(decimal)) {
            decimal = BigDecimal.ZERO;
        }
        NumberFormat percent = NumberFormat.getPercentInstance();
        percent.setMaximumFractionDigits(2);
        return percent.format(decimal.doubleValue()).toString();
    }

    /**
     * 转换请求体参数
     *
     * @param body 请求体
     * @return
     */
    public static Map<String, Object> coverRequestBodyToMap(String body) {
        if (StringUtils.isEmpty(body)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "请求体不能为空");
        }
        Map<String, Object> ObjMap = JsonUtils.toObj(body, Map.class);
        if (MapUtils.isEmpty(ObjMap)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "参数转换错误");

        }
        return ObjMap;
    }

    public static Long timeDifference(Object datetime) {
        if (datetime == null) {
            return null;
        }
        if (datetime instanceof Date) {
            return Duration.between(((Date) datetime).toInstant(), new Date().toInstant()).toMinutes();
        } else if (datetime instanceof String) {
            Date time = DateUtils.parse(datetime.toString(), DateUtils.FORMAT_TIME_WITH_BAR);
            return Duration.between(time.toInstant(), new Date().toInstant()).toMinutes();
        } else {
            return null;
        }
    }

    /**
     * 校验多个参数是否为空或者是否为null
     *
     * @param objects 需要校验的参数
     * @return
     */
    public static void analyzeObjects(Object... objects) {
        int i = 1;
        for (Object obj : objects) {
            if (obj == null || Objects.equals(obj, "")) {
                throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
            }
            i++;
        }
    }

    public static Map<String,Object> getBuyBodyParams(String requestBody) {
        JSONObject object= JSON.parseObject(requestBody);
        Object user_id =  object.get("user_id");
        Object company_id =object.get("company_id");
        Object number = object.get("number");
        Object pay_way = object.get("pay_way");
        Object remark =  object.get("remark");
        analyzeObjects(user_id, company_id, number, pay_way);
        Map<String,Object> params= Maps.newHashMap();

        params.put("user_id",user_id);
        params.put("company_id",company_id);
        params.put("number",number);
        params.put("pay_way",pay_way);
        params.put("remark",remark);
        return params;
    }

    public static Map<String,Object> getDispatchBodyParams(String requestBody) {
        JSONObject object= JSON.parseObject(requestBody);

        Object company_id =  object.get("company_id");
        Object user_id = object.get("user_id");
        Object number = object.get("number");
        Object reason = object.get("reason");
        Object remark = object.get("remark");
        Object idList = object.get("idList");
        Object type =  object.get("type");

        analyzeObjects(user_id, company_id, number, reason, type);
        Boolean all = (Boolean) type;
        if(!all){
            analyzeObjects(idList);
        }
        Map<String,Object> params= Maps.newHashMap();
        params.put("company_id",company_id);
        params.put("user_id",user_id);
        params.put("number",number);
        params.put("reason",reason);
        params.put("remark",remark);
        params.put("type",type);
        params.put("idList",idList);

        return params;
    }

    public static Map<String,Object> getRecallBodyParams(String requestBody) {
        JSONObject object= JSON.parseObject(requestBody);

        Object company_id =  object.get("company_id");
        Object user_id = object.get("user_id");
        Object order_id = object.get("order_id");
        Object reason = object.get("reason");
        Object idList = object.get("idList");
        Object type =  object.get("type");

        analyzeObjects(user_id, company_id, order_id, reason, type);
        Boolean all = (Boolean) type;
        if(!all){
            analyzeObjects(idList);
        }
        Map<String,Object> params= Maps.newHashMap();
        params.put("company_id",company_id);
        params.put("user_id",user_id);
        params.put("order_id",order_id);
        params.put("reason",reason);
        params.put("type",type);
        params.put("idList",idList);

        return params;
    }



}
