package com.fenbeitong.fenbeipay.core.service.file;

import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.tools.utils.BeetlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class BaseTemplateService {


    /**
     *
     * @param templateFile
     * @param params
     * @return
     * @throws Exception
     */
    public String renderTemplate(String templateFile, Map<String, Object> params) throws Exception {
        BeetlUtils beetlUtils = new BeetlUtils();
        beetlUtils.setTemplate("template/"+templateFile);
        if(ObjUtils.isNotEmpty(params)) {
            params.keySet().forEach(key -> beetlUtils.binding(key, params.get(key)));
        }
        return beetlUtils.toText();
    }
}
