package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: fenbei-pay
 * @description: 退款详情查询
 * @author: wanghua
 * @create: 2018-11-28 11:54
 **/
@Data
public class CashierBatchRefundQueryRespVo {

    /***
     * 收银台支付流水状态14 支付交易已创建，企业预支付成功" 13 等待三方支付中 2 支付已成功 20 交易已完成
     */
    private int refundStatus;
    /***
     * 分贝订单id
     */
    private String fbOrderId;
    /***
     * 收银台支付流水号
     */
    private String cashierTxnId;

    private String refundTxnId;

    /**
     * 售后场景逆向订单号
     */
    private String refundOrderId;

    /***
     * 第3方支付流水号
     */
    private String refundThirdTxnId;
    /***
     * 因公付款金额
     */
    private BigDecimal refundAmountPublic = BigDecimal.ZERO;

    /**
     * 企业退款金额
     */
    private BigDecimal refundAmountCompany;

    /**
     * 红包券退款金额
     */
    private BigDecimal refundAmountRedcoupon;

    /***
     * 因私付款金额
     */
    private BigDecimal refundAmountPersonal = BigDecimal.ZERO;

    /**
     * 分贝币付款金额
     */
    private BigDecimal refundAmountFbb = BigDecimal.ZERO;

    /**
     * 分贝券付款金额(单位：分)
     */
    private BigDecimal refundAmountVoucher = BigDecimal.ZERO;

    /**
     * 个人账户分贝券(单位：分)
     */
    private BigDecimal refundAmountVoucherIndividual = BigDecimal.ZERO;

    /**
     * 红包券账户分贝券(单位：分)
     */
    private BigDecimal refundAmountVoucherRedcoupon = BigDecimal.ZERO;


    /**
     * 分贝券-先开票退款金额(单位：分)
     */
    private BigDecimal refundAmountVoucherInvoice;

    /**
     * 分贝券-后开票(订单开票金额)退款金额(单位：分)
     */
    private BigDecimal refundAmountVoucherOrderInvoice;

    /**
     * 三方支付(单位：分)
     */
    private BigDecimal refundAmountThird = BigDecimal.ZERO;

    /**
     *   报销金额
     */
    private BigDecimal refundAmountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal refundAmountReimburseSelf = BigDecimal.ZERO;

    /***
     * 支付渠道
     */
    private String thirdPayChannel="";


    /**
     * 每次支付只能使用一个企业的分贝券
     */
    private String voucherCompanyId;

    /**
     * 完成时间
     */
    private Date completeTime;



    /**
     * 业务模式:1.pop 2托管 3采销
     * @see com.fenbeitong.finhub.common.constant.BusinessModeEnum
     */
    private Integer businessMode;

    /**
     * 是否提供发票:1提供 0不提供
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideStatusEnum
     */
    private Integer invoiceProvideStatus;

    /**
     * 开票类型
     * @see com.fenbeitong.finhub.common.constant.SceneInvoiceTypeEnum
     */
    private Integer sceneInvoiceType;

    /**
     * 开票方类型
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideTypeEnum
     */
    private Integer invoiceProvideType;

    /**
     * 发票供应商名称
     */
    private String invoiceProvideName;

    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;

    /**
     * 子账户id
     */
    private String accountSubId;


}
