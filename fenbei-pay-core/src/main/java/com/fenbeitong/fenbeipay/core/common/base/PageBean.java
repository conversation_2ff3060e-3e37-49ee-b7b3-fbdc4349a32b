package com.fenbeitong.fenbeipay.core.common.base;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;

/**
 * @Description: java类作用描述
 * @ClassName: PageDTO
 * @Author: zhangga
 * @CreateDate: 2018/12/3 下午8:49
 * @UpdateUser:
 * @UpdateDate: 2018/12/3 下午8:49
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
public class PageBean {
    /**
     * 最大每页条数
     **/
    private Integer maxPageSize = 1000;
    /**
     * 当前页数
     **/
    @Min(1)
    private Integer pageNo = 1;
    /**
     * 每页条数
     **/
    @Min(1)
    private Integer pageSize = 10;
    /**
     * 起始行数
     **/
    private Integer offset;

    public Integer getOffset() {
        return (pageNo - 1) * this.getPageSize();
    }

    public Integer getPageSize() {
        if (pageSize > maxPageSize) {
            return maxPageSize;
        }
        return pageSize;
    }

    public PageBean(Integer pageNo, Integer pageSize) {
        this.pageNo = pageNo == null ? 1 : pageNo;
        this.pageSize = pageSize == null ? 10 : pageSize;
    }
}
