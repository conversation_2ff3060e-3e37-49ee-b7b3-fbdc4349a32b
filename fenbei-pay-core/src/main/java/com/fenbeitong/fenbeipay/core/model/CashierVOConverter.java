package com.fenbeitong.fenbeipay.core.model;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.*;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.CashierMultipleCreateTradeRPCDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierMultipleCreateTradeRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierTradeReliefRPCVo;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.CashierCreateAndPayBatchReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.CashierCreateAndPayReqVO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.v2.CashierReimbursementPayV1VO;
import com.fenbeitong.fenbeipay.core.enums.paycenter.AccountType;
import com.fenbeitong.fenbeipay.core.enums.personpay.PayTransactionNoPrefix;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.core.utils.TransactionNoGenerator;
import com.fenbeitong.finhub.common.constant.*;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.Date;

import static com.fenbeitong.fenbeipay.core.constant.msg.CommonConstant.LOCALHOST;

public class CashierVOConverter {

    public static void cashierCreateTradeReqVo2CashierOrderSettlement(CashierCreateTradeReqVo reqVo, CashierOrderSettlement cashierOrderSettlement) {
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setCompanyId(reqVo.getCompanyId());
        cashierOrderSettlement.setPaymentCompanyId(reqVo.getPaymentCompanyId());
        cashierOrderSettlement.setEmployeeId(reqVo.getEmployeeId());
        cashierOrderSettlement.setFbOrderId(reqVo.getFbOrderId());
        cashierOrderSettlement.setFbOrderName(reqVo.getFbOrderName());
        cashierOrderSettlement.setFbOrderSnapshot(reqVo.getFbOrderSnapshot());
        cashierOrderSettlement.setFbTradeId(reqVo.getFbTradeId());
        if(StringUtils.isNotBlank(reqVo.getFbTradeId())){
            cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PRE_PAY.getCode());
        }else{
            cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        }
        cashierOrderSettlement.setCurrency(reqVo.getCurrency());
        cashierOrderSettlement.setOrderType(reqVo.getOrderType());
        cashierOrderSettlement.setOrderSubType(reqVo.getOrderSubType());
        cashierOrderSettlement.setAccountType(reqVo.getAccountType());
        cashierOrderSettlement.setAmountAll(reqVo.getTotalPayPrice());
        cashierOrderSettlement.setAmountPersonal(reqVo.getPersonalPayPrice());
        cashierOrderSettlement.setAmountPublic(reqVo.getCompanyPayPrice());
        cashierOrderSettlement.setAmountCoupon(reqVo.getCouponAmount());
        cashierOrderSettlement.setCompanyPrePay(reqVo.getCompanyPrePay());
        cashierOrderSettlement.setBizCallbackUrl(reqVo.getBizCallbackUrl());
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(reqVo.getCommonJson()));
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setBizPayType(ObjUtils.isEmpty(reqVo.getBizPayType()) ? null : StringUtils.join(reqVo.getBizPayType(), ","));
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setCallbackNext(new Date());
        cashierOrderSettlement.setConsumerAccountSubType(reqVo.getConsumerAccountSubType());
        cashierOrderSettlement.setCompanyNoSettlePrice(reqVo.getCompanyNoSettlePrice());
        cashierOrderSettlement.setOperationChannelType(reqVo.getOperationChannelType());
        Integer operationChannelType = reqVo.getOperationChannelType();
        cashierOrderSettlement.setOperationChannelType(operationChannelType == null ? OperationChannelType.NORMAL.getKey() : reqVo.getOperationChannelType());
        cashierOrderSettlement.setAmountPublicRule(ObjUtils.isEmpty(reqVo.getCompanyPayRulePrice()) ? reqVo.getCompanyPayPrice() : reqVo.getCompanyPayRulePrice());
        cashierOrderSettlement.setBusinessMode(reqVo.getBusinessMode());
        cashierOrderSettlement.setSceneInvoiceType(reqVo.getSceneInvoiceType());
        cashierOrderSettlement.setInvoiceProvideType(reqVo.getInvoiceProvideType());
        cashierOrderSettlement.setInvoiceProvideName(reqVo.getInvoiceProvideName());
        cashierOrderSettlement.setInvoiceProvideStatus(reqVo.getInvoiceProvideStatus());
        cashierOrderSettlement.setAmountReimburseCompany(reqVo.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(reqVo.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(reqVo.getOrderPaymentModel())?reqVo.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountZeroTax(reqVo.getAmountZeroTax());
    }


    public static void cashierCreatePayTradeReqVo2CashierOrderSettlement(CashierCreatePayTradeReqVo createPayTradeReqVo, CashierOrderSettlement cashierOrderSettlement) {
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setCompanyId(createPayTradeReqVo.getCompanyId());
        cashierOrderSettlement.setPaymentCompanyId(createPayTradeReqVo.getPaymentCompanyId());
        cashierOrderSettlement.setEmployeeId(createPayTradeReqVo.getEmployeeId());
        cashierOrderSettlement.setFbOrderId(createPayTradeReqVo.getFbOrderId());
        cashierOrderSettlement.setFbOrderName(createPayTradeReqVo.getFbOrderName());
        cashierOrderSettlement.setFbOrderSnapshot(createPayTradeReqVo.getFbOrderSnapshot());
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setCurrency(createPayTradeReqVo.getCurrency());
        cashierOrderSettlement.setOrderType(createPayTradeReqVo.getOrderType());
        cashierOrderSettlement.setOrderSubType(createPayTradeReqVo.getOrderSubType());
        cashierOrderSettlement.setAccountType(AccountType.Public_Type.getKey());
        cashierOrderSettlement.setAmountAll(createPayTradeReqVo.getTotalPayPrice());
        cashierOrderSettlement.setAmountPersonal(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountPublic(createPayTradeReqVo.getCompanyPayPrice());
        cashierOrderSettlement.setAmountCoupon(createPayTradeReqVo.getCouponAmount());
        cashierOrderSettlement.setMaxVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        cashierOrderSettlement.setBizCallbackUrl(StringUtils.isBlank(createPayTradeReqVo.getBizCallbackUrl()) ? LOCALHOST : createPayTradeReqVo.getBizCallbackUrl());
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(createPayTradeReqVo.getCommonJson()));
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setBizPayType(BizPayType.COMPANY.name());
        cashierOrderSettlement.setCallbackNext(new Date());
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setConsumerAccountSubType(createPayTradeReqVo.getConsumerAccountSubType());
        cashierOrderSettlement.setCompanyNoSettlePrice(createPayTradeReqVo.getCompanyNoSettlePrice());
        cashierOrderSettlement.setAmountPublicRule(ObjUtils.isEmpty(createPayTradeReqVo.getCompanyPayRulePrice()) ? createPayTradeReqVo.getCompanyPayPrice() : createPayTradeReqVo.getCompanyPayRulePrice());
        cashierOrderSettlement.setOperationChannelType(createPayTradeReqVo.getOperationChannelType());
        cashierOrderSettlement.setCustomerServiceId(createPayTradeReqVo.getCustomerServiceId());
        cashierOrderSettlement.setCustomerServiceName(createPayTradeReqVo.getCustomerServiceName());
        cashierOrderSettlement.setRedcouponCanPay(createPayTradeReqVo.getRedcouponCanPay());
        cashierOrderSettlement.setBusinessMode(createPayTradeReqVo.getBusinessMode());
        cashierOrderSettlement.setSceneInvoiceType(createPayTradeReqVo.getSceneInvoiceType());
        cashierOrderSettlement.setInvoiceProvideType(createPayTradeReqVo.getInvoiceProvideType());
        cashierOrderSettlement.setInvoiceProvideName(createPayTradeReqVo.getInvoiceProvideName());
        cashierOrderSettlement.setInvoiceProvideStatus(createPayTradeReqVo.getInvoiceProvideStatus());
        cashierOrderSettlement.setAmountReimburseCompany(createPayTradeReqVo.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(createPayTradeReqVo.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(createPayTradeReqVo.getOrderPaymentModel())?createPayTradeReqVo.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        cashierOrderSettlement.setPaymentCompanyId(createPayTradeReqVo.getPaymentCompanyId());
    }

    public static void cashierCashierCreateAutoPayTradeReqVO2CashierOrderSettlementByPrivate(CashierCreateAutoPayTradePrivateReqVO cashierCreateAutoPayTradePrivateReqVO, CashierOrderSettlement cashierOrderSettlement) {
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setCompanyId(cashierCreateAutoPayTradePrivateReqVO.getCompanyId());
        cashierOrderSettlement.setPaymentCompanyId(cashierCreateAutoPayTradePrivateReqVO.getPaymentCompanyId());
        cashierOrderSettlement.setEmployeeId(cashierCreateAutoPayTradePrivateReqVO.getEmployeeId());
        cashierOrderSettlement.setFbOrderId(cashierCreateAutoPayTradePrivateReqVO.getFbOrderId());
        cashierOrderSettlement.setFbOrderName(cashierCreateAutoPayTradePrivateReqVO.getFbOrderName());
        cashierOrderSettlement.setFbOrderSnapshot(cashierCreateAutoPayTradePrivateReqVO.getFbOrderSnapshot());
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setCurrency(cashierCreateAutoPayTradePrivateReqVO.getCurrency());
        cashierOrderSettlement.setOrderType(cashierCreateAutoPayTradePrivateReqVO.getOrderType());
        cashierOrderSettlement.setOrderSubType(cashierCreateAutoPayTradePrivateReqVO.getOrderSubType());
        cashierOrderSettlement.setAccountType(AccountType.Personal_Type.getKey());
        cashierOrderSettlement.setAmountAll(cashierCreateAutoPayTradePrivateReqVO.getTotalPayPrice());
        cashierOrderSettlement.setAmountPersonal(cashierCreateAutoPayTradePrivateReqVO.getPersonalPayPrice());
        cashierOrderSettlement.setAmountPublic(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountCoupon(BigDecimal.ZERO);
        cashierOrderSettlement.setMaxVoucher(cashierCreateAutoPayTradePrivateReqVO.getMaxVoucher() == null ? cashierCreateAutoPayTradePrivateReqVO.getTotalPayPrice()  :cashierCreateAutoPayTradePrivateReqVO.getMaxVoucher());
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        cashierOrderSettlement.setBizCallbackUrl(cashierCreateAutoPayTradePrivateReqVO.getBizCallbackUrl());
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(cashierCreateAutoPayTradePrivateReqVO.getCommonJson()));
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setCallbackNext(new Date());
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setConsumerAccountSubType(cashierCreateAutoPayTradePrivateReqVO.getConsumerAccountSubType());
        cashierOrderSettlement.setCompanyNoSettlePrice(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountPublicRule(BigDecimal.ZERO);
        cashierOrderSettlement.setOperationChannelType(cashierCreateAutoPayTradePrivateReqVO.getOperationChannelType());
        cashierOrderSettlement.setCustomerServiceId(cashierCreateAutoPayTradePrivateReqVO.getCustomerServiceId());
        cashierOrderSettlement.setCustomerServiceName(cashierCreateAutoPayTradePrivateReqVO.getCustomerServiceName());
        cashierOrderSettlement.setRedcouponCanPay(cashierCreateAutoPayTradePrivateReqVO.getRedCouponCanPay());
        cashierOrderSettlement.setBusinessMode(cashierCreateAutoPayTradePrivateReqVO.getBusinessMode());
        cashierOrderSettlement.setSceneInvoiceType(cashierCreateAutoPayTradePrivateReqVO.getSceneInvoiceType());
        cashierOrderSettlement.setInvoiceProvideType(cashierCreateAutoPayTradePrivateReqVO.getInvoiceProvideType());
        cashierOrderSettlement.setInvoiceProvideName(cashierCreateAutoPayTradePrivateReqVO.getInvoiceProvideName());
        cashierOrderSettlement.setInvoiceProvideStatus(cashierCreateAutoPayTradePrivateReqVO.getInvoiceProvideStatus());
        cashierOrderSettlement.setAmountReimburseCompany(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountReimburseSelf(BigDecimal.ZERO);
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(cashierCreateAutoPayTradePrivateReqVO.getOrderPaymentModel())? cashierCreateAutoPayTradePrivateReqVO.getOrderPaymentModel(): PayModelEnum.PERSONAL_PAY.getCode());
        cashierOrderSettlement.setAmountVoucherIndividual(cashierCreateAutoPayTradePrivateReqVO.getPersonFbbPayPrice());
        cashierOrderSettlement.setAmountVoucherRedcoupon(cashierCreateAutoPayTradePrivateReqVO.getPersonVouchersPayPrice());
        cashierOrderSettlement.setThirdPayChannel(cashierCreateAutoPayTradePrivateReqVO.getThirdPartChannel());
    }

    public static void cashierCreateTradeReqVo2CashierOrderSettlementBank(CashierCreateTradeReqVo createTradeReqVo, CashierOrderSettlement cashierOrderSettlement, String cashierTxnId,BizPayType bizPayType) {
        BeanUtils.copyProperties(createTradeReqVo, cashierOrderSettlement);
        Date currentDate = new Date();
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setPayStatus(CashierPayStatus.CASHIER_SUCCESS_DONE.getKey());
        cashierOrderSettlement.setCreateTime(currentDate);
        cashierOrderSettlement.setUpdateTime(currentDate);
        cashierOrderSettlement.setAmountAll(createTradeReqVo.getTotalPayPrice());
        cashierOrderSettlement.setAmountThird(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountFbb(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountBank(createTradeReqVo.getTotalPayPrice());
        cashierOrderSettlement.setAccountType(AccountType.Public_Type.getKey());
        cashierOrderSettlement.setAmountPublic(BigDecimal.ZERO);
        cashierOrderSettlement.setCashierTxnId(cashierTxnId);

        cashierOrderSettlement.setAmountPersonal(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountCoupon(createTradeReqVo.getCouponAmount());
        cashierOrderSettlement.setMaxVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        cashierOrderSettlement.setBizCallbackUrl(LOCALHOST);

        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setCompleteTime(currentDate);
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(createTradeReqVo.getCommonJson()));
        cashierOrderSettlement.setBizPayType(bizPayType.name());
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setCallbackNext(currentDate);
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setAmountReimburseCompany(createTradeReqVo.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(createTradeReqVo.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(createTradeReqVo.getOrderPaymentModel())?createTradeReqVo.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAccountSubId(createTradeReqVo.getAccountSubId());
        cashierOrderSettlement.setAccountModel(createTradeReqVo.getAccountModel());

        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
    }


    public static void makeCashierCreateTradeRespVo(CashierCreateTradeReqVo cashierCreateTradeReqVo, CashierCreateTradeRespVo cashierCreateTradeRespVo, CashierOrderSettlement cashierOrderSettlement, String cashierTxnId) {
        cashierCreateTradeRespVo.setCashierTxnId(cashierTxnId);
        cashierCreateTradeRespVo.setFbOrderId(cashierCreateTradeReqVo.getFbOrderId());
        cashierCreateTradeRespVo.setAccountType(cashierCreateTradeReqVo.getAccountType());
        cashierCreateTradeRespVo.setOrderType(cashierCreateTradeReqVo.getOrderType());
        cashierCreateTradeRespVo.setPayStatus(cashierOrderSettlement.getPayStatus());
        cashierCreateTradeRespVo.setEmployeeId(cashierCreateTradeReqVo.getEmployeeId());
        cashierCreateTradeRespVo.setCompanyId(cashierCreateTradeReqVo.getCompanyId());
        cashierCreateTradeRespVo.setFbTradeId(cashierCreateTradeReqVo.getFbTradeId());
    }

    public static void cashierRefundTradeReqVo2CashierOrderRefundSettlement(CashierRefundTradeReqVo cashierRefundTradeReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement) {
        refundSettlement.setRootOrderId(cashierOrder.getRootOrderId());
        refundSettlement.setFbOrderId(cashierOrder.getFbOrderId());
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setCashierTxnId(cashierOrder.getCashierTxnId());
        refundSettlement.setCompanyId(cashierOrder.getCompanyId());
        refundSettlement.setEmployeeId(cashierOrder.getEmployeeId());
        refundSettlement.setRefundOrderId(cashierRefundTradeReqVo.getRefundOrderId());
        refundSettlement.setFbTradeId(cashierOrder.getFbTradeId());
        refundSettlement.setRefundAmountAll(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTradeReqVo.getPublicRefundAmount());
        refundSettlement.setRefundAmountPersonal(cashierRefundTradeReqVo.getPersonalRefundAmount());

        refundSettlement.setFbOrderName(cashierOrder.getFbOrderName());
        refundSettlement.setCurrency(cashierOrder.getCurrency());
        refundSettlement.setAccountType(cashierOrder.getAccountType());
        String callUrl = cashierRefundTradeReqVo.getBizCallbackUrl();
        refundSettlement.setBizNotifyUrl(StringUtils.isBlank(callUrl) ? LOCALHOST : callUrl);
        refundSettlement.setRefundReason(cashierRefundTradeReqVo.getRefundReason());
        refundSettlement.setOrderType(cashierOrder.getOrderType());
        refundSettlement.setOrderSubType(cashierOrder.getOrderSubType());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRemark(cashierRefundTradeReqVo.getRemark());
        refundSettlement.setCallbackNext(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setVoucherCompanyId(cashierOrder.getVoucherCompanyId());
        refundSettlement.setCashierRefundWay(cashierRefundTradeReqVo.getPersonalRefundWay());
        refundSettlement.setCashierPublicRefundWay(cashierRefundTradeReqVo.getCashierPublicRefundWay());
        refundSettlement.setBusinessMode(cashierOrder.getBusinessMode());
        refundSettlement.setInvoiceProvideName(cashierOrder.getInvoiceProvideName());
        refundSettlement.setInvoiceProvideStatus(cashierOrder.getInvoiceProvideStatus());
        refundSettlement.setSceneInvoiceType(cashierOrder.getSceneInvoiceType());
        refundSettlement.setInvoiceProvideType(cashierOrder.getInvoiceProvideType());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setRefundAmountReimburseCompany(cashierRefundTradeReqVo.getRefundAmountReimburseCompany());
        refundSettlement.setRefundAmountReimburseSelf(cashierRefundTradeReqVo.getRefundAmountReimburseSelf());
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
        refundSettlement.setBankPersonalRefundAmount(cashierRefundTradeReqVo.getBankPersonalRefundAmount());
    }

    public static void cashierRefundTradeReliefRPCVo2CashierOrderRefundSettlement(CashierTradeReliefRPCVo cashierRefundTradeReqVo, String cashierTxnId, CashierOrderRefundSettlement refundSettlement) {
        refundSettlement.setFbOrderId(cashierRefundTradeReqVo.getFbOrderId());
        refundSettlement.setRootOrderId(cashierRefundTradeReqVo.getFbOrderId());
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setCashierTxnId(cashierTxnId);
        refundSettlement.setCompanyId(cashierRefundTradeReqVo.getCompanyId());
        refundSettlement.setEmployeeId(cashierRefundTradeReqVo.getEmployeeId());
        refundSettlement.setRefundOrderId(cashierRefundTradeReqVo.getRefundOrderId());

        refundSettlement.setRefundAmountAll(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTradeReqVo.getPublicRefundAmount());
        refundSettlement.setRefundAmountPersonal(cashierRefundTradeReqVo.getPersonalRefundAmount());

        refundSettlement.setFbOrderName(cashierRefundTradeReqVo.getFbOrderName());
        refundSettlement.setCurrency("cny");
        refundSettlement.setAccountType(AccountType.Public_Type.getKey());
        String callUrl = cashierRefundTradeReqVo.getBizCallbackUrl();
        refundSettlement.setBizNotifyUrl(StringUtils.isBlank(callUrl) ? LOCALHOST : callUrl);
        refundSettlement.setRefundReason(cashierRefundTradeReqVo.getRefundReason());
        refundSettlement.setOrderType(cashierRefundTradeReqVo.getOrderType());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRemark(cashierRefundTradeReqVo.getRemark());
        refundSettlement.setCallbackNext(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setOrderSubType(cashierRefundTradeReqVo.getOrderSubType());
        refundSettlement.setBankPersonalRefundAmount(BigDecimal.ZERO);
    }

    public static void cashierRefundTradeReqVo2CashierOrderRefundSettlementBank(CashierRefundTradeReqVo cashierRefundTradeReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement, String refundTxnId) {
        BeanUtils.copyProperties(cashierOrder, refundSettlement);
        Date date = new Date();
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setRootOrderId(cashierOrder.getRootOrderId());
        refundSettlement.setRefundStatus(RefundStatus.REFUND_SUCCESS_DONE.getKey());
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRefundAmountAll(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountBank(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        refundSettlement.setRefundAmountThird(BigDecimal.ZERO);
        refundSettlement.setRefundAmountFbb(BigDecimal.ZERO);
        refundSettlement.setRefundAmountVoucher(BigDecimal.ZERO);
        refundSettlement.setRefundTxnId(refundTxnId);
        refundSettlement.setRefundReason(cashierRefundTradeReqVo.getRefundReason());
        refundSettlement.setCompleteTime(date);
        refundSettlement.setRemark(cashierRefundTradeReqVo.getRemark());
        refundSettlement.setRefundOrderId(cashierRefundTradeReqVo.getRefundOrderId());
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(date);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
//        refundSettlement.setBankPersonalRefundAmount(cashierRefundTradeReqVo.getTotalRefundAmount());
    }

    public static void cashierCreatePayNotifyTradeReqVo2CashierOrderSettlement(CashierCreatePayNoEmployeeTradeReqVo noEmployeeTradeReqVo, CashierOrderSettlement cashierOrderSettlement) {
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setCurrency(noEmployeeTradeReqVo.getCurrency());
        cashierOrderSettlement.setOrderType(noEmployeeTradeReqVo.getOrderType());
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setAccountType(AccountType.Public_Type.getKey());
        cashierOrderSettlement.setAmountAll(noEmployeeTradeReqVo.getTotalPayPrice());
        cashierOrderSettlement.setAmountPersonal(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountPublic(noEmployeeTradeReqVo.getCompanyPayPrice());
        cashierOrderSettlement.setAmountCoupon(noEmployeeTradeReqVo.getCouponAmount());
        cashierOrderSettlement.setMaxVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        String callUrl = noEmployeeTradeReqVo.getBizCallbackUrl();
        cashierOrderSettlement.setBizCallbackUrl(StringUtils.isBlank(callUrl) ? LOCALHOST : callUrl);
        cashierOrderSettlement.setCommonJson(ObjUtils.isNotBlank(noEmployeeTradeReqVo.getCommonJson()) ? JsonUtils.toJson(noEmployeeTradeReqVo.getCommonJson()) : null);
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setBizPayType(BizPayType.COMPANY.name());
        cashierOrderSettlement.setCompanyId(noEmployeeTradeReqVo.getCompanyId());
        cashierOrderSettlement.setEmployeeId(noEmployeeTradeReqVo.getEmployeeId());
        cashierOrderSettlement.setFbOrderId(noEmployeeTradeReqVo.getFbOrderId());
        cashierOrderSettlement.setFbOrderName(noEmployeeTradeReqVo.getFbOrderName());
        cashierOrderSettlement.setFbOrderSnapshot(noEmployeeTradeReqVo.getFbOrderSnapshot());
        cashierOrderSettlement.setConsumerAccountSubType(noEmployeeTradeReqVo.getConsumerAccountSubType());
        cashierOrderSettlement.setAmountPublicRule(ObjUtils.isEmpty(noEmployeeTradeReqVo.getCompanyPayRulePrice()) ? noEmployeeTradeReqVo.getCompanyPayPrice() : noEmployeeTradeReqVo.getCompanyPayRulePrice());
        cashierOrderSettlement.setOperationChannelType(noEmployeeTradeReqVo.getOperationChannelType());
        cashierOrderSettlement.setBusinessMode(noEmployeeTradeReqVo.getBusinessMode());
        cashierOrderSettlement.setInvoiceProvideName(noEmployeeTradeReqVo.getInvoiceProvideName());
        cashierOrderSettlement.setInvoiceProvideStatus(noEmployeeTradeReqVo.getInvoiceProvideStatus());
        cashierOrderSettlement.setSceneInvoiceType(noEmployeeTradeReqVo.getSceneInvoiceType());
        cashierOrderSettlement.setInvoiceProvideType(noEmployeeTradeReqVo.getInvoiceProvideType());
        cashierOrderSettlement.setAmountReimburseCompany(noEmployeeTradeReqVo.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(noEmployeeTradeReqVo.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(noEmployeeTradeReqVo.getOrderPaymentModel())?noEmployeeTradeReqVo.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountZeroTax(noEmployeeTradeReqVo.getAmountZeroTax());
    }


    public static CashierCreatePayNoEmployeeTradeRespVo cashierOrderSettlementCashier2CreatePayNotifyTradeRespVo(CashierOrderSettlement cashier) {
        CashierCreatePayNoEmployeeTradeRespVo cashierCreateTradeReqVo = new CashierCreatePayNoEmployeeTradeRespVo();
        BeanUtils.copyProperties(cashier, cashierCreateTradeReqVo);
        cashierCreateTradeReqVo.setOperationChannelType(cashierCreateTradeReqVo.getOperationChannelType());
        cashierCreateTradeReqVo.setPayStatus(CashierPayStatus.CASHIER_SUCCESS_PAY.getKey());
        cashierCreateTradeReqVo.setCashierTxnId(cashierCreateTradeReqVo.getCashierTxnId());
        return cashierCreateTradeReqVo;
    }

    public static void cashierRefundTradeNoEmployeeReqVoVo2CashierOrderRefundSettlement(CashierRefundTradeNoEmployeeReqVo cashierRefundTradeReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement) {
        refundSettlement.setRootOrderId(cashierOrder.getRootOrderId());
        refundSettlement.setFbOrderId(cashierOrder.getFbOrderId());
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setCashierTxnId(cashierOrder.getCashierTxnId());
        refundSettlement.setCompanyId(cashierOrder.getCompanyId());
        refundSettlement.setEmployeeId(cashierOrder.getEmployeeId());
        refundSettlement.setRefundOrderId(cashierRefundTradeReqVo.getRefundOrderId());
        refundSettlement.setRefundAmountAll(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTradeReqVo.getPublicRefundAmount());
        refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        refundSettlement.setFbOrderName(cashierOrder.getFbOrderName());
        refundSettlement.setCurrency(cashierOrder.getCurrency());
        refundSettlement.setAccountType(cashierOrder.getAccountType());
        String callUrl = cashierRefundTradeReqVo.getBizCallbackUrl();
        refundSettlement.setBizNotifyUrl(StringUtils.isBlank(callUrl) ? LOCALHOST : callUrl);
        refundSettlement.setRefundReason(cashierRefundTradeReqVo.getRefundReason());
        refundSettlement.setOrderType(cashierOrder.getOrderType());
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(new Date());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setCashierPublicRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setBusinessMode(cashierOrder.getBusinessMode());
        refundSettlement.setInvoiceProvideName(cashierOrder.getInvoiceProvideName());
        refundSettlement.setInvoiceProvideStatus(cashierOrder.getInvoiceProvideStatus());
        refundSettlement.setSceneInvoiceType(cashierOrder.getSceneInvoiceType());
        refundSettlement.setInvoiceProvideType(cashierOrder.getInvoiceProvideType());
        refundSettlement.setRefundAmountCompany(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setRefundAmountCompany(cashierRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
    }

    public static void cashierRefundTradeMaxWayReqVo2CashierOrderRefundSettlement(CashierRefundTradeMaxWayReqVo cashierRefundTradeMaxWayReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement) {
        refundSettlement.setRootOrderId(cashierOrder.getRootOrderId());
        refundSettlement.setFbOrderId(cashierOrder.getFbOrderId());
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setCashierTxnId(cashierOrder.getCashierTxnId());
        refundSettlement.setCompanyId(cashierOrder.getCompanyId());
        refundSettlement.setEmployeeId(cashierOrder.getEmployeeId());
        refundSettlement.setRefundOrderId(cashierRefundTradeMaxWayReqVo.getRefundOrderId());
        refundSettlement.setRefundAmountAll(cashierRefundTradeMaxWayReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTradeMaxWayReqVo.getPublicRefundAmount());
        refundSettlement.setRefundAmountPersonal(cashierRefundTradeMaxWayReqVo.getPersonalRefundAmount());
        refundSettlement.setFbOrderName(cashierOrder.getFbOrderName());
        refundSettlement.setCurrency(cashierOrder.getCurrency());
        refundSettlement.setAccountType(cashierOrder.getAccountType());
        String callUrl = cashierRefundTradeMaxWayReqVo.getBizCallbackUrl();
        refundSettlement.setBizNotifyUrl(StringUtils.isBlank(callUrl) ? LOCALHOST : callUrl);
        refundSettlement.setRefundReason(cashierRefundTradeMaxWayReqVo.getRefundReason());
        refundSettlement.setOrderType(cashierOrder.getOrderType());
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(new Date());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(cashierRefundTradeMaxWayReqVo.getCashierRefundWay()==null?CashierRefundWay.REFUND_MAX.getKey():cashierRefundTradeMaxWayReqVo.getCashierRefundWay());
        refundSettlement.setCashierPublicRefundWay(cashierRefundTradeMaxWayReqVo.getCashierPublicRefundWay()==null?CashierRefundWay.REFUND_MAX.getKey():cashierRefundTradeMaxWayReqVo.getCashierPublicRefundWay());
        refundSettlement.setBusinessMode(cashierOrder.getBusinessMode());
        refundSettlement.setInvoiceProvideName(cashierOrder.getInvoiceProvideName());
        refundSettlement.setInvoiceProvideStatus(cashierOrder.getInvoiceProvideStatus());
        refundSettlement.setSceneInvoiceType(cashierOrder.getSceneInvoiceType());
        refundSettlement.setInvoiceProvideType(cashierOrder.getInvoiceProvideType());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
    }

    public static CashierRefundTradeRespVo cashierOrderSettlement2CashierRefundTradeRespVo(CashierOrderSettlement cashierOrder, String refundTxnId, String refundOrderId, RefundStatus refundSuccess) {
        CashierRefundTradeRespVo cashierRefundTradeRespVo = new CashierRefundTradeRespVo();
        cashierRefundTradeRespVo.setCashierTxnId(cashierOrder.getCashierTxnId());
        cashierRefundTradeRespVo.setRefundTxnId(refundTxnId);
        cashierRefundTradeRespVo.setFbOrderId(cashierOrder.getFbOrderId());
        cashierRefundTradeRespVo.setRefundStatus(refundSuccess.getKey());
        //售后场景逆向订单号
        cashierRefundTradeRespVo.setRefundOrderId(refundOrderId);
        return cashierRefundTradeRespVo;
    }


    public static CashierRefundTradeReqVo cashierBatchRefundTradeReqVo2CashierRefundTradeReqVo(RefundCommonVo refundCommonVo,
                                                                                               CashierBatchRefundTradeReqVo cashierBatchRefundTradeReqVo,
                                                                                               Integer num) {
        CashierRefundTradeReqVo cashierRefundTradeReqVo = new CashierRefundTradeReqVo();
        BeanUtils.copyProperties(refundCommonVo, cashierRefundTradeReqVo);
        cashierRefundTradeReqVo.setCompanyId(cashierBatchRefundTradeReqVo.getCompanyId());
        cashierRefundTradeReqVo.setRefundOrderId(cashierBatchRefundTradeReqVo.getBatchRefundOrderId());
        cashierRefundTradeReqVo.setRemark(MessageFormat.format("{0}{1}{2}", cashierBatchRefundTradeReqVo.getBatchRefundOrderId(), "_", num));
        return cashierRefundTradeReqVo;
    }

    public static CashierRefundTradeMaxWayReqVo batchRefundTradeReqVo2createRefundTradeMaxWayAndSaas(RefundCommonVo refundCommonVo, CashierBatchRefundTradeReqVo cashierBatchRefundTradeReqVo, Integer num) {
        CashierRefundTradeMaxWayReqVo refundTradeMaxWayReqVo = CashierRefundTradeMaxWayReqVo.builder().build();
        BeanUtils.copyProperties(refundCommonVo, refundTradeMaxWayReqVo);
        refundTradeMaxWayReqVo.setCompanyId(cashierBatchRefundTradeReqVo.getCompanyId());
        refundTradeMaxWayReqVo.setRefundOrderId(cashierBatchRefundTradeReqVo.getBatchRefundOrderId());
        refundTradeMaxWayReqVo.setRemark(MessageFormat.format("{0}{1}{2}", cashierBatchRefundTradeReqVo.getBatchRefundOrderId(), "_", num));
        return refundTradeMaxWayReqVo;
    }

    /**
     * 去除保险金额
     *
     * @param cashierOrder
     * @return
     */
    public static BigDecimal getTotalNetSettlePrice(CashierOrderSettlement cashierOrder) {
        if (null == cashierOrder) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalNetSettlePrice = cashierOrder.getAmountAll();
        if (null != cashierOrder.getCompanyNoSettlePrice()) {
            totalNetSettlePrice = totalNetSettlePrice.subtract(cashierOrder.getCompanyNoSettlePrice());
        }
        return totalNetSettlePrice;
    }

    /**
     * 去除保险金额
     *
     * @param cashierOrder
     * @return
     */
    public static BigDecimal getPublicNetSettlePrice(CashierOrderSettlement cashierOrder) {
        if (null == cashierOrder) {
            return BigDecimal.ZERO;
        }
        BigDecimal publicNetSettlePrice = cashierOrder.getAmountPublic();
        if (null != cashierOrder.getCompanyNoSettlePrice()) {
            publicNetSettlePrice = publicNetSettlePrice.subtract(cashierOrder.getCompanyNoSettlePrice());
        }
        return publicNetSettlePrice;
    }


    public static void cashierRefundTradeReqVo2CashierOrderRefundSettlementAcctPublic(CashierAcctPublicRefundTradeReqVo acctPublicRefundTradeReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement, String refundTxnId) {
        BeanUtils.copyProperties(cashierOrder, refundSettlement);
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setRefundStatus(RefundStatus.REFUND_SUCCESS_DONE.getKey());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRefundAmountAll(acctPublicRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountBank(acctPublicRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(acctPublicRefundTradeReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        refundSettlement.setRefundAmountThird(BigDecimal.ZERO);
        refundSettlement.setRefundAmountFbb(BigDecimal.ZERO);
        refundSettlement.setRefundAmountVoucher(BigDecimal.ZERO);
        refundSettlement.setRefundTxnId(refundTxnId);
        refundSettlement.setRefundReason(acctPublicRefundTradeReqVo.getRefundReason());
        refundSettlement.setCompleteTime(date);
        refundSettlement.setRemark(acctPublicRefundTradeReqVo.getRemark());
        refundSettlement.setRefundOrderId(acctPublicRefundTradeReqVo.getRefundOrderId());
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
    }

    public static void makeCashierMultipleCreateTradeRPCDTO(CashierMultipleCreateTradeRPCDTO createTradeRPCDTO, CashierOrderSettlement cashierOrderSettlementUpdate) {
        createTradeRPCDTO.setCashierTxnId(cashierOrderSettlementUpdate.getCashierTxnId());
        createTradeRPCDTO.setFbOrderId(cashierOrderSettlementUpdate.getFbOrderId());
        createTradeRPCDTO.setAccountType(cashierOrderSettlementUpdate.getAccountType());
        createTradeRPCDTO.setOrderType(cashierOrderSettlementUpdate.getOrderType());
        createTradeRPCDTO.setPayStatus(cashierOrderSettlementUpdate.getPayStatus());
        createTradeRPCDTO.setEmployeeId(cashierOrderSettlementUpdate.getEmployeeId());
        createTradeRPCDTO.setCompanyId(cashierOrderSettlementUpdate.getCompanyId());
    }

    public static void cashierMultipleCreateTradeRPCVo2CashierOrderSettlement(CashierMultipleCreateTradeRPCVo reqVo, CashierOrderSettlement cashierOrderSettlementFirst,CashierOrderSettlement cashierOrderSettlement, String cashierTxnId) {
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setCashierTxnId(cashierTxnId);
        cashierOrderSettlement.setCompanyId(cashierOrderSettlementFirst.getCompanyId());
        cashierOrderSettlement.setPaymentCompanyId(cashierOrderSettlementFirst.getPaymentCompanyId());
        cashierOrderSettlement.setEmployeeId(cashierOrderSettlementFirst.getEmployeeId());
        cashierOrderSettlement.setFbOrderId(cashierOrderSettlementFirst.getFbOrderId());
        cashierOrderSettlement.setFbOrderName(reqVo.getFbOrderName());
        cashierOrderSettlement.setFbOrderSnapshot(reqVo.getFbOrderSnapshot());
        cashierOrderSettlement.setFbTradeId(reqVo.getFbTradeId());
        cashierOrderSettlement.setCashierTradeType(reqVo.getCashierTradeType());
        cashierOrderSettlement.setCurrency(cashierOrderSettlementFirst.getCurrency());
        cashierOrderSettlement.setOrderType(cashierOrderSettlementFirst.getOrderType());
        cashierOrderSettlement.setOrderSubType(cashierOrderSettlementFirst.getOrderSubType());
        cashierOrderSettlement.setAccountType(cashierOrderSettlementFirst.getAccountType());
        cashierOrderSettlement.setAmountAll(reqVo.getTotalPayPrice());
        cashierOrderSettlement.setAmountPersonal(reqVo.getPersonalPayPrice());
        cashierOrderSettlement.setAmountPublic(reqVo.getPublicPayPrice());
        cashierOrderSettlement.setAmountCoupon(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(reqVo.getCompanyPrePay());
        cashierOrderSettlement.setBizCallbackUrl(reqVo.getBizCallbackUrl());
        cashierOrderSettlement.setDeadlineTime(reqVo.getDeadlineTime());
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(reqVo.getCommonJson()));
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setBizPayType(cashierOrderSettlementFirst.getBizPayType());
        cashierOrderSettlement.setThirdPayChannel(cashierOrderSettlementFirst.getThirdPayChannel());
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setCallbackNext(new Date());
        cashierOrderSettlement.setConsumerAccountSubType(cashierOrderSettlementFirst.getConsumerAccountSubType());
        cashierOrderSettlement.setCompanyNoSettlePrice(reqVo.getCompanyNoSettlePrice());
        cashierOrderSettlement.setOperationChannelType(reqVo.getOperationChannelType());
        Integer operationChannelType = reqVo.getOperationChannelType();
        cashierOrderSettlement.setOperationChannelType(operationChannelType == null ? OperationChannelType.NORMAL.getKey() : reqVo.getOperationChannelType());
        cashierOrderSettlement.setAmountPublicRule((ObjUtils.isEmpty(reqVo.getCompanyPayRulePrice()) ? cashierOrderSettlementFirst.getAmountPublic() : reqVo.getCompanyPayRulePrice()));
        cashierOrderSettlement.setBusinessMode(cashierOrderSettlementFirst.getBusinessMode());
        cashierOrderSettlement.setSceneInvoiceType(cashierOrderSettlementFirst.getSceneInvoiceType());
        cashierOrderSettlement.setInvoiceProvideType(cashierOrderSettlementFirst.getInvoiceProvideType());
        cashierOrderSettlement.setInvoiceProvideName(cashierOrderSettlementFirst.getInvoiceProvideName());
        cashierOrderSettlement.setInvoiceProvideStatus(cashierOrderSettlementFirst.getInvoiceProvideStatus());
        cashierOrderSettlement.setAmountReimburseCompany(cashierOrderSettlementFirst.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(cashierOrderSettlementFirst.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(cashierOrderSettlementFirst.getOrderPaymentModel())?cashierOrderSettlementFirst.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
    }

    public static CashierRefundTradeReqVo multipleCreateTradeRPCVo2cashierRefundTradeReqVo(CashierMultipleCreateTradeRPCVo multipleCreateTradeRPCVo,CashierOrderSettlement cashierOrderSettlementFirst) {
        CashierRefundTradeReqVo tradeReqVo = new CashierRefundTradeReqVo();
        tradeReqVo.setTotalRefundAmount(multipleCreateTradeRPCVo.getTotalPayPrice());
        tradeReqVo.setPublicRefundAmount(multipleCreateTradeRPCVo.getPublicPayPrice());
        tradeReqVo.setPersonalRefundAmount(multipleCreateTradeRPCVo.getPersonalPayPrice());
        tradeReqVo.setCompanyRefundAmount(multipleCreateTradeRPCVo.getPublicPayPrice());
        tradeReqVo.setRedcouponRefundAmount(BigDecimal.ZERO);
        tradeReqVo.setPersonalThirdRefundPrice(BigDecimal.ZERO);
        tradeReqVo.setPersonFbbRefundPrice(BigDecimal.ZERO);
        tradeReqVo.setPersonVouchersRefundPrice(BigDecimal.ZERO);
        tradeReqVo.setPersonVouchersIndividualRefundPrice(BigDecimal.ZERO);
        tradeReqVo.setPersonVouchersRedcouponRefundPrice(BigDecimal.ZERO);
        return tradeReqVo;
    }

    public static void cashierRefundTradeReqVo2CashierOrderRefundSettlement4Reimbursement(CashierRefundTrade4ReimbursementReqVo cashierRefundTrade4ReimbursementReqVo, CashierOrderSettlement cashierOrder, CashierOrderRefundSettlement refundSettlement, String refundTxnId) {
        BeanUtils.copyProperties(cashierOrder, refundSettlement);
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setRefundStatus(RefundStatus.REFUND_SUCCESS_DONE.getKey());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRefundAmountAll(cashierRefundTrade4ReimbursementReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountBank(cashierRefundTrade4ReimbursementReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPublic(cashierRefundTrade4ReimbursementReqVo.getTotalRefundAmount());
        refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        refundSettlement.setRefundAmountThird(BigDecimal.ZERO);
        refundSettlement.setRefundAmountFbb(BigDecimal.ZERO);
        refundSettlement.setRefundAmountVoucher(BigDecimal.ZERO);
        refundSettlement.setRefundTxnId(refundTxnId);
        refundSettlement.setRefundReason(cashierRefundTrade4ReimbursementReqVo.getRefundReason());
        refundSettlement.setCompleteTime(date);
        refundSettlement.setRemark(cashierRefundTrade4ReimbursementReqVo.getRemark());
        refundSettlement.setRefundOrderId(cashierRefundTrade4ReimbursementReqVo.getRefundOrderId());
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
    }

    public static void cashierCreateAndPayReqVo2CashierOrderSettlement(CashierCreateAndPayReqVO cashierCreateAndPayReqVO, CashierOrderSettlement cashierOrderSettlement, String cashierTxnId) {
        BeanUtils.copyProperties(cashierCreateAndPayReqVO, cashierOrderSettlement);
        Date currentDate = new Date();
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setPayStatus(CashierPayStatus.CASHIER_DEFAULT.getKey());
        cashierOrderSettlement.setCreateTime(currentDate);
        cashierOrderSettlement.setUpdateTime(currentDate);
        cashierOrderSettlement.setAmountAll(cashierCreateAndPayReqVO.getTotalPayPrice());
        cashierOrderSettlement.setAmountThird(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountFbb(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountBank(cashierCreateAndPayReqVO.getTotalPayPrice());
        cashierOrderSettlement.setAccountType(AccountType.Public_Type.getKey());
        cashierOrderSettlement.setAmountPublic(BigDecimal.ZERO);
        cashierOrderSettlement.setCashierTxnId(cashierTxnId);
        cashierOrderSettlement.setAmountPersonal(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountCoupon(cashierCreateAndPayReqVO.getAmountCoupon());
        cashierOrderSettlement.setMaxVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        cashierOrderSettlement.setBizCallbackUrl(LOCALHOST);
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setCompleteTime(currentDate);
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(cashierCreateAndPayReqVO.getCommonJson()));
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setCallbackNext(currentDate);
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setAmountReimburseCompany(cashierCreateAndPayReqVO.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(cashierCreateAndPayReqVO.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(cashierCreateAndPayReqVO.getOrderPaymentModel())?cashierCreateAndPayReqVO.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAccountModel(cashierCreateAndPayReqVO.getAccountModel());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        //对公付款银企直联无分贝通侧子账户
        cashierOrderSettlement.setConsumerAccountSubType(FundAccountSubType.UNKNOW.getKey());
        cashierOrderSettlement.setOperationChannelType(OperationChannelType.WEB.getKey());
        cashierOrderSettlement.setBusinessMode(BusinessModeEnum.TRUST_IN.getCode());
        cashierOrderSettlement.setVoucherOrderInvoiceAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setVoucherInvoiceAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankCompanyAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankRedcouponAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankPersonalAmount(BigDecimal.ZERO);
    }

    public static void cashierReimbursementPayV1VO2CashierOrderSettlement(CashierCreateAndPayBatchReqVO cashierCreateAndPayBatchReqVO, CashierReimbursementPayV1VO cashierReimbursementPayV1VO, CashierOrderSettlement cashierOrderSettlement, String cashierTxnId) {
        Date currentDate = new Date();
        cashierOrderSettlement.setId(RandomUtils.bsonId());
        cashierOrderSettlement.setEmployeeId(cashierReimbursementPayV1VO.getEmployeeId());
        cashierOrderSettlement.setPayStatus(CashierPayStatus.CASHIER_DEFAULT.getKey());
        cashierOrderSettlement.setCreateTime(currentDate);
        cashierOrderSettlement.setUpdateTime(currentDate);
        cashierOrderSettlement.setAmountAll(cashierReimbursementPayV1VO.getTotalPayPrice());
        cashierOrderSettlement.setAmountThird(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountFbb(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountBank(cashierReimbursementPayV1VO.getTotalPayPrice());
        cashierOrderSettlement.setAccountType(AccountType.Public_Type.getKey());
        cashierOrderSettlement.setAmountPublic(BigDecimal.ZERO);
        cashierOrderSettlement.setCashierTxnId(cashierTxnId);
        cashierOrderSettlement.setAmountPersonal(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountCoupon(cashierReimbursementPayV1VO.getAmountCoupon());
        cashierOrderSettlement.setMaxVoucher(BigDecimal.ZERO);
        cashierOrderSettlement.setCompanyPrePay(CompanyPrePayType.NOT_PRE_PAY.getKey());
        cashierOrderSettlement.setBizCallbackUrl(LOCALHOST);
        cashierOrderSettlement.setCountVoucher(0);
        cashierOrderSettlement.setCompleteTime(currentDate);
        cashierOrderSettlement.setCommonJson(JsonUtils.toJson(cashierCreateAndPayBatchReqVO.getCommonJson()));
        cashierOrderSettlement.setCallbackNum(0);
        cashierOrderSettlement.setCallbackNext(currentDate);
        cashierOrderSettlement.setCashierTradeType(CashierTradeEnum.PAY.getCode());
        cashierOrderSettlement.setAmountReimburseCompany(cashierReimbursementPayV1VO.getAmountReimburseCompany());
        cashierOrderSettlement.setAmountReimburseSelf(cashierReimbursementPayV1VO.getAmountReimburseSelf());
        cashierOrderSettlement.setOrderPaymentModel(ObjUtils.isNotEmpty(cashierCreateAndPayBatchReqVO.getOrderPaymentModel())?cashierCreateAndPayBatchReqVO.getOrderPaymentModel(): PayModelEnum.COMPANY_PAY.getCode());
        cashierOrderSettlement.setAccountModel(cashierCreateAndPayBatchReqVO.getAccountModel());
        cashierOrderSettlement.setAmountVoucherIndividual(BigDecimal.ZERO);
        cashierOrderSettlement.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        //对公付款银企直联无分贝通侧子账户
        cashierOrderSettlement.setConsumerAccountSubType(FundAccountSubType.UNKNOW.getKey());
        cashierOrderSettlement.setOperationChannelType(OperationChannelType.WEB.getKey());
        cashierOrderSettlement.setBusinessMode(BusinessModeEnum.TRUST_IN.getCode());
        cashierOrderSettlement.setVoucherOrderInvoiceAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setVoucherInvoiceAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankCompanyAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankRedcouponAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setBankPersonalAmount(BigDecimal.ZERO);
        cashierOrderSettlement.setOrderType(cashierCreateAndPayBatchReqVO.getOrderType());
    }

    public static CashierOrderRefundSettlement cashierOrderRefundSettlement4Salary(CashierOrderSettlement cashierOrder) {
        CashierOrderRefundSettlement refundSettlement = new CashierOrderRefundSettlement();
        BeanUtils.copyProperties(cashierOrder, refundSettlement);
        OrderType orderType = OrderType.getEnum(cashierOrder.getOrderType());
        TransactionNoGenerator transactionNoGenerator = new TransactionNoGenerator(orderType.getPrefix(), "" + PayTransactionNoPrefix.PAY_SALARY.getPayType() + PayTransactionNoPrefix.PAY_SALARY.getPayRefund());
        String refundTxnId = transactionNoGenerator.genNewId();
        refundSettlement.setId(RandomUtils.bsonId());
        refundSettlement.setRefundStatus(RefundStatus.REFUND_SUCCESS_DONE.getKey());
        Date date = new Date();
        refundSettlement.setCreateTime(date);
        refundSettlement.setUpdateTime(date);
        refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        refundSettlement.setRefundAmountThird(BigDecimal.ZERO);
        refundSettlement.setRefundAmountFbb(BigDecimal.ZERO);
        refundSettlement.setRefundAmountVoucher(BigDecimal.ZERO);
        refundSettlement.setRefundTxnId(refundTxnId);
        refundSettlement.setRefundAmountPublic(BigDecimal.ZERO);
        refundSettlement.setCompleteTime(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setCallbackNext(date);
        refundSettlement.setCallbackNum(0);
        refundSettlement.setXeStatus(XeRefundStatus.NO_XE.getKey());
        refundSettlement.setCashierRefundWay(CashierRefundWay.REFUND_DEFAULT.getKey());
        refundSettlement.setAccountSubId(cashierOrder.getAccountSubId());
        refundSettlement.setAccountModel(cashierOrder.getAccountModel());
        refundSettlement.setRefundAmountReimburseCompany(BigDecimal.ZERO);
        refundSettlement.setRefundAmountReimburseSelf(BigDecimal.ZERO);
        refundSettlement.setOrderPaymentModel(cashierOrder.getOrderPaymentModel());
        refundSettlement.setOrderChannelType(cashierOrder.getOrderChannelType());
        refundSettlement.setFbOrderId(cashierOrder.getFbOrderId());
        refundSettlement.setRootOrderId(cashierOrder.getRootOrderId());
        return refundSettlement;
    }

}
