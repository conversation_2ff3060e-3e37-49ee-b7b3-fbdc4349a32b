package com.fenbeitong.fenbeipay.core.model.dto.freezen;

import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * @Description: java类作用描述
 * @ClassName: FreezenOptRespDTO
 * @Author: zhangga
 * @CreateDate: 2021/3/19 2:29 下午
 * @UpdateUser:
 * @UpdateDate: 2021/3/19 2:29 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class FreezenOptRespDTO extends FreezenBaseReqDTO {
    /**
     * 操作金额 单位：分
     */
    @Min(0)
    private BigDecimal operationAmount;

    /**
     * 操作人
     */
    private String operationUserId;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 操作原因
     */
    private String operationDescription;

    /**
     * 业务编码Id：比如订单消费为订单号ID
     */
    private String bizNo;

    /**
     * 业务在流水记录中唯一标识，订单类的为订单ID,与bizNo一样，分贝券类的为冻结任务ID等
     */
    private String verifyNo;

    /**
     * 银行交互账户
     */
    private String bankAcctId;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 对手账户银行卡号
     */
    private String targetBankAccountNo;

    /**
     * 对手银行名称
     */
    private String targetBankName;
    /**
     * 对手账号虚户ID
     */
    private String targetBankAcctId;

    /**
     * 账户流水ID
     **/
    private String accountFlowId;

    public void validate() {
        ValidateUtils.validate(this);
        CheckUtils.checkBlank(freezenUseType,"冻结类型不可以为空!");
    }

}
