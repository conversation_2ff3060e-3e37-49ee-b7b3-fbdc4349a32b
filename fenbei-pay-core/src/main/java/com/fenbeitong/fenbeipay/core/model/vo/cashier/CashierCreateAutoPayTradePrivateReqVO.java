package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardCostAttributionDTO;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.VoucherPayRPCVo;
import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 【因私】收银创建+支付所有能自动扣部分流水请求VO
 * <AUTHOR>
 * @date 2021/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CashierCreateAutoPayTradePrivateReqVO extends BaseVo  {

    private static final long serialVersionUID = -8002568011568718071L;
    /**
     * 员工ID,非accountId（正式环境HTTP接口不传该参数，通过token获取）
     */
    @NotBlank
    private String employeeId;

    /**
     * 企业ID,非accountId（正式环境HTTP接口不传该参数，通过token获取）
     */
    @NotBlank
    private String companyId;
    /**
     * 扣款企业ID,如果指定则从指定企业的红包券+商务消费账户进行扣款
     * 如果为空则默认扣本企业账户
     */
    private String paymentCompanyId;

    /**
     * 业务模式:1.pop 2托管 3采销
     *
     * @see com.fenbeitong.finhub.common.constant.BusinessModeEnum
     */
    private Integer businessMode;

    /**
     * (必填)1因公，2因私
     */
    private Integer accountType;

    /**
     * 消费的账户类型: 2,"商务账户",3, "个人账户"4, "企业账户"
     */
    private Integer accountSubType;

    /**
     * 订单渠道来源
     */
    private Integer orderChannelType;


    /**
     * 场景方订单类型
     */
    @NotNull
    private Integer orderType;

    /**
     * 场景方子订单类型
     * 订单中心-关联场景订单
     */
    private Integer orderSubType;

    /**
     * 分贝通订单号
     */
    @NotBlank
    private String fbOrderId;

    /**
     * 交易标识
     * 多次交易,每次的交易标识默认为null,perpay/finish
     */
    private String fbTradeId;

    /**
     * 订单名称
     */
    @NotBlank
    private String fbOrderName;

    /**
     * 订单快照信息
     */
    @NotBlank
    private String fbOrderSnapshot;
    /**
     * 币种
     */
    private String currency;

    /**
     * 订单总金额
     */
    @NotNull
    @Min(0)
    private BigDecimal totalPayPrice;

    /**
     * 通知场景方，支付已经完成
     */
    private String bizCallbackUrl;

    /**
     * 交易超时时间
     * @since 2.3.0
     */
    private Date deadlineTime;

    private Integer deadLineMin;

    /**
     * 公用场景
     */
    @NotNull
    private CashierPayCommonJsonDto commonJson;
    /**
     * 消费的账户类型
     * 2,"商务账户",3, "个人账户"4, "企业账户"
     */
    private Integer consumerAccountSubType;

    /**
     * 是否可以使用红包券支付1可以2不可以
     */
    private Integer redCouponCanPay;

    /**
     * 操作渠道
     *
     * @see com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType
     */
    private Integer operationChannelType;
    /**
     * 企业支付|个人垫付
     * @see com.fenbeitong.finhub.common.constant.PayModelEnum
     */
    private Integer orderPaymentModel;

    /**
     * 企业应支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal companyPayPrice;

    /**
     * 企业实际支付金额（包含保险）
     */
    private BigDecimal companyActPayPrice;

    /**
     * 企业支付时，在收银台不结算的金额（如保险，由场景单独扣除保险）单位为分，BigDecimal类型
     */
    private BigDecimal companyNoSettlePrice;

    /**
     * 企业规则中支付超规金额，如果不填，默认为companyPayPrice中的金额
     */
    private BigDecimal companyPayRulePrice;

    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;

    /**
     * 报销金额
     */
    private BigDecimal amountReimburseCompany;

    /**
     * 自费金额
     */
    private BigDecimal amountReimburseSelf;

    /**
     * 合规金额
     */
    private BigDecimal amountCompliance;
    /**
     * 不合规金额
     */
    private BigDecimal amountNonCompliance;

    /**
     * (必填)因公企业支付是否需要提前扣款:1"不提前扣款"(默认)2"提前扣款"
     */
    private Integer companyPrePay;

    /**
     * 需个人支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal personalPayPrice;
    /**
     * 分贝币支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal personFbbPayPrice;
    /**
     * 分贝券支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal personVouchersPayPrice;
    /**
     * 三方支付现金金额
     */
    @NotNull
    @Min(0)
    private BigDecimal thirdPartPayPrice;

    /**
     * 三方支付渠道
     */
    private String thirdPartChannel;

    /**
     * 备用金Id
     * 虚拟卡
     */
    private String pettyId;
    /**
     * 银行交易流水id
     */
    private String bankTransNo;
    /**
     * 开户：银行卡号
     */
    private String bankAccountNo;
    /**
     * 银行名称简写
     */
    private String bankName;

    /**
     * 是否提供发票:1提供 0不提供
     *
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideStatusEnum
     */
    private Integer invoiceProvideStatus;

    /**
     * 开票类型
     * @see com.fenbeitong.finhub.common.constant.SceneInvoiceTypeEnum
     */
    private Integer sceneInvoiceType;

    /**
     * 开票方类型
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideTypeEnum
     */
    private Integer invoiceProvideType;

    /**
     * 发票供应商名称
     */
    private String invoiceProvideName;

    /**
     *   客服id
     */
    private String customerServiceId;

    /**
     *   客服姓名
     */
    private String customerServiceName;

    /**
     * 费用归属id
     **/
    private String costAttributionId;

    /**
     * 归属类型
     */
    private Integer costAttributionType;

    /**
     * 费用归属名称
     */
    private String costAttributionName;

    /**
     * 费用归属预算扣除时间
     * 默认是当前时间
     */
    private Date costAttributionTime;

    /**
     * 父级费用归属{"pids":"","pnames":""}
     */
    private String pCostAttribution;
    /**
     * 多个费用归属集合
     */
    private List<BankCardCostAttributionDTO> attributions;

    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;

    /**
     * 预算配置项
     */
    private Integer budgetOpt;
    /**
     * 是否占用个人预算 0-不占 1-占用(不传默认占用)
     */
    private Integer usePersonalBudget;

    /**
     * 自定义字段（json）
     */
    private String customExt;
    /**
     * (保险优化新增)主单号, 场景下单时同fbOrderId，保险或其他子单下单时为场景单号，注意场景单下单时没有保险或其他子单时该字段不传
     */
    private String rootOrderId;

    /**
     * 最多使用分贝券额度,没有需要设置，可以为null,也可以设置为personalPayPrice，如果为0，认为不能用分贝券
     */
    @NotNull
    @Min(0)
    private BigDecimal maxVoucher;

    /**
     * 分贝券使用列表
     */
    private List<VoucherPayRPCVo> voucherPayDTOList;

    public int getConsumerAccountSubType(){
        return ObjUtils.isNotBlank(accountSubType)? accountSubType: OrderType.getEnum(orderType).getAccountSubType().getKey();
    }
}
