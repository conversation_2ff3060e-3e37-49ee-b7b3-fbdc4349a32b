package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import com.fenbeitong.fenbeipay.core.enums.personpay.PayChannel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by wh on 18/2/1.
 */
@Data
public class PersonPayMailContract implements Serializable {

    private String userName;
    private String phoneNum;
    private String companyName;
    private BigDecimal amount;
    private BigDecimal actureAmount;
    private String orderNo;
    private String fbOrderNo;
    private String completeTime;
    private PayChannel payChannel;
}
