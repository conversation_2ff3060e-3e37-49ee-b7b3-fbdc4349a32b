package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收银台-计算因公支付中各项支付能力支付金额
 *
 * <AUTHOR>
 * @date 2019年12月11日15:59:31
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CashierPayPublicCalculateInfo extends BaseVo {

    /**
     * 公司账户支付金额
     */
    private BigDecimal companyPayAmount;

    /**
     * 红包券支付金额
     */
    private BigDecimal redcouponPayAmount;



}
