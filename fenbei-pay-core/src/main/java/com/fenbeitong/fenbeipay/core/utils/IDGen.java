package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * id 生成工具类
 *
 * <AUTHOR> renfj
 * @date : 2019-01-04 16:06
 */
public class IDGen {

    private static final String ACCOUNT_SUB_FLOW_PREFIX = "ASF";
    private static final String ACCOUNT_SUB_PREFIX = "ASA";

    private static final String ACCOUNT_GENERAL_FLOW_PREFIX = "AGF";
    private static final String ACCOUNT_GENERAL_PREFIX = "AGA";

    private static final String ACCOUNT_FREEZEN_FLOW_PREFIX = "AFF";
    private static final String ACCOUNT_FREEZEN_PREFIX = "AFA";

    private static final String VOUCHER_PERSON_PREFIX = "FBQ";
    private static final String VOUCHER_TEMPLATE_PREFIX = "QMB";
    private static final String VOUCHERS_GRANT_WITHDRAWAL_TASK_PREFIX = "QGL";
    private static final String VOUCHER_TASK_MAIN_PREFIX = "QRW";
    private static final String VOUCHER_TASK_DETAILS_PREFIX = "QMX";
    private static final String VOUCHER_OPERATION_FLOW_PREFIX = "QLS";
    private static final String VOUCHER_TASK_TIMING_PREFIX = "QTT";
    private static final String VOUCHER_TASK_IMPORT_PREFIX = "QTI";
    private static final String VOUCHER_BATCH_TRANSFER_PREFIX = "VBT";

    private static final String BANK_CARD_ACCOUNT_PREFIX = "FCB";
    private static final String BANK_CARD_APPLY_ACCOUNT_PREFIX = "APA";
    private static final String BANK_CARD_CREDIT_DISTRIBUTE_PREFIX = "CDP";
    private static final String BANK_CARD_CREDIT_REFUND_DISTRIBUTE_PREFIX = "RFD";
    private static final String BANK_CARD_CREDIT_RETURN_DISTRIBUTE_PREFIX = "RTR";
    private static final String BANK_CARD_CREDIT_CONSUMER_DISTRIBUTE_PREFIX = "CON";

    private static final String ACCOUNT_REDCOUPON_PREFIX = "ARC";
    private static final String ACCOUNT_REDCOUPON_FLOW_PREFIX = "ARF";

    public static final String ACCOUNT_PUBLIC_PREFIX = "APB";
    public static final String ACCOUNT_PUBLIC_FLOW_PREFIX = "APF";
    public static final String ACCOUNT_PUBLIC_MFLOW_PREFIX = "APM";

    private static final String PUBLIC_ELC_PREFIX = "OPPE";
    /**
     * 企业虚拟卡账户ID前缀
     */
    private static final String ACCOUNT_COMPANY_CARD_PREFIX = "ABA";
    /**
     * 企业虚拟卡账户流水ID前缀
     */
    private static final String ACCOUNT_COMPANY_CARD_FLOW_PREFIX = "ABF";


    /**
     * 主体信息
     */
    private static final String COMPANY_MAIN_PREFIX = "CMA";

    /**
     * 对账日切
     */
    public static final String ACCOUNT_EXTRACT_DAY_PREFIX = "AED";

    /**
     * 个人对账日切
     */
    public static final String PERSON_ACCOUNT_EXTRACT_DAY_PREFIX = "PAED";

    /**
     * 个人对账日切
     */
    public static final String PERSON_ACCOUNT_BALANCE_CHECK_PREFIX = "PAEB";

    /**
     * 对账日切
     */
    public static final String ACCOUNT_EXTRACT_MONTH_PREFIX = "AEM";

    /**
     * 对公生成余额帐户
     */
    public static final String ACCOUNT_PUBLIC_HUPO_PREFIX = "AGA";

    /**
     * 个人资金账户
     */
    public static final String USER_CARD_FLOW_PREFIX = "UCF";

    public static final String EX_HANDLER_NO_PREFIX = "EXN";

    public static final String INNER_ACCT_PREFIX = "INN";

    /**
     * 虚拟卡-设置绑定入金setAccountEntrySignHW
     */
    public static final String SET_ACCT_ENTRY_SIGN_HW = "SAE";
    public static final String TRAP = "TRP";
    /**
     * 虚拟卡圈存表主键
     */
    public static final String BANK_CARD_TRAP = "BCT";

    /**
     * 广发圈存流水号
     */
    public final static String BANK_TXN_SOLVE_TRAP = "BTS";
    /**
     * 圈存流水表主键
     */
    public static final String BANK_CARD_TRAP_FLOW = "BTF";
    public static final String BIND_VIRTUAL_CARD = "BBC";

    /**
     * 个人资金提现
     */
    public static final String CASH_IN = "CAI";
    /**
     * 备用金方法记录主键
     */
    public static final String CARD_PETTY_PREFIX = "BCP";

    public static final String KEEP_ALIVE_VCARD = "KAV";
    public static final String KEEP_ALIVE_VCARD_TRAP = "KAVT";
    public static final String KEEP_ALIVE_VCARD_SOLVETRAP = "KAVS";

    /**
     * 提前还款
     */
    public static final String ACCOUNT_PREPAYMENT_PREFIX = "PREP";

    /**
     * 时间+两位随机+两位公司Id的hash
     *
     * @param companyId
     * @param prefix
     * @return
     */
    public static String genId(String companyId, String prefix) {
        if (StringUtils.isBlank(companyId)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (StringUtils.isBlank(prefix)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        StringBuilder sb = new StringBuilder(prefix);
        String tm = DateUtils.format(new Date(), "yyyyMMddHHmmssSSS");
        sb.append(tm);
        String randomNum = RandomUtils.randomNum(2);
        sb.append(randomNum);
        String userIdHash = String.valueOf(companyId.hashCode());
        String limit = userIdHash.substring(userIdHash.length() - 2);
        sb.append(limit);
        return sb.toString();
    }

    /**
     * 时间+6位随机
     *
     * @param prefix
     * @return
     */
    public static String genId(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        StringBuilder sb = new StringBuilder(prefix);
        String tm = DateUtils.format(new Date(), "yyMMddHHmmssSSS");
        sb.append(tm);
        String randomNum = RandomUtils.randomNum(6);
        sb.append(randomNum);
        return sb.toString();
    }

    /**
     * @Description: 时间+4位随机+两位因子的hash
     * @methodName: genIdByFactor
     * @Param: [factor, prefix]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/6/28 3:57 PM
     **/
    public static String genIdByFactor(String factor, String prefix) {
        if (StringUtils.isBlank(factor)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (StringUtils.isBlank(prefix)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        StringBuilder sb = new StringBuilder(prefix);
        String tm = DateUtils.format(new Date(), "yyMMddHHmmssSSS");
        sb.append(tm);
        String randomNum = RandomUtils.randomNum(4);
        sb.append(randomNum);
        String userIdHash = String.valueOf(factor.hashCode());
        String limit = userIdHash.substring(userIdHash.length() - 2);
        sb.append(limit);
        return sb.toString();
    }

    /**
     * @Description: 时间+3位字母随机+4数字随机+两位因子的hash
     * @methodName: genRandomIdByFactor
     * @Param: [factor, prefix]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2020/8/5 11:25 AM
     **/
    public static String genRandomIdByFactor(String factor, String prefix) {
        if (StringUtils.isBlank(factor)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (StringUtils.isBlank(prefix)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        StringBuilder sb = new StringBuilder(prefix);
        String tm = DateUtils.format(new Date(), "yyMMddHHmmss");
        sb.append(tm);
        sb.append(RandomUtils.randomNumAndLowerLetter(7).toUpperCase());
        String userIdHash = String.valueOf(factor.hashCode());
        String limit = userIdHash.substring(userIdHash.length() - 2);
        sb.append(limit);
        return sb.toString();
    }

    public static String genBsonIdByFactor(String factor, String prefix) {
        if (StringUtils.isBlank(factor)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        if (StringUtils.isBlank(prefix)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
        StringBuilder sb = new StringBuilder(prefix);
        sb.append(RandomUtils.bsonId());
        String userIdHash = String.valueOf(factor.hashCode());
        String limit = userIdHash.substring(userIdHash.length() - 2);
        sb.append(limit);
        return sb.toString();
    }

    /**
     * @Description: 随机获取N个大写字母
     * @methodName: getRandomLetter
     * @Param: [n]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2020/8/5 11:54 AM
     **/
    public static String getRandomLetter(int n) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < n; i++) {
            char str = (char) (int) (Math.random() * 26 + 97);
            sb.append(str);
        }
        return sb.toString().toUpperCase();
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountRedcouponId(String companyId) {
        return genId(companyId, ACCOUNT_REDCOUPON_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountRedcouponFlowId(String companyId) {
        return genId(companyId, ACCOUNT_REDCOUPON_FLOW_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountGeneralId(String companyId) {
        return genId(companyId, ACCOUNT_GENERAL_PREFIX);
    }

    /**
     * 企业虚拟卡账户ID
     * @param companyId
     * @return
     */
    public static String genCompanyBankCardAcctId(String companyId) {
        return genId(companyId, ACCOUNT_COMPANY_CARD_PREFIX);
    }

    /**
     * 企业虚拟卡账户流水Id
     * @param companyId
     * @return
     */
    public static String genCompanyBankCardAcctFlowId(String companyId) {
        return genId(companyId, ACCOUNT_COMPANY_CARD_FLOW_PREFIX);
    }

    /**
     * 个人虚拟卡账户Id
     * @param employeeId
     * @return
     */
    public static String genBankCardAccountId(String employeeId) {
        return genId(employeeId, BANK_CARD_ACCOUNT_PREFIX);
    }
    
    /**
     * 企业虚拟卡账户ID
     * @param companyId
     * @return
     */
    public static String genCompanyMainId(String companyId) {
        return genId(companyId, COMPANY_MAIN_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genApplyAccountId(String companyId) {
        return genId(companyId, BANK_CARD_APPLY_ACCOUNT_PREFIX);
    }

    public static String genCreditDistributeId(String companyId) {
        return genId(companyId, BANK_CARD_CREDIT_DISTRIBUTE_PREFIX);
    }
    public static String genCreditRefundDistributeId(String companyId) {
        return genId(companyId, BANK_CARD_CREDIT_REFUND_DISTRIBUTE_PREFIX);
    }
    public static String genCreditConsumerDistributeId(String companyId) {
        return genId(companyId, BANK_CARD_CREDIT_CONSUMER_DISTRIBUTE_PREFIX);
    }
    public static String genCreditReturnDistributeId(String companyId) {
        return genId(companyId, BANK_CARD_CREDIT_RETURN_DISTRIBUTE_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountGeneralFlowId(String companyId) {
        return genId(companyId, ACCOUNT_GENERAL_FLOW_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountSubId(String companyId) {
        return genId(companyId, ACCOUNT_SUB_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genAccountSubFlowId(String companyId) {
        return genId(companyId, ACCOUNT_SUB_FLOW_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genFreezenId(String companyId) {
        return genBsonIdByFactor(companyId, ACCOUNT_FREEZEN_PREFIX);
    }

    /**
     * @param companyId
     * @return
     */
    public static String genFreezenFlowId(String companyId) {
        return genBsonIdByFactor(companyId, ACCOUNT_FREEZEN_FLOW_PREFIX);
    }

    /**
     * @Description: 生成分贝券主任务Id
     * @methodName: getVoucherTaskId
     * @Param: [companyId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 10:51 AM
     **/
    public static String getVoucherTaskId(String companyId) {
        return genIdByFactor(companyId, VOUCHER_TASK_MAIN_PREFIX);
    }

    /**
     * @Description: 生成分贝券任务明细Id
     * @methodName: getVoucherTaskDetailsId
     * @Param: [employeeId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 10:51 AM
     **/
    public static String getVoucherTaskDetailsId(String employeeId) {
        return genRandomIdByFactor(employeeId, VOUCHER_TASK_DETAILS_PREFIX);
    }

    /**
     * @Description: 生成分贝券模板Id
     * @methodName: getVoucherTemplateId
     * @Param: [companyId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 10:52 AM
     **/
    public static String getVoucherTemplateId(String companyId) {
        return genIdByFactor(companyId, VOUCHER_TEMPLATE_PREFIX);
    }

    /**
     * @Description: 生成分贝券操作流水Id
     * @methodName: getVoucherOperationFlowId
     * @Param: [employeeId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 10:52 AM
     **/
    public static String getVoucherOperationFlowId(String employeeId) {
        return genRandomIdByFactor(employeeId, VOUCHER_OPERATION_FLOW_PREFIX);
    }

    /**
     * @Description: 生成分贝券发放撤回任务Id
     * @methodName: getVoucherGrantWithdrawalTaskId
     * @Param: [companyId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 10:52 AM
     **/
    public static String getVoucherGrantWithdrawalTaskId(String companyId) {
        return genIdByFactor(companyId, VOUCHERS_GRANT_WITHDRAWAL_TASK_PREFIX);
    }

    /**
     * @Description: 生成券Id
     * @methodName: getVoucherPersonId
     * @Param: [employeeId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/5/21 11:09 AM
     **/
    public static String getVoucherPersonId(String employeeId) {
        return genRandomIdByFactor(employeeId, VOUCHER_PERSON_PREFIX);
    }

    /**
     * @Description: 生成分贝券定时任务ID
     * @methodName: getVoucherTaskTimingId
     * @Param: [companyId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/6/28 3:53 PM
     **/
    public static String getVoucherTaskTimingId(String companyId) {
        return genIdByFactor(companyId, VOUCHER_TASK_TIMING_PREFIX);
    }

    /**
     * @Description: 生成分贝券批量导入任务ID
     * @methodName: getVoucherTaskTimingId
     * @Param: [companyId]
     * @return: java.lang.String
     * @Author: zhangga
     * @Date: 2019/6/28 3:53 PM
     **/
    public static String getVoucherTaskImportId(String companyId) {
        return genIdByFactor(companyId, VOUCHER_TASK_IMPORT_PREFIX);
    }

    public static String getVoucherBatchTransferId(String employeeId) {
        return genRandomIdByFactor(employeeId, VOUCHER_BATCH_TRANSFER_PREFIX);
    }

    public static String genPublicPayElc(String userId) {
        return genId(userId,PUBLIC_ELC_PREFIX);
    }


    public static String genAccountInnerFlowId(String companyId){
        return genId(companyId,INNER_ACCT_PREFIX);
    }

    public static String genUpgradeId(String companyId){
        return genId(companyId,"UGI");
    }

}
