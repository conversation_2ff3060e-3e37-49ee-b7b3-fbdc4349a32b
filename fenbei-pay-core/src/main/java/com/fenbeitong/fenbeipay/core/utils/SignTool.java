package com.fenbeitong.fenbeipay.core.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.StringJoiner;

/**
 * Created by x<PERSON><PERSON> on 2017/8/27.
 */
public class SignTool {

    private static Logger LOGGER = LoggerFactory.getLogger(SignTool.class);

    public static String genSign(long timestamp, String data, String signKey) throws UnsupportedEncodingException {
        LinkedHashMap<String, Object> param = new LinkedHashMap<>();
        param.put("timestamp", timestamp);
        param.put("data", data);
        return genSign(param, signKey);
    }

    //===============>以下都是辅助方法<===============

    /**
     * 根据参数和 signKey 生成签名
     */
    private static String genSign(Map<String, Object> param, String signKey) throws UnsupportedEncodingException {
        /** 1、生成签名的时候,将颁发的 sign_key 加入到传递的参数中，参与加密 */
        param.put("sign_key", signKey);
        /** 2、传递的参数(包含 sign_key )，已 & 形式连接 k=v，生成小写的 md5 串 */
        String joinParam = joinParam(param);
        /** 3、生成小写的 md5 串 */
        LOGGER.info("传递的参数进行加密，joinParam={}"+joinParam);

        byte[] bytes = joinParam.getBytes("utf-8");

        return DigestUtils.md5Hex(bytes);
    }

    /**
     * 使用&、=拼接变量名和变量值
     */
    private static String joinParam(Map<String, Object> param) {
        StringJoiner stringJoiner = new StringJoiner("&");
        param.forEach((k, v) -> stringJoiner.add(k + "=" + v));
        return stringJoiner.toString();
    }

}
