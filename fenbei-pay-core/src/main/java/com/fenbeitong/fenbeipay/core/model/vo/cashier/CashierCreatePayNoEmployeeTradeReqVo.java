package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.OrderType;
import com.fenbeitong.fenbeipay.api.model.dto.bank.req.BankCardCostAttributionDTO;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.constant.*;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 收银创建支付流水请求VO
 * <AUTHOR>
 */
@Data
@ToString
public class CashierCreatePayNoEmployeeTradeReqVo extends CashierBasePersonVo {

    /**
     * 场景方订单类型
     */
    @NotNull
    private Integer orderType;

    @NotBlank
    @Length(min = 1)
    private String fbOrderId;
    /**
     * 订单名称
     */
    @NotNull
    @Length(min = 1)
    private String fbOrderName;
    /**
     * 订单快照信息
     */
    @NotBlank
    @Length(min = 1)
    private String fbOrderSnapshot;
    /**
     * 币种
     */
    private String currency;
    /**
     * 企业支付|个人垫付
     * @see PayModelEnum
     */
    private Integer orderPaymentModel;

    /**
     *  报销金额
     */
    private BigDecimal amountReimburseCompany = BigDecimal.ZERO;

    /**
     *  自费金额
     */
    private BigDecimal amountReimburseSelf = BigDecimal.ZERO;

    /**
     *   合规金额
     */
    private BigDecimal amountCompliance = BigDecimal.ZERO;
    /**
     *   不合规金额
     */
    private BigDecimal amountNonCompliance = BigDecimal.ZERO;

    /**
     * 订单总金额
     */
    @NotNull
    @Min(0)
    private BigDecimal totalPayPrice = BigDecimal.ZERO;

    /**
     * 企业应支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal companyPayPrice = BigDecimal.ZERO;
    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount = BigDecimal.ZERO;



    /**
     * 企业支付时，在收银台不结算的金额（如保险，由场景单独扣除保险）单位为分，BigDecimal类型
     */
    private BigDecimal companyNoSettlePrice = BigDecimal.ZERO;

    /**
     * 企业规则中支付超规金额，如果不填，默认为companyPayPrice中的金额
     */
    private BigDecimal companyPayRulePrice;

    /**
     * 通知场景方，支付已经完成
     */
    private String bizCallbackUrl;

    /**
     * 交易超时分钟数（多少分钟数之后超时）
     */
    @Deprecated
    private Integer deadLineMin;

    /**
     * 交易超时时间
     * @since 2.3.0
     */
    private Date deadlineTime;

    /**
     * 公用场景
     */
    private CashierPayCommonJsonDto commonJson;

    /**
     * 费用归属id
     **/
    private String costAttributionId;

    /**
     * 归属类型
     */
    private Integer costAttributionType;

    /**
     * 费用归属名称
     */
    private String costAttributionName;

    /**
     * 费用归属预算扣除时间
     * 默认是当前时间
     */
    private Date  costAttributionTime;

    /**
     * 父级费用归属{"pids":"","pnames":""}
     */
    private String pCostAttribution;

    /**
     * 自定义字段（json）
     */
    private String customExt;

    /**
     * 业务模式:1.pop 2托管 3采销
     * @see BusinessModeEnum
     */
    private Integer businessMode;

    /**
     * 是否提供发票:1提供 0不提供
     * @see InvoiceProvideStatusEnum
     */
    private Integer invoiceProvideStatus;

    /**
     * 开票类型
     * @see SceneInvoiceTypeEnum
     */
    private Integer sceneInvoiceType;

    /**
     * 开票方类型
     * @see InvoiceProvideTypeEnum
     */
    private Integer invoiceProvideType;

    /**
     * 发票供应商名称
     */
    private String invoiceProvideName;
    /** 消费的账户类型
      * 2,"商务账户",3, "个人账户"4, "企业账户"
      */
    @NotNull
    @Min(0)
    private Integer accountSubType;

    /**
     * 订单渠道来源
     */
    private Integer orderChannelType;

    /**
     * @see OperationChannelType
     */
    private Integer  operationChannelType = OperationChannelType.NORMAL.getKey();

    /**
     * 费用归属集合
     */
    private List<BankCardCostAttributionDTO> attributions;

    /**
     * 费用归属配置项
     */
    private Integer costAttributionOpt;

    /**
     * 预算配置项
     */
    private Integer budgetOpt;

    /**
     * 员工姓名
     */
    private String employeeName;
    /**
     * 员工手机号
     */
    private String employeePhone;
    /**
     * 是否可以使用红包券支付1可以2不可以
     */
    private Integer redcouponCanPay;

    /**
     * 0税率商品金额
     */
    private BigDecimal amountZeroTax;

    /**
      * @Description: 是否有费用归属
      * 存在 true 不存在false
      */
    public boolean isCostAttribution(){
        return ObjUtils.isNotBlank(costAttributionId) && ObjUtils.isNotBlank(costAttributionType);
    }

    public int getConsumerAccountSubType(){
        return ObjUtils.isNotBlank(accountSubType)? accountSubType: OrderType.getEnum(orderType).getAccountSubType().getKey();
    }

    /**
     * 实际扣除公司多少钱
     * @return
     */
    public BigDecimal getCompanyNetSettlePrice(){
        BigDecimal companyNetSettlePrice = companyPayPrice;
        if(BigDecimalUtils.hasPrice(companyNoSettlePrice)){
            companyNetSettlePrice =  companyPayPrice.subtract(companyNoSettlePrice);
        }
        return companyNetSettlePrice;
    }

    public boolean isCompanyPayModel(){
        if(ObjUtils.isNull(orderPaymentModel)){
            orderPaymentModel = PayModelEnum.COMPANY_PAY.getCode();
        }
        return PayModelEnum.isCompanyPay(orderPaymentModel);
    }

    public boolean isPersonalPrePayModel(){
        return PayModelEnum.isPersonalPrepay(orderPaymentModel);
    }

}
