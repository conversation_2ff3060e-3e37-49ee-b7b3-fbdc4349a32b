package com.fenbeitong.fenbeipay.core.model.vo.newaccount;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class AccountSubIndividualRespVo extends AccountSubRespVo {

    /**
     * 分贝券-本月消费总额
     */
   private BigDecimal voucherConsumeAmount;

    /**
     * 分贝券-本月发放总额
     */
   private BigDecimal voucherGrantAmount;

    /**
     * 分贝券-本月撤回总额
     */
   private BigDecimal voucherWithdrawalAmount;

}