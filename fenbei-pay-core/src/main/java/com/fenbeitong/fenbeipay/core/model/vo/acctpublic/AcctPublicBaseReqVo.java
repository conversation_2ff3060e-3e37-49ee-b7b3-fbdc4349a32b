package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.model.dto.BaseDTO;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Title: BankCardBaseReqDTO
 * @ProjectName fenbei-pay
 * @Description: 卡的基本信息
 * @author: wh
 * @date 2019/9/20 10:39
 */
@Data
public class AcctPublicBaseReqVo extends BaseDTO {

    /**
     * 公司Id
     */
    @NotBlank
    private String companyId;


    /**
     * 当前操作业务ID：场景标识
     */
    @NotBlank
    private String bizNo;

    /**
     * FBT虚户卡：开户行名称
     */
    @NotBlank
    private String bankAccountName;

}
