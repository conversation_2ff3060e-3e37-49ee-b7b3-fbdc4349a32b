package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherFlowType4Stereo;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.ExtractAccountTypeEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by fubaoxin on 2019/1/8.
 */
public class EnumUtils {

    /**
     * 查询子账户流水 -订单场景类型
     * CategoryTypeEnum
     *
     * @return
     */
    public static List<Map<String, Object>> getCategoryTypeEnumList() {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (CategoryTypeEnum type : CategoryTypeEnum.values()) {
            if (!Objects.equals(-1, type.getCode())) {
                Map<String, Object> type1 = Maps.newHashMap();
                type1.put("value", type.getCode());
                type1.put("name", type.getName());
                list.add(type1);
            }
        }
        return list;
    }

    /**
     * 查询子账户流水 -账户类型
     * AccountSubType
     *
     * @return
     */
    public static List<Map<String, Object>> getAccountSubTypeList() {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (FundAccountSubType type : FundAccountSubType.values()) {
            if (!Objects.equals(-1, type.getKey()) && !Objects.equals(4, type.getKey())) {
                Map<String, Object> type1 = Maps.newHashMap();
                type1.put("value", type.getKey());
                type1.put("name", type.getValue());
                list.add(type1);
            }
        }
        return list;
    }



    public static List<Map<String, Object>> getVoucherFlowType4Stereo() {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (VoucherFlowType4Stereo type : VoucherFlowType4Stereo.values()) {
            if (!Objects.equals(-1, type.getValue())) {
                Map<String, Object> type1 = Maps.newHashMap();
                type1.put("value", type.getValue());
                type1.put("name", type.getMsg());
                list.add(type1);
            }
        }
        return list;


    }

    public static List<Map<String, Object>> getWriteInvoiceType() {
        List<Map<String, Object>> list = Lists.newArrayList();
        for (WriteInvoiceType type : WriteInvoiceType.values()) {
            if (!Objects.equals(-1, type.getValue())) {
                Map<String, Object> type1 = Maps.newHashMap();
                type1.put("value", type.getValue());
                type1.put("name", type.getMsg());
                list.add(type1);
            }
        }
        return list;


    }

    /**
     * 账户日终/月终余额查询 - 账户类型
     */
    @Deprecated
    public static List<Map<String, Object>> getExtractAccountSubType() {
        List<Map<String, Object>> list = Lists.newArrayList();
        //普通子账户类型
        for (ExtractAccountTypeEnum type : ExtractAccountTypeEnum.values()) {
            if (!Objects.equals(-1, type.getAccountType())) {
                Map<String, Object> type1 = Maps.newHashMap();
                type1.put("value", type.getAccountType());
                type1.put("name", type.getDesc());
                list.add(type1);
            }
        }
        //账户总额
        return list;
    }





}
