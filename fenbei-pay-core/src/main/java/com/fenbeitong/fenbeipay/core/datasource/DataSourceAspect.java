package com.fenbeitong.fenbeipay.core.datasource;

import com.fenbeitong.finhub.common.utils.FinhubLogger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 多数据源切面处理类
 *
 * <AUTHOR>
 */
@Order(1)
@Aspect
@Component
public class DataSourceAspect {

    // @within匹配类上的注解  @annotation匹配方法上的注解
    @Pointcut("@annotation(com.fenbeitong.fenbeipay.core.datasource.CurrentDataSource)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        CurrentDataSource ds = method.getAnnotation(CurrentDataSource.class);
        if (ds == null) {
            DynamicDataSource.setDataSourceKey(DataSourceNames.MASTER);
        } else {
            DynamicDataSource.setDataSourceKey(ds.name());
        }
        FinhubLogger.debug("当前连接的数据库为{}", ds.name());
        try {
            return point.proceed();
        } finally {
            DynamicDataSource.toDefault();
        }
    }
}
