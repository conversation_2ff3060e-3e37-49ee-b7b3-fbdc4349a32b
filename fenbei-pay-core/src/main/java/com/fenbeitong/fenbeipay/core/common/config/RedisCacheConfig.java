package com.fenbeitong.fenbeipay.core.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * RedisCacheConfig Class
 *
 * <AUTHOR>
 * @date 2018/09/17
 **/
@Configuration
public class RedisCacheConfig {

    private static long ONE_DAY = 60L * 60L * 24L;

    private String appname = "fenbei-pay";

    @Autowired
    private RedisTemplate redisTemplate;

    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager cacheManager = new RedisCacheManager(redisTemplate);

        Map<String, Long> expires = new HashMap<>();

        expires.put("voucherstype", ONE_DAY);

        expires.put("pay_pwd", ONE_DAY);

        // 设置超时
        cacheManager.setExpires(expires);

        // 没有设置的缓存默认过期时间
        cacheManager.setDefaultExpiration(60);
        cacheManager.setCachePrefix(new RedisPrefix(appname));
        cacheManager.setUsePrefix(true);

        return cacheManager;
    }
}
