package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;

/**
 * Created by xiaoxiong on 2017/10/23.
 */
public class EmployeeAlipayContract implements Serializable {

    private String alipayAccount;

    private Integer operateType;

    public String getAlipayAccount() {
        return alipayAccount;
    }

    public void setAlipayAccount(String alipayAccount) {
        this.alipayAccount = alipayAccount;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public EmployeeAlipayContract() {
    }

    public EmployeeAlipayContract(String alipayAccount, Integer operateType) {
        this.alipayAccount = alipayAccount;
        this.operateType = operateType;
    }
}
