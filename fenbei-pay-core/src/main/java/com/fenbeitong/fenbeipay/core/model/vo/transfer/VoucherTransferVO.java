package com.fenbeitong.fenbeipay.core.model.vo.transfer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: VoucherTransferVO
 * @Author: zhangga
 * @CreateDate: 2019/1/15 12:31 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/15 12:31 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class VoucherTransferVO {
    /**
     * 转赠金额（单位分）
     */
    private BigDecimal transferAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 转账描述
     **/
    private String title;

    /**
     * 转账类型 1-转入；2-转出
     **/
    private Integer type;

    /**
     * 创建时间  2022/01/02
     */
    private String createTimeStr;

    public String getCreateTimeStr() {
        if (this.createTime != null){
            this.createTimeStr = cn.hutool.core.date.DateUtil.format(createTime, "yyyy/MM/dd HH:mm:ss");
        }
        return createTimeStr;
    }
}
