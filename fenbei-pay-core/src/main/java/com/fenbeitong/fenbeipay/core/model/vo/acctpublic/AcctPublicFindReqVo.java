package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.model.dto.BaseDTO;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;

/**
 * @Title: BankCardBaseReqDTO
 * @ProjectName fenbei-pay
 * @Description: 卡的基本信息
 * @author: wh
 * @date 2019/9/20 10:39
 */
@Data
public class AcctPublicFindReqVo extends BaseDTO {

    /**
     * 虚拟卡：银行账户名称
     */
    protected String bankAccountAcctName;

    protected String companyId;


    public void checkReq(){
        if (ObjUtils.isBlank(companyId) ){
            throw new ValidateException("参数错误，公司不可以为空！");
        }
    }
}
