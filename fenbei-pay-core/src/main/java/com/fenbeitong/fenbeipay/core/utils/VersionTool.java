package com.fenbeitong.fenbeipay.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by xiabin on 2017/4/27.
 * 版本检查工具
 */
public class VersionTool {
    /**
     * 等于
     *
     * @param srcVersion
     * @param desVersion
     * @return
     */
    public static boolean areEqual(String srcVersion, String desVersion) {
        int val = compare(srcVersion, desVersion);
        return val == 0;
    }

    /**
     * 前者大于后者
     *
     * @param srcVersion
     * @param desVersion
     * @return
     */
    public static boolean greaterThan(String srcVersion, String desVersion) {
        int val = compare(srcVersion, desVersion);
        return val > 0;
    }

    /**
     * 前者大于等于后者
     *
     * @param srcVersion
     * @param desVersion
     * @return
     */
    public static boolean greaterThanOrEqualTo(String srcVersion, String desVersion) {
        int val = compare(srcVersion, desVersion);
        return val >= 0;
    }

    /**
     * 前者小于后者
     *
     * @param srcVersion
     * @param desVersion
     * @return
     */
    public static boolean lessThan(String srcVersion, String desVersion) {
        int val = compare(srcVersion, desVersion);
        return val < 0;
    }

    /**
     * 前者小于等于后者
     *
     * @param srcVersion
     * @param desVersion
     * @return
     */
    public static boolean lessThanOrEqualTo(String srcVersion, String desVersion) {
        int val = compare(srcVersion, desVersion);
        return val <= 0;
    }

    /**
     * 比较两个版本
     * 只能识别数字，如果出现字母，则识别为0
     *
     * @param srcVersion
     * @param desVersion
     * @return 等于返回0
     * 前者大于后者返回1
     * 前者小于后者返回-1
     */
    public static int compare(String srcVersion, String desVersion) {
        if (StringUtils.isEmpty(srcVersion)) {
            srcVersion = "0";
        }
        if (StringUtils.isEmpty(desVersion)) {
            desVersion = "0";
        }
        List<String> srcVersionParams = new ArrayList<>();
        srcVersionParams.addAll(Arrays.asList(srcVersion.split("\\.")));
        List<String> desVersionParams = new ArrayList<>();
        desVersionParams.addAll(Arrays.asList(desVersion.split("\\.")));
        int srcLen = srcVersionParams.size();
        int desLen = desVersionParams.size();
        if (srcLen > desLen) {
            for (int i = desLen; i < srcLen; i++) {
                desVersionParams.add("0");
            }
        } else if (srcLen < desLen) {
            for (int i = srcLen; i < desLen; i++) {
                srcVersionParams.add("0");
            }
        }
        for (int i = 0; i < srcVersionParams.size(); i++) {
            String srcParam = srcVersionParams.get(i);
            String desParam = desVersionParams.get(i);
            int srcParamInt = 0;
            try {
                srcParamInt = Integer.parseInt(srcParam);
            } catch (Exception ex) {

            }
            int desParamInt = 0;
            try {
                desParamInt = Integer.parseInt(desParam);
            } catch (Exception ex) {

            }
            if (srcParamInt > desParamInt) {
                return 1;
            } else if (srcParamInt < desParamInt) {
                return -1;
            }
        }
        return 0;
    }
}
