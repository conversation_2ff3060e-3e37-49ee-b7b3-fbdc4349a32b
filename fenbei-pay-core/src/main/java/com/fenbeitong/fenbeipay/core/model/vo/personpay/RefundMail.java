package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;

import java.util.Date;

/**
 * 退款异常邮件VO
 * <AUTHOR>
 * @version 2.1.2
 *
 */
public class RefundMail extends BaseVo {


    /**
     * 逆向订单号
     */
    private String id;

    /**
     * 正向订单号
     */
    private String orderId;

    /**
     * 付钱拉系统订单号
     */
    private String txnId;

    /**
     * 逆向流水号
     */
    private String refundNo;

    /**
     *  退款金额
     */
    private Integer refundAmount;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     *  7,退款处理中，8退款成功，9退款失败
     */
    private Integer refundStatus;

    /**
     * 退款接收时间
     */
    private Date receiveTime;

    /**
     *
     */
    private Date updateTime;
}
