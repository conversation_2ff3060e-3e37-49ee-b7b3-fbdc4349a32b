package com.fenbeitong.fenbeipay.core.service.bigdata;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.bigdata.HoloAdsAccountAllFlowADMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowAD;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample;

import lombok.extern.slf4j.Slf4j;

/**
 * 大数据-流水查询
 */
@Service
@Slf4j
public class AccountAllFlowADManager {

    @Autowired
    private HoloAdsAccountAllFlowADMapper mapper;


    public List<String> queryAccountFlowIdsForPage(HoloAdsAccountAllFlowADExample example) {
        return mapper.selectAccountFlowIds(example);
    }

    public Long queryCount(HoloAdsAccountAllFlowADExample example) {
        return mapper.countByExample(example);
    }

    public List<HoloAdsAccountAllFlowAD> queryEntityByCondition(HoloAdsAccountAllFlowADExample example) {
        return mapper.selectByExample(example);
    }

}
