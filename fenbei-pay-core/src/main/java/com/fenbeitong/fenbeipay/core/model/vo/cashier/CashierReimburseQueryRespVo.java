package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.finhub.common.constant.PayModelEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2020年10月16日09:56:52
 * @since V4.5.0
 */
@Data
public class CashierReimburseQueryRespVo {


    /***
     * 分贝订单id
     */
    private String fbOrderId;
    /***
     * 1:因私订单 2:因公订单
     */
    private Integer accountType;
    /***
     * 收银台支付流水号
     */
    private String cashierTxnId;
    /**
     * 公司id
     */
    private String companyId;

    private String voucherCompanyId;

    /**
     * 企业支付|个人垫付
     * @see PayModelEnum
     */
    private Integer orderPaymentModel;
    /**
     * 支付完成时间
     */
    private Date completeTime;


    private BigDecimal totalRefundAmount = BigDecimal.ZERO;
    /**
     *   报销金额
     */
    private BigDecimal amountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal amountReimburseSelf = BigDecimal.ZERO;



    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;

    /**
     * 子账户id
     */
    private String accountSubId;
}
