package com.fenbeitong.fenbeipay.core.model.dto.account;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 备用金张动账通知DTO
 */
@Data
public class AccountChangeDTO implements Serializable {

    Integer accountSubType;
    String companyId; 
    Integer key; 
    BigDecimal balance;
    Integer orderType;
    /**
     * 单位分
     */
    BigDecimal operationAmount;
    Integer accountModel;
    Boolean companyAcctModelSwitch = false;
    /**
     * 银行名称(中文)
     */
    String bankName;
}
