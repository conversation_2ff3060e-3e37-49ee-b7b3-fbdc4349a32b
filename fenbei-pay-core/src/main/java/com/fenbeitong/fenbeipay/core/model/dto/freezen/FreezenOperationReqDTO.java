package com.fenbeitong.fenbeipay.core.model.dto.freezen;

import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenChangeType;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * @Description: 冻结指定账户的金额
 * @Author: wh
 * @Date: 2019/3/20 5:15 PM
 */
@Data
public class FreezenOperationReqDTO extends FreezenBaseReqDTO {
    /**
     * 操作金额 单位：分
     */
    @Min(0)
    private BigDecimal operationAmount;

    /**
     * 操作人
     */
    @NotBlank
    private String operationUserId;

    /**
     * 操作人姓名
     */
    @NotBlank
    private String operationUserName;

    /**
     * 操作原因
     */
    private String operationDescription;

    /**
     * 业务编码Id：比如订单消费为订单号ID
     */
    @NotBlank
    private String bizNo;

    /**
     * 业务票据ID(操作凭证),退款时为退款单号
     */
    private String reBizNo;

    /**
     * 冻结池流水id
     **/
    private String freezenFlowId;

    /**
     * 业务在流水记录中唯一标识，订单类的为订单ID,与bizNo一样，分贝券类的为冻结任务ID等
     */
    private String verifyNo;
    /**
     * 分贝券id
     **/
    private String voucherId;

    /**
     * 银行交互账户
     */
    private String bankAcctId;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 对手账户银行卡号
     */
    private String targetBankAccountNo;

    /**
     * 对手银行名称
     */
    private String targetBankName;
    /**
     * 对手账号虚户ID
     */
    private String targetBankAcctId;

    /**
     * 操作类型
     */
    private FreezenChangeType operationType;

    /**
     * 担保账户主体id
     */
    private String guaranteeCompanyMainId;

    /**
     * 对手账户主体id
     */
    private String targetCompanyMainId;

    /**
     * 担保账户id
     */
    private String guaranteeAccountId;

    /**
     * 对手账户id
     */
    private String targetAccountId;

    public void validate() {
        ValidateUtils.validate(this);
        CheckUtils.checkBlank(freezenUseType, "冻结类型不可以为空!");
    }

}
