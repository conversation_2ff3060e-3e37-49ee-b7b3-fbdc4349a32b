package com.fenbeitong.fenbeipay.core.service.employee;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.model.dto.acct.req.GroupAcctReqDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.FxCompanyAcctInfo;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.fenbeipay.core.constant.personpay.EmployeeStatus;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.model.vo.account.AccountInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.BaseAccountVO;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyAccountInfo;
import com.fenbeitong.fenbeipay.core.model.vo.account.CompanyInfoVO;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.company.GroupCompany;
import com.fenbeitong.usercenter.api.model.dto.employee.*;
import com.fenbeitong.usercenter.api.model.dto.group.GroupDataPrivilegeReqDTO;
import com.fenbeitong.usercenter.api.model.dto.group.GroupDataPrivilegeResDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthPermissionResCompanyDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.GroupAuthPermissionResDTO;
import com.fenbeitong.usercenter.api.model.po.company.Company;
import com.fenbeitong.usercenter.api.model.po.employee.Employee;
import com.fenbeitong.usercenter.api.service.company.IRCompanyService;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.fenbeitong.usercenter.api.service.employee.IREmployeeService;
import com.fenbeitong.usercenter.api.service.group.IGroupPrivilegeService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 用户处理类
 * @ClassName: EmployeeService
 * @Author: zhangga
 * @CreateDate: 2019/7/4 7:07 PM
 * @UpdateUser:
 * @UpdateDate: 2019/7/4 7:07 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service("employeeService")
public class EmployeeService {


    public static final String EMPLOYEE_LEAVES = "（已离职）";
    @Autowired
    private IBaseEmployeeExtService baseEmployeeExtService;
    @Autowired
    private IREmployeeService irEmployeeService;
    @Autowired
    private IGroupPrivilegeService iGroupPrivilegeService;
    @Autowired
    private IRCompanyService iRCompanyService;

    public EmployeeContract queryEmployeeInfo(String employeeId, String companyId) {
        try {
            return baseEmployeeExtService.queryEmployeeInfo(employeeId, companyId);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询用户信息异常，用户Id：{}，公司ID：{}", employeeId, companyId, e);
            throw e;
        }
    }

    public Map<String, EmployeeContract> getEmployeeMap(String companyId, List<String> employeeIdList) {
        Map<String, EmployeeContract> employeeMap = new HashMap<>();
        if (ObjUtils.isEmpty(employeeIdList)) {
            return employeeMap;
        }
        try {
            //用户信息查询
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOList = Lists.newArrayList();
            if (employeeIdList.size() > 100){
                List<List<String>> partitionList = Lists.partition(employeeIdList, 100);
                for (List<String> partition : partitionList) {
                    List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = baseEmployeeExtService.queryAllEmployeeInfByIdList(companyId,partition);
                    employeeOrgUnitDTOList.addAll(employeeOrgUnitDTOS);
                }
            }else {
                employeeOrgUnitDTOList = baseEmployeeExtService.queryAllEmployeeInfByIdList(companyId,employeeIdList);
            }
            if(CollectionUtils.isNotEmpty(employeeOrgUnitDTOList)){
                employeeOrgUnitDTOList.forEach(employeeOrgUnitDTO -> employeeMap.put(employeeOrgUnitDTO.getEmployeeId(), covertEmployeeInfo(employeeOrgUnitDTO)));
            }
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，公司ID：{}，员工ID集合：{}", companyId, employeeIdList, e);
        }
        return employeeMap;
    }

    public EmployeeSimpleInfoContract getEmployeeSimpleInfo(String employeeId, String companyId) {
        try {
            return baseEmployeeExtService.getEmployeeSimpleInfo(employeeId, companyId);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，公司ID：{}，员工ID：{}", companyId, employeeId, e);
            throw e;
        }
    }

    public EmployeeContract queryEmployeeInfoByPhone(String companyId, String phone) {
        try {
            return baseEmployeeExtService.queryEmployeeInfoByPhone(companyId, phone);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，公司ID：{}，手机号：{}", companyId, phone, e);
            throw e;
        }
    }

    public Map<String, EmployeeContract> getEmployeeMapByPhone(String companyId, List<String> employeePhones) {
        Map<String, EmployeeContract> employeeMap = new HashMap<>();
        if (ObjUtils.isEmpty(employeePhones)) {
            return employeeMap;
        }
        try {
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOList = baseEmployeeExtService.queryAllEmployeeInfoByPhoneList(companyId, employeePhones);
            if(CollectionUtils.isNotEmpty(employeeOrgUnitDTOList)){
                employeeOrgUnitDTOList.forEach(employeeOrgUnitDTO -> employeeMap.put(employeeOrgUnitDTO.getPhoneNum(), covertEmployeeInfo(employeeOrgUnitDTO)));
            }
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，公司ID：{}，手机号集合：{}", companyId, employeePhones, e);
        }
        return employeeMap;
    }

    /**
     * 根据员工id查询相关信息，查不到会抛异常
     * @param employeeId
     * @return
     */
    public EmployeeSimpleInfoContract getEmployeeByEmployeeId(String employeeId) {
        return getEmployeeByEmployeeId(employeeId, true);
    }

    /**
     * 根据员工id查询相关信息，查不到不会抛异常
     * @param employeeId
     * @return
     */
    public EmployeeSimpleInfoContract getEmployeeByIdWithoutException(String employeeId) {
        return getEmployeeByEmployeeId(employeeId, false);
    }

    public EmployeeSimpleInfoContract getEmployeeByEmployeeId(String employeeId, boolean shouldThrowException) {
        try {
            return baseEmployeeExtService.getEmployeeInfoByEmloyeeId(employeeId);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID：{}", employeeId, e);

            if (shouldThrowException) {
                throw e;
            } else {
                return null;
            }
        }
    }

    public EmployeeSimpleInfoContract getEmployeeById(String employeeId) {
        try {
            return baseEmployeeExtService.getEmployeeInfoById(employeeId);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID：{}", employeeId, e);
            throw e;
        }
    }

    public Map<String, EmployeeOrgUnitDTO> getEmployeeInfoMap(String companyId, List<String> employeeIdList) {
        Map<String, EmployeeOrgUnitDTO> employeeMap = new HashMap<>();
        if (ObjUtils.isEmpty(employeeIdList)) {
            return employeeMap;
        }
        try {
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = baseEmployeeExtService.queryAllEmployeeInfByIdList(companyId, employeeIdList);
            employeeOrgUnitDTOS.forEach(employee -> {
                buildEmployeeInfo(employee);
                employeeMap.put(employee.getEmployeeId(), employee);
            });
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeIdList, e);
        }
        return employeeMap;
    }

    public Map<String, Employee> getEmployeeInfoMap(List<String> employeeIdList) {
        Map<String, Employee> employeeMap = new HashMap<>();
        if (ObjUtils.isEmpty(employeeIdList)) {
            return employeeMap;
        }
        try {
            List<Employee> employees = irEmployeeService.listIdMatchEmployees(employeeIdList);
            if (CollectionUtils.isEmpty(employees)) {
                return employeeMap;
            }
            employees.forEach(employee -> employeeMap.put(employee.getId(), employee));
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeIdList, e);
        }
        return employeeMap;
    }

    public List<EmployeeOrgUnitDTO> getEmployeeInfoList(String companyId, List<String> employeeIdList) {
        if (ObjUtils.isEmpty(employeeIdList)) {
            return new ArrayList<>();
        }
        try {
            return baseEmployeeExtService.queryEmployeeOrgUnitDTOListByCompanyIdAndEmployeeId(companyId, employeeIdList);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeIdList, e);
        }
        return new ArrayList<>();
    }

    public Map<String, EmployeeDimissionDTO> getDimissionEmployeeInfoMap(String companyId, List<String> employeeIdList) {
        Map<String, EmployeeDimissionDTO> employeeMap = new HashMap<>();
        if (ObjUtils.isEmpty(employeeIdList)) {
            return employeeMap;
        }
        try {
            List<EmployeeDimissionDTO> employeeDimissionDTOS = baseEmployeeExtService.queryDimissionList(companyId, employeeIdList);
            employeeDimissionDTOS.forEach(employee -> {
                employeeMap.put(employee.getEmployeeId(), employee);
            });
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeIdList, e);
        }
        return employeeMap;
    }

    public EmployeeOrgUnitDTO getEmployeeInfo(String companyId, String employeeId) {
        try {
            List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = baseEmployeeExtService.queryEmployeeOrgUnitDTOListByCompanyIdAndEmployeeId(companyId, Lists.newArrayList(employeeId));
            if (ObjUtils.isEmpty(employeeOrgUnitDTOS)) {
                return null;
            }
            return employeeOrgUnitDTOS.get(0);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeId, e);
            return null;
        }
    }

    public int getEmployeeNumberByCompanyId(String companyId) {
        if (ObjUtils.isBlank(companyId)) {
            return 0;
        }
        return baseEmployeeExtService.queryEmployeeCount(companyId, 2, companyId);
    }

    public int getEmployeeNumberByDepartmentId(String companyId, String departmentId) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(departmentId)) {
            return 0;
        }
        return baseEmployeeExtService.queryEmployeeCount(companyId, 2, departmentId);
    }
    /**
     * 仅查询启用员工
     * @param companyId
     * @return
     */
    public int countEnabledEmployee(String companyId) {
        if (ObjUtils.isBlank(companyId)) {
            return 0;
        }
        return irEmployeeService.countEnabledEmployee(companyId, 2, companyId);
    }

    public int countEnabledEmployeeByDepartmentId(String companyId, String departmentId) {
        if (ObjUtils.isBlank(companyId) || ObjUtils.isBlank(departmentId)) {
            return 0;
        }
        return irEmployeeService.countEnabledEmployee(companyId, 2, departmentId);
    }

    /**
     * @Description: 查询员工以及部门信息
     * @methodName: getEmployeeInfoByDepartmentId
     * @Param: [companyId, departmentId, pageIndex, pageSize]
     * @return: java.util.List<com.fenbeitong.usercenter.api.model.dto.employee.EmployeeOrgUnitDTO>
     * @Author: zhangga
     * @Date: 2021/2/7 5:36 下午
     **/
    public List<EmployeeOrgUnitDTO> getEmployeeInfoByDepartmentId(String companyId, String departmentId, Integer pageIndex, Integer pageSize) {
        List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = baseEmployeeExtService.queryEmployeeListByDeptId(2, companyId, departmentId, pageSize, pageIndex);
        if (ObjUtils.isNotEmpty(employeeOrgUnitDTOS)) {
            return employeeOrgUnitDTOS;
        }
        return Lists.newArrayList();
    }

    /**
     * @Description: 查询员工以及部门信息
     * @methodName: getEmployeeInfoByCompanyId
     * @Param: [companyId, pageIndex, pageSize]
     * @return: java.util.List<com.fenbeitong.usercenter.api.model.dto.employee.EmployeeOrgUnitDTO>
     * @Author: zhangga
     * @Date: 2021/2/7 5:36 下午
     **/
    public List<EmployeeOrgUnitDTO> getEmployeeInfoByCompanyId(String companyId, Integer pageIndex, Integer pageSize) {
        List<EmployeeOrgUnitDTO> employeeOrgUnitDTOS = baseEmployeeExtService.queryEmployeeListByDeptId(1, companyId, companyId, pageSize, pageIndex);
        if (ObjUtils.isNotEmpty(employeeOrgUnitDTOS)) {
            return employeeOrgUnitDTOS;
        }
        return Lists.newArrayList();
    }

    /**
     * @Description: 不超过一百条
     * @methodName: getCurrentComEmployeeMap
     * @Param: [companyId, employeeIdList]
     * @return: Map<String, List < EmployeeBaseInfo>>
     * @Author: zhangga
     * @Date: 2021/9/18 9:32 下午
     **/
    public Map<String, EmployeeBaseInfo> getCurrentComEmployeeMap(String companyId, List<String> employeeIdList) {
        if (ObjUtils.isEmpty(employeeIdList)) {
            return new HashMap<>();
        }
        try {
            List<EmployeeBaseInfo> employeeBaseInfoList = irEmployeeService.queryEmployeeListByIdList(companyId, employeeIdList);
            if (CollectionUtils.isEmpty(employeeBaseInfoList)) {
                return new HashMap<>();
            }
            return employeeBaseInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(EmployeeBaseInfo::getId, employeeBaseInfo -> employeeBaseInfo));
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeIdList, e);
        }
        return new HashMap<>();
    }

    public EmployeeBaseInfo getEmployeeBaseInfo(String companyId, String employeeId) {
        try {
            List<EmployeeBaseInfo> employeeBaseInfoList = irEmployeeService.queryEmployeeListByIdList(companyId, Lists.newArrayList(employeeId));
            if (ObjUtils.isEmpty(employeeBaseInfoList)) {
                return null;
            }
            return employeeBaseInfoList.get(0);
        } catch (Exception e) {
            FinhubLogger.error("【UC查询】查询员工信息异常，员工ID集合：{}", employeeId, e);
            return null;
        }
    }

    /**
     * 集团版功能
     * 根据employeeId查询 集团下所有公司
     * <AUTHOR>
     * @date 2022-07-04 14:20:45
     */
    public List<CompanyInfoVO> getCompanyInfoByEmployeeId(String employeeId) {
        try {
            FinhubLogger.info("查询集团下所有公司employeeId:{}", employeeId);
            List<Company> res = iRCompanyService.listGroupRelCompanyByEmployeeId(employeeId);
            FinhubLogger.info("查询集团下所有公司返回结果employeeId:{}, res:{}", employeeId, JSON.toJSONString(res));
            return buildRes(res);
        } catch (Exception e) {
            FinhubLogger.error("查询集团下所有公司异常employeeId:{}", employeeId, e);
            throw new FinPayException(GlobalResponseCode.GROUP_COMPANY_LIST_ERROR);
        } catch (Throwable e) {
            FinhubLogger.error("查询集团下所有公司error employeeId:{}", employeeId, e);
            throw new FinPayException(GlobalResponseCode.GROUP_COMPANY_LIST_ERROR);
        }
    }

    /**
     * 集团版功能
     * 有账户权限的公司
     * @param groupAcctReqDTO
     * @return
     */
    public List<CompanyAccountInfo> getCompanyAccountWithAuth(GroupAcctReqDTO groupAcctReqDTO) {
    	List<CompanyAccountInfo> list = getCompanyAccountAuth(groupAcctReqDTO);
    	return Optional.ofNullable(list).orElse(Collections.emptyList())
    			.stream()
    			.filter(ca -> CompanyAccountInfo.AcctAuthTypeEnum.NO_AUTH != ca.getAcctAuthTypeEnum())
    			.collect(Collectors.toList());
    }

    /**
     * 集团版功能
     * 公司下有权限的账户
     * <AUTHOR>
     * @date 2022-07-04 14:20:45
     */
    public List<CompanyAccountInfo> getCompanyAccountAuth(GroupAcctReqDTO groupAcctReqDTO) {
        try {
            String groupId = groupAcctReqDTO.getGroupId();
            String companyId = groupAcctReqDTO.getCompanyId();
            String menuCode = groupAcctReqDTO.getMenuCode();
            String employeeId = groupAcctReqDTO.getEmployeeId();

            List<CompanyAccountInfo> result = new ArrayList<>();
            FinhubLogger.info("公司下有权限的公司groupAcctReqDTO:{}", JSON.toJSONString(groupAcctReqDTO));
            List<GroupCompany> companies = iRCompanyService.listGroupCompanyInfo(groupId);
            FinhubLogger.info("公司下有权限的公司返回结果groupAcctReqDTO:{}, res:{}", JSON.toJSONString(groupAcctReqDTO), JSON.toJSONString(companies));

            GroupDataPrivilegeReqDTO req = new GroupDataPrivilegeReqDTO(groupId, employeeId, companyId, menuCode);
            FinhubLogger.info("公司下有权限的账户groupAcctReqDTO{}", JSON.toJSONString(req));
            List<GroupDataPrivilegeResDTO> privilegeRes = iGroupPrivilegeService.listEmployeeMatchMenuDataPrivilege(req);
            FinhubLogger.info("公司下有权限的账户返回结果req:{}, privilegeRes:{}", JSON.toJSONString(req), JSON.toJSONString(privilegeRes));

            // 查询所有有权限账户
            if (CollectionUtils.isNotEmpty(privilegeRes) && CollectionUtils.isNotEmpty(companies)) {
                // companyMap记录所有公司，公司权限处理完毕后移除，未移除公司为无权限公司
                Map<String, GroupCompany> companyMap = companies.stream().collect(Collectors.toMap(GroupCompany::getId, s1 -> s1, (s1, s2) -> s1));

                // 根据平台权限数据，构建权限模型
                GroupDataPrivilegeResDTO privilegeResDTO = privilegeRes.stream().findFirst().get();
                List<AuthPermissionResCompanyDTO> authObjResDTOs = privilegeResDTO.getAuthObjResDTOs();
                if (CollectionUtils.isNotEmpty(authObjResDTOs)) {
                    for (AuthPermissionResCompanyDTO authObjResDTO : authObjResDTOs) {
                        buildCompanyAccountInfo(companyMap, companyMap.get(authObjResDTO.getCompanyId()), authObjResDTO, privilegeResDTO.getAuthObjType(), result);
                    }
                }

                // 5、无权限公司账户记录
                for (Map.Entry<String, GroupCompany> companyEntry : companyMap.entrySet()) {
                    GroupCompany com = companyEntry.getValue();
                    result.add(new CompanyAccountInfo(com.getId(), com.getName(), com.getCompanyType(), CompanyAccountInfo.AcctAuthEnum.NO_AUTH, CompanyAccountInfo.AcctAuthTypeEnum.NO_AUTH));
                }
            }
            FinhubLogger.info("公司下有权限的账户返回结果companyId:{}, employeeId:{}, result:{}", companyId, employeeId, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            FinhubLogger.error("公司下有权限的账户异常groupAcctReqDTO:{}", groupAcctReqDTO, e);
            throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_LIST_ERROR);
        } catch (Throwable e) {
            FinhubLogger.error("公司下有权限的账户error employeeId:{}", groupAcctReqDTO, e);
            throw new FinPayException(GlobalResponseCode.GROUP_ACCOUNT_LIST_ERROR);
        }
    }

    /**
     * 构建返回参数
     * <AUTHOR>
     * @date 2022-07-04 14:33:14
     */
    private List<CompanyInfoVO> buildRes(List<Company> companies) {
        List<CompanyInfoVO> res = new ArrayList<>();
        if (companies != null) {
            for (Company company : companies) {
                CompanyInfoVO companyInfoVO = new CompanyInfoVO();
                companyInfoVO.setId(company.getId());
                companyInfoVO.setName(company.getName());
                res.add(companyInfoVO);
            }
        }
        return res;
    }

    /**
     * 构建权限数据
     * <AUTHOR>
     * @date 2022-07-16 11:33:56
     */
    private void buildCompanyAccountInfo(Map<String, GroupCompany> companyMap, GroupCompany company, AuthPermissionResCompanyDTO authRes, List<String> authObjType, List<CompanyAccountInfo> result) {
        GroupAuthPermissionResDTO companyAuth = authRes.getCompany();
        GroupAuthPermissionResDTO account = authRes.getAccount();

        // 有公司权限维度标识
        if (companyAuth != null && BooleanUtils.isTrue(companyAuth.getAllData()) && authObjType.contains(CompanyAccountInfo.AcctAuthTypeEnum.AUTH_COMPANY.getKey())) {
            companyMap.remove(company.getId());
            result.add(new CompanyAccountInfo(company.getId(), company.getName(), company.getCompanyType(), CompanyAccountInfo.AcctAuthEnum.NO_AUTH, CompanyAccountInfo.AcctAuthTypeEnum.AUTH_COMPANY));
            return;
        }

        // 有账户维度标识
        if (account != null && authObjType.contains(CompanyAccountInfo.AcctAuthTypeEnum.AUTH_ACCOUNT.getKey())) {
            // 3、有所在公司所有权限
            if (BooleanUtils.isTrue(account.getAllData())) {
                companyMap.remove(company.getId());
                result.add(new CompanyAccountInfo(company.getId(), company.getName(), company.getCompanyType(), CompanyAccountInfo.AcctAuthEnum.COMPANY_ALL_DATA, CompanyAccountInfo.AcctAuthTypeEnum.AUTH_ACCOUNT));
            } else {
                // 4、有部分账户权限校限
                List<String> dataList = account.getPartDataIdList();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    companyMap.remove(company.getId());
                    // 境内账户
                    List<AccountInfoVO> infos = dataList.stream().map(this :: parseToAccountInfo).filter(v -> !CoreConstant.FX_UC_ACCOUNT_MODEL.equals(v.getAccountModel())).collect(Collectors.toList());
                    List<BaseAccountVO> permissionAccounts = dataList.stream().map(this::parseToAccountInfo).filter(v -> !CoreConstant.FX_UC_ACCOUNT_MODEL.equals(v.getAccountModel())).collect(Collectors.toList());

                    // 海外卡账户
                    List<FxCompanyAcctInfo> fxInfos = dataList.stream().map(this :: parseToFxAccountInfo).filter(v -> CoreConstant.FX_UC_ACCOUNT_MODEL.equals(v.getAccountModel())).collect(Collectors.toList());
                    List<BaseAccountVO> fxPermissionAccounts = dataList.stream().map(this::parseToAccountInfo).filter(v -> CoreConstant.FX_UC_ACCOUNT_MODEL.equals(v.getAccountModel())).collect(Collectors.toList());

                    result.add(new CompanyAccountInfo(company.getId(), company.getName(), company.getCompanyType(), CompanyAccountInfo.AcctAuthEnum.COMPANY_PART_DATA, CompanyAccountInfo.AcctAuthTypeEnum.AUTH_ACCOUNT, infos, permissionAccounts).appendFxAccounts(fxInfos).appendFxPermissionAccounts(fxPermissionAccounts));
                }
            }
        }
    }

    /**
     * 平台数据转AccountInfoVO 实体类
     * <AUTHOR>
     * @date 2022-07-16 12:16:52
     */
    private AccountInfoVO parseToAccountInfo(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        JSONObject obj = JSON.parseObject(str);
        AccountInfoVO info = new AccountInfoVO();
        info.setAccountId(obj.getString("id"));
        info.setAccountModel(obj.getInteger("type"));
        info.setCompanyId(obj.getString("cid"));
        return info;
    }

    private FxCompanyAcctInfo parseToFxAccountInfo(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        JSONObject obj = JSON.parseObject(str);
        FxCompanyAcctInfo info = new FxCompanyAcctInfo();
        info.setAccountId(obj.getString("id"));
        info.setAccountModel(obj.getInteger("type"));
        info.setCompanyId(obj.getString("cid"));
        return info;
    }



    /**
     * 离职员工初始化值
     * @param employeeInfo
     */
    public void buildEmployeeInfo(EmployeeOrgUnitDTO employeeInfo) {
        //员工离职的情况默认值
        if(ObjectUtil.isNotNull(employeeInfo) && EmployeeStatus.SOFT_DELETE.getKey() == employeeInfo.getStatus()){
            String employeeName = employeeInfo.getEmployeeName();
            if (ObjUtils.isEmpty(employeeName)){
                employeeInfo.setEmployeeName(EMPLOYEE_LEAVES);
            }
            if (ObjUtils.isNotEmpty(employeeName) && !employeeName.contains(EMPLOYEE_LEAVES)) {
                employeeName = employeeName + EMPLOYEE_LEAVES;
                employeeInfo.setEmployeeName(employeeName);
            }
        }
    }


    /**
     * 员工对象转换
     * @param employeeOrgUnitDTO
     * @return
     */
    public EmployeeContract covertEmployeeInfo(EmployeeOrgUnitDTO employeeOrgUnitDTO) {
        buildEmployeeInfo(employeeOrgUnitDTO);
        EmployeeContract employeeContract = new EmployeeContract();
        employeeContract.setId(employeeOrgUnitDTO.getEmployeeId());
        employeeContract.setEmployee_id(employeeOrgUnitDTO.getEmployeeId());
        employeeContract.setCompany_id(employeeOrgUnitDTO.getCompanyId());
        employeeContract.setCompany_name(employeeOrgUnitDTO.getCompanyName());
        employeeContract.setPhone_num(employeeOrgUnitDTO.getPhoneNum());
        employeeContract.setName(employeeOrgUnitDTO.getEmployeeName());
        employeeContract.setOrg_name(employeeOrgUnitDTO.getOrgUnitName());
        employeeContract.setStatus(employeeOrgUnitDTO.getStatus());
        return employeeContract;
    }
}
