package com.fenbeitong.fenbeipay.core.model.dto;

import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* @Date: 2021/1/26 10:17 上午
* @Description 分贝券统计dto
**/
@Data
public class VouchersStatisticsReqDTO extends PageBean {

    /**
    * @Date: 2021/1/26 10:22 上午
    * @Description 公司id
    **/
    private String companyId;
    /**
    * @Date: 2021/1/26 10:19 上午
    * @Description 员工id
    **/
    private String employeeId;

    /**
    * @Date: 2021/1/26 10:20 上午
    * @Description 部门id
    **/
    private String employeeDepartmentId;

    /**
     * 部门名称
     */
    private String employeeDepartmentName;

    /**
    * @Date: 2021/1/26 10:20 上午
    * @Description 筛选开始时间
    **/
    @DateTimeFormat
    private String startTime;

    /**
    * @Date: 2021/1/26 10:20 上午
    * @Description 筛选结束时间
    **/
    @DateTimeFormat
    private String endTime;
}
