package com.fenbeitong.fenbeipay.core.service.config;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.config.PayCommonConfigMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.config.PayCommonConfig;

/** 
 * <AUTHOR> 
 * @Description: 
 * 
 * @date 2023-03-13 03:55:29 
*/
@Service
public class PayCommonConfigService {

	@Autowired
	PayCommonConfigMapper payCommonConfigMapper;
	
	public boolean upsertConfig(PayCommonConfig config) {
		int result = payCommonConfigMapper.insert(config);
		return result > 0;
	}
	
	public PayCommonConfig queryByKey(String key) {
		return payCommonConfigMapper.selectByConfigKey(key);
	}
	
	public boolean updateConfigStatus(String key, Integer status) {
		if (Objects.isNull(status) || StringUtils.isBlank(key) || (!Objects.equals(status, 0) && !Objects.equals(status, 1))) {
			return false;
		}
		PayCommonConfig config = PayCommonConfig.builder().configKey(key).status(status).build();
		return upsertConfig(config);
	}
}
