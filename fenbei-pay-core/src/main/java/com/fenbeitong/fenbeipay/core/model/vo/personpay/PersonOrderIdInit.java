package com.fenbeitong.fenbeipay.core.model.vo.personpay;

/**
 * Created by mac on 17/12/29.
 */
public class PersonOrderIdInit {
    /**
     * 生成订单号
     *
     * @param pre
     * @return
     */
    public static String getOrderId(String pre) {
        StringBuffer orderNo = new StringBuffer(pre); //前缀
        orderNo.append((System.currentTimeMillis() + "").substring(1)); //去掉一位的毫秒
        orderNo.append((System.nanoTime() + "").substring(7, 10));//纳米7-9位
        orderNo.append((int) (Math.random() * 900 + 100));//随机位数(100-999)
        return orderNo.toString();
    }

}
