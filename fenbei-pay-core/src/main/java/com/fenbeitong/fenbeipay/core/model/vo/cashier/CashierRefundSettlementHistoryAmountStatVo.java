package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.finhub.common.constant.PayModelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 历史退款各种金额汇总
 */

@Data
@NoArgsConstructor
public class CashierRefundSettlementHistoryAmountStatVo {

    private String cashierTxnId;

    private String fbOrderId;

    private String refundOrderId;

    /**
     * 企业支付|个人垫付
     * @see PayModelEnum
     */
    private Integer orderPaymentModel;

    private BigDecimal refundAmount = BigDecimal.ZERO;

    private BigDecimal refundAmountPublic = BigDecimal.ZERO;
    private BigDecimal refundAmountCompany = BigDecimal.ZERO;
    private BigDecimal refundAmountRedcoupon = BigDecimal.ZERO;

    private BigDecimal refundAmountPersonal = BigDecimal.ZERO;

    private BigDecimal refundAmountFbb = BigDecimal.ZERO;

    private BigDecimal refundAmountVoucher = BigDecimal.ZERO;
    private BigDecimal refundAmountVoucherIndividual = BigDecimal.ZERO;
    private BigDecimal refundAmountVoucherRedcoupon = BigDecimal.ZERO;

    private BigDecimal refundAmountThird = BigDecimal.ZERO;

    private BigDecimal refundAmountBankPersonal = BigDecimal.ZERO;

    /**
     *   报销金额
     */
    private BigDecimal refundAmountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal refundAmountReimburseSelf = BigDecimal.ZERO;
    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;

    /**
     * 子账户id
     */
    private String accountSubId;

    public CashierRefundSettlementHistoryAmountStatVo(String cashierTxnId,String fbOrderId) {
        this.fbOrderId = fbOrderId;
        this.cashierTxnId = cashierTxnId;
        this.refundAmount = BigDecimal.ZERO;
        this.refundAmountPublic = BigDecimal.ZERO;
        this.refundAmountPersonal = BigDecimal.ZERO;
        this.refundAmountFbb = BigDecimal.ZERO;
        this.refundAmountVoucher = BigDecimal.ZERO;
        this.refundAmountThird = BigDecimal.ZERO;
        this.refundAmountCompany = BigDecimal.ZERO;
        this.refundAmountRedcoupon = BigDecimal.ZERO;
        this.refundAmountVoucherIndividual =BigDecimal.ZERO;
        this.refundAmountVoucherRedcoupon =BigDecimal.ZERO;
        this.refundAmountReimburseCompany =BigDecimal.ZERO;
        this.refundAmountReimburseSelf =BigDecimal.ZERO;
        this.refundAmountBankPersonal = BigDecimal.ZERO;
    }

}
