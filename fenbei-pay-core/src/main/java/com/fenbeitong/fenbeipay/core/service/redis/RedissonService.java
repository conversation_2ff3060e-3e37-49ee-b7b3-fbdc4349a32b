package com.fenbeitong.fenbeipay.core.service.redis;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedissonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 分布式锁
 */
@Service
public class RedissonService {


    @Autowired
    private RedissonDao redissonDao;

    /**
     *  根据key值获取锁
     * @param lockName
     */
    public void lock(String lockName){
        redissonDao.lock(lockName);
    }

    /**
     * 获取锁
     * @param lockName
     * @return
     * @throws InterruptedException
     */
    public boolean tryLock(String lockName) throws InterruptedException {
        return redissonDao.tryLock(lockName);
    }

    /**
     * 获取锁 如果获取失败等待一些时间后，再进行，否者失败
     * @param lockName
     * @return
     * @throws InterruptedException
     */
    public boolean tryLock(long waitTime,long lockTime,TimeUnit timeUnit,String lockName) throws InterruptedException {
        return redissonDao.tryLock(waitTime,lockTime, timeUnit,lockName);
    }

    /**
     * 解锁
     * @param lockName
     */
    public void unLock(String lockName){
        redissonDao.unLock(lockName);
    }

    /**
     * 获取锁，一直等待到取到锁后返回
     * @param lockName
     * @throws InterruptedException
     */
    public boolean getUntilHaveLock(String lockName) throws InterruptedException {
        return redissonDao.getUntilHaveLock(lockName);
    }



}
