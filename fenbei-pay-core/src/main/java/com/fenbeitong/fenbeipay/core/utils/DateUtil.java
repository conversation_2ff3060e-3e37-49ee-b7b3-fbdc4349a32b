package com.fenbeitong.fenbeipay.core.utils;

import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTermTypeEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.WriteInvoiceType;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyFbqRuleDTO;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;
import java.util.SimpleTimeZone;

/**
 * Created by xiaoxiong on 2017/9/26.
 */
public class DateUtil {
    public final static String FORMAT_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    //其他年份的时间格式
    public static final String OTHER_YEAR_DATE_FORMAT = "yyyy/MM/dd HH:mm";
    //当前年份时间格式
    public static final String CUR_YEAR_DATE_FORMAT = "MM/dd HH:mm";
    public static final String YYYY_MM_DD = "yyyy/MM/dd";
    public static final String EMPTY = "";


    /**
     * 格式：
     * 今年以内：mm/dd hh:mm，如：03/18 14:01
     * 非今年内：yyyy/mm/dd hh:mm，如：2021/12/25 14:01
     * 注意：
     * 使用 24 小时制；
     * 日期和时间之间包含一个空格；
     * 特殊：【费控报销】发票开票日期：yyyy/mm/dd
     *
     * @param dateTime 时间，可以是数据创建时间，更新时间
     * @return 根据时间返回前端需要展示的类型
     **/
    public static String getShowByDateTime(Date dateTime) {

        if (dateTime == null) {
            return EMPTY;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(dateTime);
        int dateYear = c.get(Calendar.YEAR);//传入时间的年份
        int curYear = LocalDate.now().getYear();//当前年份
        SimpleDateFormat format = null;
        //比较传入的日期是否是当前年份，使用不同的模版
        if (dateYear == curYear) {
            format = new SimpleDateFormat(CUR_YEAR_DATE_FORMAT);
        } else {
            format = new SimpleDateFormat(OTHER_YEAR_DATE_FORMAT);
        }
        return format.format(dateTime);
    }

    /**
     * 获得本月第一天0点时间
     */

    public static Date getTimesMonthStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static Date getTimesNextMonthStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static Date getTimesDayStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), cal.get(Calendar.HOUR_OF_DAY), 0, 0);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        return cal.getTime();
    }

    public static Date getTimesNextDayStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), cal.get(Calendar.HOUR_OF_DAY), 0, 0);
        cal.add(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        return cal.getTime();
    }

    /**
     * 获取格式化字符串
     *
     * @param formateStr yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static String getFormateDateString(String formateStr, Date date) {
        if (StringUtils.isEmpty(date)) {
            return "";
        }
        return new SimpleDateFormat(formateStr).format(date);
    }

    /**
     * string字符串转date
     *
     * @param dateStr
     * @param formate
     * @return
     */
    public static Date getDateFromString(String dateStr, String formate) {
        SimpleDateFormat sdf = new SimpleDateFormat(formate);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * string字符串转date
     *
     * @param dateStr
     * @return
     */
    public static Date getDateFromStringByDefaultDateFormat(String dateStr) {
        if (null == dateStr) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_DATE_TIME_PATTERN);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 修改日期
     *
     * @param date
     * @param amount
     * @return
     */
    public static Date changeDateDay(Date date, int amount) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.DATE, amount);
        date = c.getTime();
        return date;
    }

    /**
     * 修改日期
     *
     * @param date
     * @param day  天 hour 小时 minute 分钟
     * @return 增加后的时间
     */
    public static Date changeDateDayHourMinute(Date date, int day, int hour, int minute) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.DATE, day);
        c.add(Calendar.HOUR, hour);
        c.add(Calendar.MINUTE, minute);
        date = c.getTime();
        return date;
    }

    public static Date changeDateMonth(Date date, int amount) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.MONTH, amount);
        date = c.getTime();
        return date;
    }

    public static Date changeDateYear(Date date, int amount) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.YEAR, amount);
        date = c.getTime();
        return date;
    }

    public static Date changeDateHour(Date date, int amount) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.HOUR, amount);
        date = c.getTime();
        return date;
    }

    public static Date changeDateSecond(Date date, int amount) {
        if (null == date) {
            return date;
        }
        Calendar c = Calendar.getInstance();
        //设置当前日期
        c.setTime(date);
        c.add(Calendar.SECOND, amount);
        date = c.getTime();
        return date;
    }

    public static long calculationHours(Date createDate) {
        //当前时间处理
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());


        //给定时间处理
        Calendar setCal = Calendar.getInstance();
        setCal.setTime(createDate);
        long dayDiff = (cal.getTimeInMillis() - setCal.getTimeInMillis()) / (1000 * 60 * 60);
        return dayDiff;
    }

    /***
     * 支付判断的失效时间
     * @param endDate
     * @return 距离终结时间相差多少分
     */
    public static long changeDateMin(Date endDate) {
        if (Objects.isNull(endDate)) {
            throw new RuntimeException("结束数据不可以为空！");
        }
        //当前时间处理
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        //给定时间处理
        Calendar setCal = Calendar.getInstance();
        setCal.setTime(endDate);
        long diffMin = (setCal.getTimeInMillis() - cal.getTimeInMillis()) / (1000 * 60);
        return diffMin;
    }

    public static Date dateToISODate(Date date) {
        //T代表后面跟着时间，Z代表UTC统一时间
        SimpleDateFormat format =
                new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        format.setCalendar(new GregorianCalendar(new SimpleTimeZone(0, "GMT")));
        String isoDate = format.format(date);
        try {
            return format.parse(isoDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date setDate(Date date, Integer year, Integer month, Integer day, Integer hour, Integer minute, Integer second, Integer millisecond) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        if (year != null) {
            c.set(Calendar.YEAR, year);
        }
        if (month != null) {
            c.set(Calendar.MONTH, month - 1);
        }
        if (day != null) {
            c.set(Calendar.DATE, day);
        }
        if (hour != null) {
            c.set(Calendar.HOUR_OF_DAY, hour);
        }
        if (minute != null) {
            c.set(Calendar.MINUTE, minute);
        }
        if (second != null) {
            c.set(Calendar.SECOND, second);
        }
        if (millisecond != null) {
            c.set(Calendar.MILLISECOND, millisecond);
        }
        Date time = c.getTime();
        return time;
    }

    public static int getHourOfDate(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.HOUR_OF_DAY);
    }

    public static boolean checkIsToday(Date date) {
        if (date == null) {
            return false;
        }
        String dateStr = getFormateDateString("yyyy-MM-dd", date);
        String currentDateStr = getFormateDateString("yyyy-MM-dd", new Date());
        if (currentDateStr.equals(dateStr)) {
            return true;
        }
        return false;
    }

    public static Date timeOffset(Date date, Date startDate, Date endDate) {
        if (null == date || null == startDate || null == endDate) {
            return null;
        }
        long timeDifference = endDate.getTime() - startDate.getTime();
        return new Date(date.getTime() + timeDifference);
    }

    public static int daysBetweenDay(Date startDate, Date endDate) {
        long time1 = startDate.getTime();
        long time2 = endDate.getTime();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    public static void checkTime(Integer voucherExpiryNotice, Integer voucherTermType, String voucherTermValidity, Integer writeInvoiceType,
                                 CompanyFbqRuleDTO companyFbqRuleDTO) {
        //校验过期提醒时间
        voucherExpiryNotice = Objects.isNull(voucherExpiryNotice) ? 3 : voucherExpiryNotice;
        if (voucherExpiryNotice > 60 || voucherExpiryNotice < 1) {
            throw new FinPayException(GlobalResponseCode.VOUCHER_EXPIRY_NOTICE_ERROR);
        }
        int checkDay = 0;
        GlobalResponseCode globalResponseCode = GlobalResponseCode.VOUCHER_AFTER_INVOICE_MAX_DAYS;
        String msg = null;
        if (Objects.equals(writeInvoiceType, WriteInvoiceType.INVOICE_ADVANCE.getValue())) {
            checkDay = companyFbqRuleDTO.getBeforeInvoiceMaxValidityDay();
            msg = MessageFormat.format(globalResponseCode.getMsg(), companyFbqRuleDTO.getBeforeInvoiceMaxValidityDay());
        } else {
            checkDay = companyFbqRuleDTO.getAfterInvoiceMaxValidityDay();
            msg = MessageFormat.format(globalResponseCode.getMsg(), companyFbqRuleDTO.getAfterInvoiceMaxValidityDay());
        }
        globalResponseCode.setMsg(msg);

        if (VoucherTermTypeEnum.TERM_OF_DAYS.getValue() == voucherTermType) {
            int day = Integer.parseInt(voucherTermValidity);
            if (day < 0 || day > checkDay) {
                throw new FinPayException(globalResponseCode);
            }
        }
        if (VoucherTermTypeEnum.TERM_OF_DAY_HOUR_MINUTE.getValue() == voucherTermType) {
            JSONObject jsb = JSON.parseObject(voucherTermValidity);
            Integer day = jsb.getInteger("day");
            Integer hour = jsb.getInteger("hour");
            Integer minute = jsb.getInteger("minute");
            if (day < 0 || hour < 0 || hour > 23 || minute < 0 || minute > 59) {
                throw new FinPayException(GlobalResponseCode.VOUCHER_TIME_SETTING_ERROR);
            }
            int limitMinutes = checkDay * 24 * 60;
            int setMinutes = day * 24 * 60 + hour * 60 + minute;
            if (setMinutes > limitMinutes) {
                throw new FinPayException(globalResponseCode);
            }
        }
    }

    public static String fbtDateFormat(Date date) {
        String result = cn.hutool.core.date.DateUtil.format(date, "yyyy/MM/dd HH:mm:ss");
        if (ObjectUtil.isNotNull(result)) {
            return result;
        }
        return "";
    }

    public static String fbtDateFormatSimple(Date date) {
        String result = cn.hutool.core.date.DateUtil.format(date, YYYY_MM_DD);
        if (ObjectUtil.isNotNull(result)) {
            return result;
        }
        return "";
    }

    /**
     * 修改日期
     *
     * @param date
     * @return
     */
    public static int getMonthLeftDay(Date date) {
        //获取当前时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int today = cal.get(Calendar.DAY_OF_MONTH);
        //得到一个月最最后一天日期
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        return lastDay - today + 1;
    }

    public static Date convertDate(String dateString){
        //2021-01-01 00:00:00
        DateFormat minDateOfQuery = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date minDate = null;
        try {
            minDate = minDateOfQuery.parse(dateString);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return minDate;
    }

    /**
     * 计算两个日期之间的总月数（完整月份）
     */
    public static int getIntervalMonths(Date startDate, Date endDate) {
        return (int) ChronoUnit.MONTHS.between(toLocalDate(startDate), toLocalDate(endDate));
    }

    /**
     * 计算两个日期之间的总周数（完整周数）
     */
    public static int getIntervalWeeks(Date startDate, Date endDate) {
        return (int) ChronoUnit.WEEKS.between(toLocalDate(startDate), toLocalDate(endDate));
    }

    /**
     * 计算两个日期之间的总天数
     */
    public static int getIntervalDays(Date startDate, Date endDate) {
        return (int) ChronoUnit.DAYS.between(toLocalDate(startDate), toLocalDate(endDate));
    }
    /**
     * 将 Date 转换为 LocalDate
     */
    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static void main(String[] args) {
        Date now = DateUtil.convertDate("2025-01-25 00:02:00");
        Date grantDate = DateUtil.convertDate("2024-12-25 23:02:00");
        long month = DateUtil.getIntervalMonths(grantDate,now);
        System.out.println(month);
        long totalWeeks = DateUtil.getIntervalWeeks(grantDate,now);
        System.out.println(totalWeeks);
    }
}
