package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;

/**
 * Created by mac on 17/12/21.
 */
@Deprecated
public class FuQianLaPayCommonReqContract implements Serializable {

    private String clientIp;
    private String device;
    private Boolean liveMode;
    private String sdkMark;
    private String version;
    private String personOrderNo;
    private String orderType;
    private String client_type;
    private String client_ip;
    private String client_version;

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getClientIp() {
        return clientIp == null ? getClient_ip() : clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getDevice() {
        return device == null ? getClient_type() : device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public Boolean getLiveMode() {
        return liveMode;
    }

    public void setLiveMode(Boolean liveMode) {
        this.liveMode = liveMode;
    }

    public String getSdkMark() {
        return sdkMark;
    }

    public void setSdkMark(String sdkMark) {
        this.sdkMark = sdkMark;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPersonOrderNo() {
        return personOrderNo;
    }

    public void setPersonOrderNo(String personOrderNo) {
        this.personOrderNo = personOrderNo;
    }

    public String getClient_type() {
        return client_type;
    }

    public void setClient_type(String client_type) {
        this.client_type = client_type;
    }

    public String getClient_ip() {
        return client_ip;
    }

    public void setClient_ip(String client_ip) {
        this.client_ip = client_ip;
    }

    public String getClient_version() {
        return client_version;
    }

    public void setClient_version(String client_version) {
        this.client_version = client_version;
    }
}
