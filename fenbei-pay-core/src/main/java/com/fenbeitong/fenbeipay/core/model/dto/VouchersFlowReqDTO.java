package com.fenbeitong.fenbeipay.core.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: VouchersFlowRequestDTO
 * @Author: zhangga
 * @CreateDate: 2019/4/23 12:06 PM
 * @UpdateUser:
 * @UpdateDate: 2019/4/23 12:06 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class VouchersFlowReqDTO {

    /**
     * 公司ID
     */
    private String companyId;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     **/
    private Date endTime;
    /**
     * 操作用户姓名
     */
    private String operationUserName;
    /**
     * 操作用户电话
     */
    private String operationUserPhone;
    /**
     * 操作类型:1-消费；2-退款
     */
    private Integer type;
    /**
     * 开票类型：0-后开票；1-先开票
     */
    private Integer writeInvoiceType;

    /**
     * 开票状态：0-未申请开票；1-开票中；2-已开票
     */
    private Integer writeInvoiceStatus;
    /**
     * 消费场景
     */
    private Integer businessType;

    /**
     * 扣款账户类型：2-商务账户；3-个人账户；4-企业账户；6-红包券账户
     */
    private Integer deductionAccountType;

    /**
     * 分贝券流水编号
     */
    private String voucherFlowId;

    /**
     * 账户流水ID
     */
    private String accountSubFlowId;
}