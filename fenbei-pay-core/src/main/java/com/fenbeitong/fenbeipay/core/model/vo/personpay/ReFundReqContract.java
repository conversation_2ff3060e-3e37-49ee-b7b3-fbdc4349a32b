package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.math.BigDecimal;

/**
 * 退款接受参数
 * Created by mac on 18/1/10.
 */
public class ReFundReqContract {

    private String orderNo;
    private BigDecimal orderAmount;
    private String channel;
    private String notifyUrl;

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
