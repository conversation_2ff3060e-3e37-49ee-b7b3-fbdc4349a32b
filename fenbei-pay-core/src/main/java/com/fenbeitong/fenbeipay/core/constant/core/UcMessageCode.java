package com.fenbeitong.fenbeipay.core.constant.core;

import com.fenbeitong.finhub.common.constant.FinhubMessageCode;

/**
 * User: zhuminghua
 * Date: 2017/5/8 下午1:49
 * Desc:
 */
public interface UcMessageCode extends FinhubMessageCode {

    /**
     * 用户不存在
     */
    int SYS_USER_NOT_EXIST = 10001;
    /**
     * 用户被禁用
     */
    int SYS_USER_DISABLE = 10002;
    /**
     * 用户名或密码不匹配
     */
    int SYS_USER_PASSWORD_INVALID = 10003;
    /**
     * token创建失败
     */
    int SYS_USER_TOKEN_CREATE_ERROR = 10004;
    /**
     * token无效
     */
    int SYS_USER_TOKEN_INVALID = 10005;

    /**
     * 外部接口调用错误
     */
    int INTERFACE_EXCEPTION = 999999;

    /**
     *  excel类型不正确
     */
    int EXCEL_CODE_ERROR=21001;
    /**
     * excle表格列不正确
     */
    int EXCEL_COLUM_ERROR=21002;
    /**
     * 文件内容格式不正确
     */
    int EXCEL_CONTENT_ERROR=21003;

    /**
     *公司参数错误
     */
    int COMPANY_PARAM_ERROR=22001;
}
