package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收银创建支付流水请求VO
 * REFUND_MAX(3, "指定每种支付能力最大可退金额，进行退款"),
 * 以上退款方式都会用到次VO
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CashierRefundTradeMaxWayReqVo extends CashierRefundTradeBaseReqVo {

    /**
     * 要求因公-企业退款金额
     * 兼容老接口
     */
    @Deprecated
    @Min(0)
    private BigDecimal companyRefundAmount;

    /**
     * 要求三方退款金额
     * (必填)个人支付3方退款最大总金额，单位为分（支付宝，微信支付）
     */
    @Min(0)
    private BigDecimal personalThirdRefundMaxPrice;
    /**
     * 要求分贝币退款金额
     * (必填)分贝币退款最大总金额，单位为分
     */
    @Min(0)
    private BigDecimal personFbbRefundMaxPrice;
    /**
     * 要求分贝券退款金额
     * (必填)分贝券退款最大总金额，单位为分
     */
    @Min(0)
    private BigDecimal personVouchersRefundMaxPrice;

    /**
     * 要求分贝券退款金额
     * (必填)个人账户分贝券 单位为分
     */
    @Min(0)
    private BigDecimal personVouchersIndividualRefundMaxPrice;

    /**
     * 要求分贝券退款金额
     * (必填)红包券账户分贝券 单位为分
     */
    @Min(0)
    private BigDecimal personVouchersRedcouponRefundMaxPrice;

    /**
     * 因公-企业最大退款金额
     */
    @Min(0)
    private BigDecimal companyRefundMaxAmount;

    /**
     * 因公-红包券最大退款金额
     */
    @Min(0)
    private BigDecimal redcouponRefundMaxAmount;

    /**
     *  报销金额
     */
    private BigDecimal reimburseCompanyRefundMaxAmount;

    /**
     *  自费金额
     */
    private BigDecimal reimburseSelfRefundMaxAmount;


    @Builder
    public CashierRefundTradeMaxWayReqVo(String fbOrderId, Integer checkStatus, Date createTime, String fbTradeId, String refundOrderId, String cashierTxnId, String bankTransNo, String bankAccountNo, String bankName, BigDecimal totalRefundAmount, BigDecimal personalRefundAmount, BigDecimal publicRefundAmount, Integer cashierPublicRefundWay, Integer cashierRefundWay, String bizCallbackUrl, String refundReason, String remark, Integer operationChannelType, String customerServiceId, String customerServiceName, BigDecimal companyRefundAmount, BigDecimal personalThirdRefundMaxPrice, BigDecimal personFbbRefundMaxPrice, BigDecimal personVouchersRefundMaxPrice, BigDecimal personVouchersIndividualRefundMaxPrice, BigDecimal personVouchersRedcouponRefundMaxPrice, BigDecimal companyRefundMaxAmount, BigDecimal redcouponRefundMaxAmount) {
        super(fbOrderId, checkStatus,createTime,fbTradeId, refundOrderId, cashierTxnId, bankTransNo, bankAccountNo, bankName, totalRefundAmount, personalRefundAmount, publicRefundAmount, cashierPublicRefundWay, cashierRefundWay, bizCallbackUrl, refundReason, remark, operationChannelType, customerServiceId, customerServiceName);
        this.companyRefundAmount = companyRefundAmount;
        this.personalThirdRefundMaxPrice = personalThirdRefundMaxPrice;
        this.personFbbRefundMaxPrice = personFbbRefundMaxPrice;
        this.personVouchersRefundMaxPrice = personVouchersRefundMaxPrice;
        this.personVouchersIndividualRefundMaxPrice = personVouchersIndividualRefundMaxPrice;
        this.personVouchersRedcouponRefundMaxPrice = personVouchersRedcouponRefundMaxPrice;
        this.companyRefundMaxAmount = companyRefundMaxAmount;
        this.redcouponRefundMaxAmount = redcouponRefundMaxAmount;
    }


    public CashierRefundTradeMaxWayReqVo(BigDecimal personalThirdRefundMaxPrice, BigDecimal personFbbRefundMaxPrice, BigDecimal personVouchersRefundMaxPrice) {
        this.personalThirdRefundMaxPrice = personalThirdRefundMaxPrice;
        this.personFbbRefundMaxPrice = personFbbRefundMaxPrice;
        this.personVouchersRefundMaxPrice = personVouchersRefundMaxPrice;
    }

    public boolean overPersonalThirdRefundMaxPrice(BigDecimal canMaxRefund) {
        return personalThirdRefundMaxPrice.compareTo(canMaxRefund) > 0;
    }
    public boolean overPersonalFbbRefundMaxPrice(BigDecimal canMaxRefund) {
        return personFbbRefundMaxPrice.compareTo(canMaxRefund) > 0;
    }
    public boolean overPersonalVouchersRefundMaxPrice(BigDecimal canMaxRefund) {
        return personVouchersRefundMaxPrice.compareTo(canMaxRefund) > 0;
    }

    public boolean hasPublicRefund() {
        BigDecimal publicRefund = getPublicRefundAmount();
        return BigDecimalUtils.hasPrice(publicRefund);
    }
    /**
     * 因公退款金额是否超过了canMaxRefund
     *
     * @param canMaxRefund
     * @return
     */
    public boolean overPublicRefund(BigDecimal canMaxRefund) {
        BigDecimal publicRefund = getPublicRefundAmount();
        return publicRefund.compareTo(canMaxRefund) > 0;
    }


    public boolean addNoEqlTotalRefund() {
        BigDecimal publicRefund = getPublicRefundAmount();
        BigDecimal actTotalRefund =  personalRefundAmount.add(publicRefund);
        return totalRefundAmount.compareTo(actTotalRefund) != 0;
    }

    @Override
    public BigDecimal getPublicRefundAmount() {
        //TODO companyRefundAmount去除
        if(BigDecimalUtils.hasPrice(companyRefundAmount)){
            return companyRefundAmount;
        }else if(BigDecimalUtils.hasPrice(publicRefundAmount)){
            return publicRefundAmount;
        }else{
            return BigDecimal.ZERO;
        }
    }

    public boolean checkFbqMaxWay() {
        if(BigDecimalUtils.hasNoPrice(personVouchersRefundMaxPrice)){
            return true;
        }
         return (BigDecimalUtils.hasPrice(personVouchersIndividualRefundMaxPrice)||BigDecimalUtils.hasPrice(personVouchersRedcouponRefundMaxPrice));

    }
}