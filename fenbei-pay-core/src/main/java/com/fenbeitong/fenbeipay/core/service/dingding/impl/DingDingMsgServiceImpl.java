package com.fenbeitong.fenbeipay.core.service.dingding.impl;

import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.common.utils.webhook.TextMsg;
import com.fenbeitong.finhub.common.utils.webhook.WebHookUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2021/9/14
 */
@Service
public class DingDingMsgServiceImpl implements DingDingMsgService {
    @Value("${exception.remind.profile}")
    protected String currentEnvironment;
    @Value("${ding.ding.cashier.token}")
    protected String dingToken;
    @Value("${ding.ding.cashier.project.manager}")
    protected String projectManager;
    @Override
    public void sendMsg(String sendMsg) {
        final String msg = "【fenbei-pay】【" + currentEnvironment + "】" + sendMsg;
        CompletableFuture.runAsync(() -> {
            try {
                TextMsg textMsg = new TextMsg(dingToken, msg);
                List<String> phoneNoList = Arrays.asList(projectManager.split(","));
                textMsg.at(phoneNoList);
                WebHookUtils.sendFeishu(textMsg);
            } catch (Exception e) {
                FinhubLogger.error("【发送钉钉消息异常】token：{}，msg：{}，env：{}，phoneNos：{}", dingToken, msg, currentEnvironment, projectManager, e);
            }
        });
    }

}
