package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;

/**
 * Created by mac on 17/12/20.
 */
public class SignInfoReqContract implements Serializable {

    private String signInfo;
    private String orderNo;
    private String personOrderNo;
    private String channel;
    private String client_version;

    public String getSignInfo() {
        return signInfo;
    }

    public void setSignInfo(String signInfo) {
        this.signInfo = signInfo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPersonOrderNo() {
        return personOrderNo;
    }

    public void setPersonOrderNo(String personOrderNo) {
        this.personOrderNo = personOrderNo;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getClient_version() {
        return client_version;
    }

    public void setClient_version(String client_version) {
        this.client_version = client_version;
    }
}
