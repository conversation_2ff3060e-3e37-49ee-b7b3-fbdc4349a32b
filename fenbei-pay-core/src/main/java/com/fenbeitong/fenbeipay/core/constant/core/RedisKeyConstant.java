package com.fenbeitong.fenbeipay.core.constant.core;

/**
 * Created by zhuminghua on 2017/7/19.
 */
public interface RedisKeyConstant {

    String USER_CENTER_REDIS_KEY = "user_center_redis_key:{0}";

    String CASHIER_REFUND_ORDERID_KEY = "cashier_refund_orderid_key:{0}";

    String CASHIER_PAY_ACCTPUBLIC_ORDERID_KEY = "acctPublic_pay_orderid_key:{0}";

    String CASHIER_REFUND_ACCTPUBLIC_ORDERID_KEY = "acctPublic_refund_orderid_key:{0}";

    String ACCOUNT_FLOW_SYNC_BANK_KEY = "fenbei-pay:account_flow_sync_bank_key:{0}";
    String ACCOUNT_MSG_SYNC_BANK_KEY = "fenbei-pay:account_msg_sync_bank_key:{0}";

    String CASHIER_REFUND_ERROR_ORDER_ID_KEY = "cashier_refund_order_id_key:{0}";

    String ACCT_RECHARGE_BANK_KEY = "acct_recharge_bank_key:{0}";

    String ACCT_SETTLEMENT_KEY = "acct_recharge_bank_key:{0}";

    String CASHIER_CREATE_AND_PAY_ORDER_ID_KEY = "cashier_create_pay_fb_order_id_key:{0}";


    String CASHIER_CREATE_ORDER_ID_KEY = "cashier_create_fb_order_id_key:{0}";

    String ACCT_REIMBURSEMENT_PAY_COUNT = "acct_reimbursement_pay_count:{0}:{1}:{2}";

    /**
     * 报销账户支付唯一索引
     */
    String CASHIER_PAY_REIMBURSEMENT_ORDERID_KEY = "acct_reimbursement_pay_orderid_key:{0}";

    String FBB_ACTIVITY_GRANT_CONFIG_DAILY_AMOUNT_KEY = "fbb_activity_grant_config_daily_amount_key:{0}:{1}";

    String FBB_ACTIVITY_GRANT_CONFIG_TOTAL_AMOUNT_KEY = "fbb_activity_grant_config_total_amount_key:{0}";

    String FBB_ACTIVITY_GRANT_EMPLOYEE_ORDER_ID_KEY = "fbb_activity_grant_employee_order_id_key:{0}:{1}";

    /**
     * 收银支付唯一索引
     */
    String CASHIER_CREATE_AND_PAY_ORDER_KEY = "fenbei-pay:cashier_create_and_pay_orderid_key:{0}:{1}";


    String CASHIER_BATCH_PAY_KEY = "fenbei-pay:cashier_batch_pay_key:{0}";

    /**
     * 按企业解圈存Key
     */
    String FBB_TRAP_COMPANY_KEY = "fbb_trap_company_key:{0}";
}
