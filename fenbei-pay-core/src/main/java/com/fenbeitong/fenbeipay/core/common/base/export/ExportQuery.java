package com.fenbeitong.fenbeipay.core.common.base.export;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: java类作用描述
 * @ClassName: ExportQuery
 * @Author: zhangga
 * @CreateDate: 2019/4/18 3:54 PM
 * @UpdateUser:
 * @UpdateDate: 2019/4/18 3:54 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class ExportQuery implements Serializable {
    /**
     * 查询项目标识
     **/
    private String taskSrc;
    /**
     * 任务分类标识, 各系统可创建自己的分类[例如: 50:外卖订单导出, 10:用户列表导出]
     * 任务分类ID，可根据需求自行维护任务分类，用于任务查询的筛选操作
     **/
    private Integer taskCategory;
    /**
     * 任务分类名称
     **/
    private String taskCategoryName;
    /**
     * 文档说明。此段说明将加在文档头部第一行, 如需换行请用换行符"\n"
     **/
    private String description;
}
