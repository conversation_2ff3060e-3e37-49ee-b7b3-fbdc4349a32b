package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;


@Data
@ToString(callSuper = true)
public class CashierCreatePayNoEmployeeTradeRespVo extends CashierPayTradeBaseRespVo {

    //渠道来源
    private Integer  operationChannelType;

    /**
     * 企业支付总金额
     */
    private BigDecimal amountCompany ;

    /**
     * 红包券支付总金额
     */
    private BigDecimal amountRedcoupon;
}
