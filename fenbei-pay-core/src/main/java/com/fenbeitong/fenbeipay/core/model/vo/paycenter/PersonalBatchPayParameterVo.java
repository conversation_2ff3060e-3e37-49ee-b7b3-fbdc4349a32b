package com.fenbeitong.fenbeipay.core.model.vo.paycenter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 因私批量接口参数
 */
public class PersonalBatchPayParameterVo {

    private BigDecimal totalAmount;
    private Integer businessType;
    private Integer orderType;
    //当前消费/退款员工id（必填）
    private String currentEmployeeId;
    private List<PersonalPayParameterVo> orderList;

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getCurrentEmployeeId() {
        return currentEmployeeId;
    }

    public void setCurrentEmployeeId(String currentEmployeeId) {
        this.currentEmployeeId = currentEmployeeId;
    }

    public List<PersonalPayParameterVo> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<PersonalPayParameterVo> orderList) {
        this.orderList = orderList;
    }
}
