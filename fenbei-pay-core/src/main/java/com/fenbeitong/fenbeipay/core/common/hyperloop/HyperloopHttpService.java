package com.fenbeitong.fenbeipay.core.common.hyperloop;

import com.fenbeitong.fenbeipay.core.common.hyperloop.dto.PushAlertDto;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date：2019-01-05
 */
public interface HyperloopHttpService {


    /**
     * 推送push消息
     * @param dto
     * @return
     */
    @POST("/push")
    Call<HashMap> push(@Body PushAlertDto dto);
}
