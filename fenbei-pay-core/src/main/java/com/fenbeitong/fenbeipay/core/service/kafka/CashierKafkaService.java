package com.fenbeitong.fenbeipay.core.service.kafka;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord;
import com.fenbeitong.finhub.common.cashier.CashierThirdOptType;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAcctFlowMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaFbbFlowMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaThirdFlowMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.fenbeitong.finhub.kafka.producer.ISaturnKafkaProducerPublisher;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

/**
 * @Description: 收银台异步kafka消息类
 * @ClassName: CashierKafkaService
 * @Author: zhangga
 * @CreateDate: 2021/5/25 8:59 下午
 * @UpdateUser:
 * @UpdateDate: 2021/5/25 8:59 下午
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Service
public class CashierKafkaService {

    @Autowired
    private ISaturnKafkaProducerPublisher iSaturnKafkaProducerPublisher;

    @Autowired
    private IKafkaProducerPublisher ikafkaProducerPublisher;

    /**
     * @Description: 异步发送kafka消息
     * @methodName: asyncSendKafkaMsg
     * @Param: [msg]
     * @return: void
     * @Author: zhangga
     * @Date: 2021/5/25 9:05 下午
     **/
    @Transactional(transactionManager = "fenbeipay", readOnly = false, rollbackFor = Exception.class)
    public void asyncSendKafkaMsg4AcctFlow(KafkaAcctFlowMsg msg) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交完毕时，触发
                CompletableFuture.runAsync(() -> {
                    try {
                        iSaturnKafkaProducerPublisher.publish(msg);
                    } catch (Exception e) {
                        FinhubLogger.error("【发送kafka消息异常】msg:{}", JSONObject.toJSONString(msg), e);
                    }
                });
                // 向低版本发送mq
                CompletableFuture.runAsync(() -> {
                    try {
                        ikafkaProducerPublisher.publish(msg);
                    } catch (Exception e) {
                        FinhubLogger.error("【发送向低版本发送mqkafka消息异常】msg:{}", JSONObject.toJSONString(msg), e);
                    }
                });
            }
        });
    }

    public void asyncSendKafkaMsg4FbbFlow(PersonAccountFlow flow,String refundOrderId) {
        try {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    // 事务提交完毕时，触发
                    CompletableFuture.runAsync(() -> {
                        KafkaFbbFlowMsg msg = new KafkaFbbFlowMsg();
                        msg.setFlowId(flow.getId());
                        msg.setAccountId(flow.getAccountId());
    //                    msg.setAccountModel();
                        msg.setCategoryType(flow.getOrderType());
    //                    msg.setTradeType();
                        msg.setOperationType(flow.getBusinessType());
    //                    msg.setOperationTypeDesc();
                        msg.setBizNo(flow.getOrderId());
                        msg.setReBizNo(flow.getOrderId());
                        msg.setOperationAmount(flow.getAmount());
                        msg.setBalance(flow.getBalance());
                        FinhubLogger.info("【发送kafka消息】msg:{}", JSONObject.toJSONString(flow));
                        iSaturnKafkaProducerPublisher.publish(msg);
                    });
                }
            });
        } catch (Exception e) {
            FinhubLogger.error("【发送kafka消息异常】msg:{}", JSONObject.toJSONString(flow), e);
        }
    }

    public void asyncSendKafkaMsg4ThirdFlow(PersonPayRecord payRecord, PersonRefundRecord refundRecord) {
        // 事务提交完毕时，触发
        CompletableFuture.runAsync(() -> {
            FinhubLogger.info("【发送kafka消息 个人三方支付】payRecord：{}，refundRecord：{}", JSONObject.toJSONString(payRecord), JSONObject.toJSONString(refundRecord));
            try {
                KafkaThirdFlowMsg msg = new KafkaThirdFlowMsg();
                if (!ObjUtils.isNull(payRecord)) {
                    msg.setFlowId(payRecord.getId());
                    msg.setCategoryType(payRecord.getType());
                    msg.setOperationType(CashierThirdOptType.CONSUMER.getKey());
                    msg.setBizNo(payRecord.getFbOrderId());
                    msg.setOperationAmount(ObjUtils.isNull(payRecord.getAmount()) ? BigDecimal.ZERO : BigDecimal.valueOf(payRecord.getAmount()));
                }
                if (!ObjUtils.isNull(refundRecord)) {
                    msg.setFlowId(refundRecord.getId());
                    msg.setOperationType(CashierThirdOptType.REFUND.getKey());
                    msg.setBizNo(refundRecord.getOrderId());
                    msg.setReBizNo(refundRecord.getRefundNo());
                    msg.setOperationAmount(ObjUtils.isNull(refundRecord.getRefundAmount()) ? BigDecimal.ZERO : BigDecimal.valueOf(refundRecord.getRefundAmount()));
                }

                iSaturnKafkaProducerPublisher.publish(msg);
            } catch (BeansException e) {
                FinhubLogger.error("【发送kafka消息异常】payRecord：{}，refundRecord：{}", JSONObject.toJSONString(payRecord), JSONObject.toJSONString(refundRecord), e);
            }
        });
    }
}
