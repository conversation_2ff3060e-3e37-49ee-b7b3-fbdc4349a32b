package com.fenbeitong.fenbeipay.core.constant.paycenter;

import com.luastar.swift.base.config.PropertyUtils;

/**
 * Created by mac on 17/12/21.
 */
@Deprecated
public class FuQianLaConstant {

    /**
     * 付钱拉支付相关参数
     */
    public static String privateKey = PropertyUtils.getString("fuqianla.private.key", "");
    public static String publicKey = PropertyUtils.getString("fuqianla.public.key", "");
    public static String appid = PropertyUtils.getString("fuqianla.appid", "");
    public static String notify_url = PropertyUtils.getString("fuqianla.notify_url", "");
    public static String host_hyperloop = PropertyUtils.getString("api.hyperloop.host", "");
    public static String host_car_biz = PropertyUtils.getString("host.car.biz.host", "");
    public static String ULLS = PropertyUtils.getString("fuqianla.notify_url", "");
    public static String hyperloop_order_check = host_car_biz + "/internal/taxi/order/pre_personal_pay";
    public static String hyperloop_order_finish = host_car_biz + "/internal/taxi/order/personal_pay";
    public static String host_hotel_biz = PropertyUtils.getString("host.hotel.biz.host", "");
    public static String hotel_order_check= host_hotel_biz+ "/v1/api/hotel/order/person_pay_price";
    public static String hotel_order_finish=host_hotel_biz+"/internal/api/hotel/order/person_pay_callback";
    //付钱拉域名
    public static String fuqianla_host = "https://api.fuqian.la";
    //支付订单查询
    public static String fuqianla_singquery_url = fuqianla_host + "/services/order/singleQuery";
    //退款请求
    public static String fuqianla_refund_url = fuqianla_host + "/services/order/refund";
    //退款请求
    public static String fuqianla_refund_query_url = fuqianla_host + "/services/order/refund/query";
    //环境
    public static String fuqianla_fenbeitong_env = PropertyUtils.getString("fuqianla.fenbeitong.env", "bd");


    public static final Integer fuqianla_refund_retry_max_time = 10;

    public static final String fuqianla_refund_status_ing="01";
    public static final String fuqianla_refund_status_success="02";
    public static final String fuqianla_refund_status_fail="03";

    public static final String FQL_CHANNLE_LIKE = "%"+"_pay_"+"%";



}
