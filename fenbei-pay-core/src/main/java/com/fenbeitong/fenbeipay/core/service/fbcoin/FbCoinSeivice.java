package com.fenbeitong.fenbeipay.core.service.fbcoin;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.model.po.account.Account;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountExtMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountFlowExtMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbFlowExtMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbIssuingBillExtMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.account.AccountFbbIssuingBillMapper;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.paycenter.*;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbFlow;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBill;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.account.AccountFbbIssuingBillExample;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountExample;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountFlow;
import com.fenbeitong.fenbeipay.core.model.vo.fenbeitong.account.AccountFbbIssuingBillVo;
import com.fenbeitong.fenbeipay.core.model.vo.fenbeitong.account.CompanyFbbIssuingBillVo;
import com.fenbeitong.fenbeipay.core.utils.CommonUtils;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeBaseInfo;
import com.fenbeitong.usercenter.api.model.dto.employee.EmployeeContract;
import com.fenbeitong.usercenter.api.service.employee.IBaseEmployeeExtService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Created by fanqian on 2018/8/9.
 */
@Service
public class FbCoinSeivice {

    private static final Logger logger = LoggerFactory.getLogger(FbCoinSeivice.class);
    @Autowired
    private AccountFbbIssuingBillExtMapper accountFbbIssuingBillExtMapper;
    @Autowired
    private IBaseEmployeeExtService iBaseEmployeeExtService;
    @Autowired
    private AccountFbbFlowExtMapper accountFbbFlowExtMapper;
    @Autowired
    private AccountFbbIssuingBillMapper fbbIssuingBillMapper;
    @Autowired
    private PersonAccountExtMapper personAccountExtMapper;
    @Autowired
    private PersonAccountFlowExtMapper personAccountFlowExtMapper;
    @Autowired
    private PersonAccountMapper personAccountMapper;


    /**
     * 查询企业分贝币操作记录
     *
     * @param companyId
     * @param operator_name
     * @param number
     * @param bill_type
     * @param pageIndex
     * @param pageSize
     * @param time_start
     * @param time_end
     * @return
     */
    public Map<String, Object> selectBill(String companyId, String operator_name, Integer number, Integer bill_type, Integer pageIndex, Integer pageSize, String time_start, String time_end) {
        Map<String, Object> params = Maps.newHashMap();
        if (!StringUtils.isEmpty(time_start)) {
            time_start = time_start + " 00:00:00";
            validDateIllegal(time_start);
            Timestamp time_start_stamp = Timestamp.valueOf(time_start);
            params.put("time_start", time_start_stamp);
        }
        if (!StringUtils.isEmpty(time_end)) {
            time_end = time_end + " 23:59:59";
            validDateIllegal(time_end);
            Timestamp time_end_stamp = Timestamp.valueOf(time_end);
            params.put("time_end", time_end_stamp);
        }
        params.put("companyId", companyId);
        params.put("operator_name", operator_name);
        params.put("number", number);
        Integer offset = pageSize * (pageIndex - 1);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("bill_type", bill_type);  // bill_type =1  购买
        logger.info("/fbb/query/list请求开始 入参为  = {}", params);
        List<Map<String, Object>>  result = Lists.newArrayList();
                List<CompanyFbbIssuingBillVo> buyIssuingBill = accountFbbIssuingBillExtMapper.selectBill(params);
        if(CollectionUtils.isNotEmpty(buyIssuingBill)){
            buyIssuingBill.forEach(companyFbbIssuingBill -> {
                Map<String, Object> resultObj = Maps.newHashMap();
                Map<String, Object> company = Maps.newHashMap();
                company.put("companyId",companyFbbIssuingBill.getCompanyId());
                company.put("companyName",companyFbbIssuingBill.getCompanyName());
                resultObj.put("company",company);
                resultObj.put("orderId",companyFbbIssuingBill.getOrderId());
                resultObj.put("createTime",companyFbbIssuingBill.getCreateTime());
                resultObj.put("operatorName",companyFbbIssuingBill.getOperatorName());
                resultObj.put("number",companyFbbIssuingBill.getNumber());
                resultObj.put("billType",companyFbbIssuingBill.getBillType());
                result.add(resultObj);
            });
        }
        int count = selectBillCount(params);
        Map<String, Object> data = Maps.newHashMap();
        data.put("results", result);
        data.put("count", count);
        return data;
    }


    /**
     * 企业分贝币操作记录导出数据
     *
     * @param companyId
     * @param operator_name
     * @param number
     * @param bill_type
     * @param time_start
     * @param time_end
     * @return
     */
    public List<CompanyFbbIssuingBillVo> exportList(String companyId, String operator_name, Integer number, Integer bill_type, String time_start, String time_end) {
        Map<String, Object> params = Maps.newHashMap();
        if (!StringUtils.isEmpty(time_start)) {
            time_start = time_start + " 00:00:00";
            validDateIllegal(time_start);
            Timestamp time_start_stamp = Timestamp.valueOf(time_start);
            params.put("time_start", time_start_stamp);
        }
        if (!StringUtils.isEmpty(time_end)) {
            time_end = time_end + " 23:59:59";
            validDateIllegal(time_end);
            Timestamp time_end_stamp = Timestamp.valueOf(time_end);
            params.put("time_end", time_end_stamp);
        }
        params.put("company_name", companyId);
        params.put("operator_name", operator_name);
        params.put("number", number);
        params.put("bill_type", bill_type);  // bill_type =1  购买
        logger.info("/fbb/exportList/list请求开始 入参为  = {}", params);
        List<CompanyFbbIssuingBillVo> buyIssuingBill = accountFbbIssuingBillExtMapper.selectBill(params);
        return buyIssuingBill;
    }


    /**
     * 企业购买操作记录导出数据
     *
     * @param companyId
     * @param buyer_name
     * @param buyer_phone
     * @param bill_type
     * @param buyer_time_start
     * @param buyer_time_end
     * @return
     */
    public List<AccountFbbIssuingBill> exportBuyList(String companyId, String buyer_name, String buyer_phone, int bill_type, String buyer_time_start, String buyer_time_end) {
        Map<String, Object> params = Maps.newHashMap();
        if (StringUtils.isEmpty(companyId)) {
            throw new FinhubException(GlobalResponseCode.TOKEN_EXPIRE.getCode(), GlobalResponseCode.TOKEN_EXPIRE.getType(), GlobalResponseCode.TOKEN_EXPIRE.getMsg());
        }

        if (!StringUtils.isEmpty(buyer_time_start)) {
            buyer_time_start = buyer_time_start + " 00:00:00";
            validDateIllegal(buyer_time_start);
            Timestamp time_start = Timestamp.valueOf(buyer_time_start);
            params.put("time_start", time_start);
        }

        if (!StringUtils.isEmpty(buyer_time_end)) {
            buyer_time_end = buyer_time_end + " 23:59:59";
            validDateIllegal(buyer_time_end);
            Timestamp time_end = Timestamp.valueOf(buyer_time_end);
            params.put("time_end", time_end);
        }
        params.put("companyId", companyId);
        params.put("operator_name", buyer_name);
        params.put("operator_phone", buyer_phone);
        params.put("bill_type", BillType.BUY.getKey());  // bill_type =1  购买

        logger.info("/fbb/buy/export 请求开始 入参为  = {}", params);

        //获取购买记录
        List<AccountFbbIssuingBill> buyIssuingBill = selectFbbIssuingBill(params);
        return buyIssuingBill;
    }

    /**
     * 查询企业分贝币操作记录数
     *
     * @param params
     * @return
     */
    public int selectBillCount(Map<String, Object> params) {
        return accountFbbIssuingBillExtMapper.selectBillCount(params);
    }

    /**
     * 查询购买/分发 分贝币记录
     *
     * @param params
     * @return
     */
    public List<AccountFbbIssuingBill> selectFbbIssuingBill(Map<String, Object> params) {
        List<AccountFbbIssuingBill> buyIssuingBill = accountFbbIssuingBillExtMapper.selectByParams(params);
        return buyIssuingBill;
    }

    /**
     * 查询购买/分发 分贝币记录数
     *
     * @param params
     * @return
     */
    public int selectCount(Map<String, Object> params) {
        return accountFbbIssuingBillExtMapper.count(params);
    }


    /**
     * 查询发放单详情
     *
     * @param order_id
     * @param company_id
     * @return
     */
    public AccountFbbIssuingBillVo getBillDeatil(String order_id, String company_id) {
        if (StringUtils.isEmpty(order_id) || StringUtils.isEmpty(company_id)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        return accountFbbIssuingBillExtMapper.getDeatil(order_id, company_id);
    }

    /**
     * 查询发放单发放员工
     *
     * @param params
     * @return
     */
    public List<AccountFbbIssuingBillVo> findAllDispatchEmployee(Map<String, Object> params) {
        return accountFbbIssuingBillExtMapper.selectDispatchEmployee(params);
    }


    /**
     * 查询某单发放员工(查询某单可撤回对象)
     *
     * @param type 1: 查询某单发放员工 2: 查询某单撤回对象
     * @param order_id
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @param type
     * @return
     */
    public Map<String, Object> findDispatchEmployee(String order_id, String companyId, Integer pageIndex, Integer pageSize, Integer type) {

        CommonUtils.analyzeObjects(order_id, companyId, type);
        OperationType operationType = OperationType.getEnum(type);
        CommonUtils.analyzeObjects(operationType);
        Map<String, Object> params = Maps.newHashMap();
        Integer offset = pageSize * (pageIndex - 1);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("order_id", order_id);
        params.put("company_id", companyId);
        params.put("business_type", PersonPayBusinessType.Recharge.getKey()); //    person_account_flow : '消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回';
        logger.info("/fbb/dispatch/employee/list 请求开始 入参为 params ={}", params);

        List<AccountFbbIssuingBillVo> employeeList = findAllDispatchEmployee(params);
        int count = getDispatchCount(params);
        Map<String, Object> result = Maps.newHashMap();

        if (operationType.getKey() == OperationType.Grant_Employee.getKey()) {
            result.put("results", employeeList);
            result.put("count", count);
            return result;
        }
        //查询撤回对象
        Map<String, Object> recallParams = Maps.newHashMap();
        recallParams.put("order_id", params.get("order_id"));
        recallParams.put("company_id", params.get("company_id"));
        recallParams.put("business_type", PersonPayBusinessType.Exchange.getKey());      // 消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回

        //查询该单撤回员工
        List<AccountFbbIssuingBillVo> recallEmployee = accountFbbIssuingBillExtMapper.selectRecallEmployee(recallParams);
        List<String> recallEmployeeIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recallEmployee)) {
            result.put("results", employeeList);
            result.put("count", count);
            return result;
        }

        recallEmployee.stream().forEach(employee -> recallEmployeeIdList.add(employee.getId()));
        //查询没有进行过操作记录的发放对象
        params.put("employeeIdList", recallEmployeeIdList);
        employeeList = findAllDispatchEmployee(params);
        count = getDispatchCount(params);
        result.put("results", employeeList);
        result.put("count", count);

        return result;
    }

    /**
     * 查询某发单撤回员工
     *
     * @param order_id
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @param business_type
     * @return
     */
    public Map<String, Object> selectRecallEmployee(String companyId, String order_id, Integer pageIndex, Integer pageSize, int business_type) {

        if (StringUtils.isEmpty(order_id) || StringUtils.isEmpty(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }

        Map<String, Object> params = Maps.newHashMap();
        Integer offset = pageSize * (pageIndex - 1);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("order_id", order_id);
        params.put("company_id", companyId);
        params.put("business_type", business_type);      // '消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回';

        List<AccountFbbIssuingBillVo> recallEmployeeList = findRecallEmployee(params);
        logger.info("/fbb/recall/list 请求开始 入参为 params ={}", params);
        int recallCount = getRecallCount(params);

        Map<String, Object> data = Maps.newHashMap();
        data.put("count", recallCount);
        data.put("results", recallEmployeeList);
        return data;
    }


    /**
     * 查询发放单发放员工数量
     *
     * @param params
     * @return
     */
    public int getDispatchCount(Map<String, Object> params) {
        return accountFbbIssuingBillExtMapper.selectDispatchEmployeeCount(params);
    }

    /**
     * 查询发放单撤回员工
     *
     * @param params
     * @return
     */
    public List<AccountFbbIssuingBillVo> findRecallEmployee(Map<String, Object> params) {
        List<AccountFbbIssuingBillVo> result = accountFbbIssuingBillExtMapper.selectRecallEmployee(params);
        if (CollectionUtils.isEmpty(result))
            throw new FinhubException(GlobalResponseCode.BILL_ORDER_NOT_RECALL.getCode(), GlobalResponseCode.BILL_ORDER_NOT_RECALL.getType(), GlobalResponseCode.BILL_ORDER_NOT_RECALL.getMsg());

        return result;
    }


    /**
     * 查询发放单撤回员工数量
     *
     * @param params
     * @return
     */
    public int getRecallCount(Map<String, Object> params) {
        return accountFbbIssuingBillExtMapper.recallEmployeeCount(params);
    }


    /**
     * 查询发放单详情
     *
     * @param order_id
     * @param company_id
     * @return
     */
    public AccountFbbIssuingBillVo getRecallDeatil(String order_id, String company_id) {
        if (StringUtils.isEmpty(order_id) || StringUtils.isEmpty(company_id)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        AccountFbbIssuingBillVo grantDeatail = accountFbbIssuingBillExtMapper.getRecallDeatil(order_id, company_id);
        if (grantDeatail == null)
            throw new FinhubException(GlobalResponseCode.BILL_ORDER_NOT_RECALL.getCode(), GlobalResponseCode.BILL_ORDER_NOT_RECALL.getType(), GlobalResponseCode.BILL_ORDER_NOT_RECALL.getMsg());
        return grantDeatail;
    }


    /**
     * 企业购买分贝币
     *
     * @param user_id
     * @param company_id
     * @param number
     * @param pay_way
     * @param remark
     * @return
     *//*
    @Deprecated
    @Transactional(transactionManager = "fenbeitong", readOnly = false, rollbackFor = Exception.class)
    public String buyFbCoin(String user_id, String company_id, Integer number, Integer pay_way, String remark) throws FinhubException {
        logger.info("企业购买分贝币 入参是:user_id={}, company_id={}, number={}, pay_way={},remark ={} ", user_id, company_id, number, pay_way, remark);
        //校验入参
        this.buyCoinCheck(user_id, company_id, number, pay_way);

        //锁定账户
        Account account = this.lockAccount(company_id);

        //查询操作人信息
        EmployeeContract employee = this.getEmployeeContract(user_id, company_id);

        BigDecimal credit = account.getCredit();  //企业剩余资金或剩余额度
        BigDecimal buyNumber = new BigDecimal(number);
        logger.info("企业可用余额或者可用额度是 credit={}, 购买数量是: number={},当前分贝币余额是={},当前分贝币总兑换数={}", credit, number, account.getFbbBalance(), account.getExchangeFbbAmount());

        //校验企业余额
        int result = credit.compareTo(buyNumber);
        if (result == -1)
            throw new FinhubException(GlobalResponseCode.BALANCE_NOT_ENOUGH.getCode(), GlobalResponseCode.BALANCE_NOT_ENOUGH.getType(), GlobalResponseCode.BALANCE_NOT_ENOUGH.getMsg());

        //创建发放单
        String billId = RandomUtils.bsonId();
        this.createAccountBill(account.getId(), buyNumber, user_id, employee.getName(), employee.getPhone_num(), billId, remark);

        BigDecimal fbbBalance = account.getFbbBalance().add(buyNumber);  //计算account 账户分贝币余额

        //创建流水
        this.createBuyFlow(billId, buyNumber, fbbBalance, account, user_id, employee, remark, credit);

        //修改企业账户余额
        logger.info("修改企业账户 资金余额，分贝币余额，总兑换分贝币数,入参: number={}", number);
        int companyEffect = accountExtMapper.updateAccountBuyBalance(account.getId(), number, AccountLockType.Unlock.getKey(), "", account.getFbbBalance());
        if (companyEffect <= 0)
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), "数据库操作失败");

        return billId;
    }*/


    /**
     * 分贝币购买参数校验
     *
     * @param user_id
     * @param company_id
     * @param number
     * @param pay_way
     */
    private void buyCoinCheck(String user_id, String company_id, Integer number, Integer pay_way) {
        CheckUtils.create()
                .addCheckEmpty(user_id, "user_id 不能为空")
                .addCheckEmpty(company_id, "company_id 不能为空")
                .addCheckEmpty(number, "number 不能为空")
                .addCheckEmpty(pay_way, "pay_way 不能为空")
                .check();

        if (new BigDecimal(number).compareTo(BigDecimal.ZERO) <= 0) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
    }

    /**
     * 锁定企业账户
     * @param company_id
     *//*
    @Deprecated
    private Account lockAccount(String company_id) throws FinhubException {
        CheckUtils.create()
                .addCheckEmpty(company_id, "company_id 不能为空")
                .check();

        logger.info("锁定账户,入参:company_id={}", company_id);
        int lockR = accountExtMapper.lockAccount(company_id, AccountLockType.Lock.getKey(), PayCenterConstant.PAY_LOCK_REASON); //AccountLockType.Lock.getKey(), PayCenterConstant.PAY_LOCK_REASON
        Account account = getAccount(company_id);   // 查询公司账户余额
        if (lockR <= 0) {    //账户被锁定
            logger.error("锁定账户失败，账户ID为: account_id={}",  account.getId());
            throw new FinhubException(GlobalResponseCode.ACCOUNT_IS_LOCK.getCode(), GlobalResponseCode.ACCOUNT_IS_LOCK.getType(), account.getLockReason());
        }
        logger.info("账户信息为: account={}",  JsonUtils.toJson(account));
        return account;
    }*/

    /**
     * 创建购买发放单
     *@param accountId
     * @param buyNumber
     * @param userId
     * @param employeeName
     * @param employeePhone
     * @param billId
     * @param remark
     *
     */
    private void createAccountBill(Integer accountId, BigDecimal buyNumber, String userId, String employeeName, String employeePhone, String billId, String remark) throws FinhubException {
        logger.info("创建购买发放单 入参是:accountId={}, buyNumber={}, user_id={}, employeeName={},employeePhone ={} ,billId={},remark={}", accountId, buyNumber, userId, employeeName, employeePhone, billId, remark);
        Date orderCreateTime = new Date();
        AccountFbbIssuingBill bill = new AccountFbbIssuingBill();
        bill.setAccountId(accountId);
        bill.setAmount(buyNumber);
        bill.setFbbAmount(buyNumber);
        bill.setOperatorId(userId);
        bill.setOperatorName(employeeName);
        bill.setOperatorName(employeeName);
        bill.setOperatorPhone(employeePhone);
        bill.setBillType(BillType.BUY.getKey());    //类型：1、购买 2、发放
        bill.setComment(remark);
        bill.setCreateTime(orderCreateTime);
        bill.setId(billId);
        logger.info("创建购买单,组装数据:{}", JsonUtils.toJson(bill));

        int i = fbbIssuingBillMapper.insertSelective(bill);
        if (i <= 0)
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), "创建购买发放单出错");

    }


    /**
     * 创建购买分贝币流水
     *@param billId
     * @param buyNumber
     * @param fbbBalance
     * @param account
     * @param user_id
     * @param employee
     * @param remark
     * @param credit
     *//*
    @Deprecated
    private void createBuyFlow(String billId, BigDecimal buyNumber, BigDecimal fbbBalance, Account account, String user_id,
                               EmployeeContract employee, String remark, BigDecimal credit) {
        logger.info("创建购买分贝币流水 入参是:billId={}, buyNumber={}, fbbBalance={}, account={},user_id ={} ,employee={},remark={},credit={}", billId, buyNumber, fbbBalance, account, user_id, employee, remark, credit);
        Date orderCreateTime = new Date();
        //  企业账户资金流水
        AccountFlow accountFlow = new AccountFlow();
        accountFlow.setId(RandomUtils.bsonId());
        accountFlow.setAccountId(account.getId());
        accountFlow.setAmount(buyNumber.negate());
        accountFlow.setBusinessType(PayBusinessType.Exchange.getKey()); //消费类型：1、消费支出，2、充值，3、退款，4、分贝币兑换
        accountFlow.setOrderId(billId);
        accountFlow.setBalance(credit.subtract(buyNumber));
        accountFlow.setComment(remark);
        accountFlow.setOperatorId(user_id);
        accountFlow.setOperatorName(employee.getName());
        accountFlow.setCreateTime(orderCreateTime);
        logger.info("企业账户资金流水组装数据,入参:{}", JsonUtils.toJson(accountFlow));
        int n = accountFlowMapper.insertSelective(accountFlow);
        if (n <= 0)
            logger.error("记录企业资金账户流水出错，订单号是:{}", billId);

        //企业分贝币流水
        AccountFbbFlow accountFbbFlow = new AccountFbbFlow();
        accountFbbFlow.setId(RandomUtils.bsonId());
        accountFbbFlow.setAccountId(account.getId());
        accountFbbFlow.setAmount(buyNumber);  // 金额，充值为正，消费为负
        accountFbbFlow.setBusinessType(BillBusinessType.Fbb_Recharge.getKey());   // 消费类型：1、分贝币充值，2、分贝币发放，3、分贝币撤回;
        accountFbbFlow.setBalance(fbbBalance);  //账户余额
        accountFbbFlow.setOrderId(billId);
        accountFbbFlow.setComment(remark);
        accountFbbFlow.setOperatorId(user_id);
        accountFbbFlow.setOperatorName(employee.getName());
        accountFbbFlow.setCreateTime(orderCreateTime);
        logger.info("企业分贝币流水组装数据,入参:{}", JsonUtils.toJson(accountFbbFlow));
        n = accountFbbFlowMapper.insertSelective(accountFbbFlow);
        if (n <= 0)
            logger.error("记录企业分贝币账户流水出错，订单号是:{}", billId);
    }*/

/*

    /**
     * 分贝币发放
     *
     * @param user_id
     * @param company_id
     * @param number
     * @param reason
     * @param remark
     * @param type
     * @param paramIdList
     * @return
     *//*

    @Deprecated
    @Transactional(transactionManager = "fenbeitong", readOnly = false, rollbackFor = Exception.class)
    public Map<String, Object> dispatch(String user_id, String company_id, Integer number, String reason, String remark,
                                        Boolean type, Object paramIdList) throws FinhubException {
        logger.info("分贝币发放 入参是:user_id={}, company_id={}, number={}, reason={},remark ={},type={},paramIdList={} ", user_id, company_id, number, reason, remark, type, JsonUtils.toJson(paramIdList));

        //参数校验
        this.checkDispatchParam(user_id, company_id, number, reason, type);
        if (!type) {
            CheckUtils.create().addCheckEmpty(paramIdList, "idList 不能为空").check();
        }
        //获取发放员工ID
        List<String> idList = getEmployeeList(company_id, type, paramIdList);

        //锁定账户
        Account account = this.lockAccount(company_id);

        //计算发放总额
        Integer dispatchSum = number * idList.size();
        BigDecimal fbbBalance = account.getFbbBalance();
        BigDecimal bigdispatchSum = BigDecimal.valueOf(dispatchSum);
        logger.info("企业准备发放金额是：{},准备发放员工数量是:{}, 企业余额是:{}", bigdispatchSum, idList.size(), fbbBalance);

        int result = fbbBalance.compareTo(bigdispatchSum);
        if (result == -1)  //余额少于分发数量
            throw new FinhubException(GlobalResponseCode.FBB_BALANCE_NOT_ENOUGH.getCode(), GlobalResponseCode.FBB_BALANCE_NOT_ENOUGH.getType(), GlobalResponseCode.FBB_BALANCE_NOT_ENOUGH.getMsg());

        List<String> validEmployeeId = Lists.newArrayList();
        List<String> notValidEmployeeId = Lists.newArrayList();

        Map<String, PersonAccount> personAccountMap = getPersonAccount(idList);      //校验员工账户信息
        idList.forEach(employeeId -> {   //信息有效性及
            PersonAccount personAccount = personAccountMap.get(employeeId);
            if (personAccount == null) {
                logger.info("员工信息或者员工账户信息不存在, employeeId={}", employeeId);
                notValidEmployeeId.add(employeeId);
            } else {
                validEmployeeId.add(employeeId);
            }
        });

        Integer relWithdrawing = number * validEmployeeId.size();  //实际发放金额
        bigdispatchSum = BigDecimal.valueOf(relWithdrawing);
        logger.info("企业实际发放金额是：{},实际发放员工数量是:{}，实际发放员工ID是:{}", bigdispatchSum, validEmployeeId.size(), JsonUtils.toJson(validEmployeeId));
        if (CollectionUtils.isEmpty(validEmployeeId)) {
            throw new FinhubException(GlobalResponseCode.EMPLOYEE_INFO_NOT_ERROR.getCode(), GlobalResponseCode.EMPLOYEE_INFO_NOT_ERROR.getType(), GlobalResponseCode.EMPLOYEE_INFO_NOT_ERROR.getMsg());
        }

        Date orderCreateTime = new Date();
        String billId = RandomUtils.bsonId();

        //创建发放单
        EmployeeContract employee = getEmployeeContract(user_id, company_id); //查询发放人信息
        AccountFbbIssuingBill bill = this.creatGrantBill(billId, account, bigdispatchSum, (long) validEmployeeId.size(), user_id, employee, reason, remark, orderCreateTime);

        //插入流水
        this.creatGrantFlow(personAccountMap, orderCreateTime, account.getId(), validEmployeeId, number, fbbBalance, relWithdrawing, billId, reason, remark, employee, company_id, user_id);

        //修改企业分贝币余额
        logger.info("真实扣款数：{}", relWithdrawing);
        int companyEffect = accountExtMapper.updateAccountBalance(relWithdrawing, company_id, fbbBalance);
        if (companyEffect != 1)
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());

        //个人账户分贝币增加
        logger.info("个人账户添加分贝币:{}, 发放员工数量:{}", number, validEmployeeId.size());
        int personEffect = personAccountExtMapper.updateAccount(number, validEmployeeId);
        if (personEffect != validEmployeeId.size())
            throw new FinhubException(GlobalResponseCode.PERSON_ACCOUNT_ERROR.getCode(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getType(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getMsg());

        bill.setStatus(BillStatusType.DISPATCH_SUCCESS.getKey());   //状态：0: 发放中 1、发放成功 2、发放失败 3、全部撤回 4、部分撤回   // 修改发放单状态
        bill.setUpdateTime(new Date());
        int i = fbbIssuingBillMapper.updateByPrimaryKeySelective(bill);
        if (i <= 0)
            logger.error("修改发放单状态操作失败[实际发放成功],, order_id={}, 发放单信息为:{},发放时间 ={}", billId,  JsonUtils.toJson(bill), orderCreateTime);

        Map<String, Object> bill_id = Maps.newHashMap();
        bill_id.put("order_id", billId);                     //订单ID
        bill_id.put("grant_count", validEmployeeId.size()); //实际发放员工数
        bill_id.put("grount_amount", relWithdrawing);      //实际总发放额
        return bill_id;
    }
*/


    /**
     * 创建发放单
     *
     * @param billId
     * @param account
     * @param bigdispatchSum
     * @param validEmployeeIdMum 实际发放员工数量
     * @param user_id
     * @param employee
     * @param reason
     * @param remark
     * @param orderCreateTime
     */
    private AccountFbbIssuingBill creatGrantBill(String billId, Account account, BigDecimal bigdispatchSum, long validEmployeeIdMum, String user_id, EmployeeContract employee,
                                                 String reason, String remark, Date orderCreateTime) {
        logger.info("创建发放单 billId={}, account={},bigdispatchSum={},validEmployeeIdMum={},user_id={}, employee={}, reason={},remark={},orderCreateTime={}",
                billId, JsonUtils.toJson(account), bigdispatchSum, validEmployeeIdMum, user_id, JsonUtils.toJson(employee), reason, remark, orderCreateTime);

        //创建发放单
        AccountFbbIssuingBill bill = new AccountFbbIssuingBill();
        bill.setId(billId);
        bill.setAccountId(account.getId());
        bill.setFbbAmount(bigdispatchSum);
        bill.setGrantEmployeeCount(validEmployeeIdMum);
        bill.setOperatorId(user_id);
        bill.setOperatorName(employee.getName());
        bill.setOperatorPhone(employee.getPhone_num());
        bill.setBillType(BillType.GRANT.getKey());  //类型：1、购买 2、发放
        bill.setStatus(BillStatusType.DISPATCHING.getKey());   //状态：0: 发放中 1、发放成功 2、发放失败 3、全部撤回 4、部分撤回
        bill.setReason(reason);
        bill.setComment(remark);
        bill.setCreateTime(orderCreateTime);
        bill.setAccountId(account.getId());

        logger.info("创建发放单,发放单组装数据:{}", JsonUtils.toJson(bill));

        int i = fbbIssuingBillMapper.insertSelective(bill);
        if (i <= 0)
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), "发放单创建失败");
        return bill;
    }


    /**
     * 创建发放单账户流水
     *
     * @param personAccountMap
     * @param orderCreateTime
     * @param accountId
     * @param validEmployeeId
     * @param number
     * @param fbbBalance
     * @param relWithdrawing
     * @param billId
     * @param reason
     * @param remark
     * @param employee
     * @param company_id
     * @param user_id
     *
     */
    private void creatGrantFlow(Map<String, PersonAccount> personAccountMap, Date orderCreateTime, Integer accountId,  List<String> validEmployeeId, Integer number, BigDecimal fbbBalance
            , Integer relWithdrawing, String billId, String reason, String remark, EmployeeContract employee, String company_id, String user_id) {
        logger.info("创建发放单账户流水 personAccountMap={}, orderCreateTime={},accountId={},validEmployeeId={}, number={}, fbbBalance={},relWithdrawing={},billId={},reason={},remark={},employee={},company_id={},user_id={}",
                JsonUtils.toJson(personAccountMap), orderCreateTime, accountId, JsonUtils.toJson(validEmployeeId), number, fbbBalance, relWithdrawing, billId, reason, remark, JsonUtils.toJson(employee), company_id, user_id);
        //组装发放流水数据
       String companyName = employee.getCompany_name();
        List<AccountFbbFlow> accountFbbFlowList = Lists.newArrayList();
        List<PersonAccountFlow> personAccountFlowList = Lists.newArrayList();
        validEmployeeId.forEach(employeeId -> {
            PersonAccount personAccount = personAccountMap.get(employeeId);

            BigDecimal intBalance = personAccount.getBalance();
            // 企业分贝币账户流水
            AccountFbbFlow accountFbbFlow = new AccountFbbFlow();
            accountFbbFlow.setCreateTime(orderCreateTime);
            accountFbbFlow.setId(RandomUtils.bsonId());
            accountFbbFlow.setAccountId(accountId);
            accountFbbFlow.setAmount(new BigDecimal(number).negate()); // 金额，充值为正，消费为负
            accountFbbFlow.setGrantAmount(new BigDecimal(number));    //发放金额
            accountFbbFlow.setBalance(fbbBalance.subtract(BigDecimal.valueOf(relWithdrawing))); //当前企业分贝币余额
            accountFbbFlow.setBusinessType(BillBusinessType.Fbb_Grant.getKey());      //消费类型：1、分贝币充值，2、分贝币发放，3、分贝币撤回
            accountFbbFlow.setPersonAccountId(personAccount.getId());
            accountFbbFlow.setOrderId(billId);
            accountFbbFlow.setReason(reason);
            accountFbbFlow.setComment(remark);
            accountFbbFlow.setOperatorId(employee.getId());
            accountFbbFlow.setOperatorName(employee.getName());
            accountFbbFlowList.add(accountFbbFlow);

            // 个人账户流水
            PersonAccountFlow personAccountFlow = new PersonAccountFlow();
            personAccountFlow.setId(RandomUtils.bsonId());
            personAccountFlow.setAccountId(personAccount.getId());
            personAccountFlow.setAmount(new BigDecimal(number)); //金额（元），充值为正，消费为负
            personAccountFlow.setBusinessType(PersonPayBusinessType.Recharge.getKey()); //消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回
            personAccountFlow.setOrderId(billId);
            personAccountFlow.setCompanyId(company_id);
            personAccountFlow.setBalance(intBalance.add(new BigDecimal(number)));
            personAccountFlow.setOperatorId(user_id);
            personAccountFlow.setOperatorName(employee.getName());
            personAccountFlow.setReason(reason);
            personAccountFlow.setCreateTime(orderCreateTime);
            Map<String, Object> commonJson = Maps.newHashMap();
            Map<String, String> companyInfo = Maps.newHashMap();
            companyInfo.put("companyName", companyName);
            companyInfo.put("operatorName", employee.getName());
            companyInfo.put("comment", reason);
            commonJson.put("companyInfo", companyInfo);
            personAccountFlow.setCommonJson(JSON.toJSONString(commonJson));
            personAccountFlowList.add(personAccountFlow);
        });


        //插入企业分贝币账户流水
        logger.info("创建分贝币发放流水， 有效员工:{}", validEmployeeId.size());

        int i = accountFbbFlowExtMapper.batchInsert(accountFbbFlowList);
        if (i != validEmployeeId.size())
            logger.error("插入异常，企业账户流水插入数量:{}， 总数量:{}", i, validEmployeeId.size());

        //插入个人账户流水
        i = personAccountFlowExtMapper.batchInsert(personAccountFlowList);
        if (i != validEmployeeId.size())
            logger.error("插入异常，员工账户流水插入数量:{}， 总数量:{}", i, validEmployeeId.size());
    }


    /**
     * 分贝币分发参数校验
     *
     * @param user_id
     * @param company_id
     * @param number
     * @param reason
     * @param type
     */
    private void checkDispatchParam(String user_id, String company_id, Integer number, String reason, Boolean type) {
        CheckUtils.create()
                .addCheckEmpty(user_id, "user_id 不能为空")
                .addCheckEmpty(company_id, "company_id 不能为空")
                .addCheckEmpty(number, "number 不能为空")
                .addCheckEmpty(reason, "reason 不能为空")
                .addCheckEmpty(type, "type 不能为空")
                .check();

        if (new BigDecimal(number).compareTo(BigDecimal.ZERO) <= 0) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
    }

    /**
     * 分贝币撤回参数校验
     *
     * @param user_id
     * @param company_id
     * @param order_id
     * @param reason
     * @param type
     */
    private void checkRecallParam(String user_id, String company_id, String order_id, String reason, Boolean type) {
        CheckUtils.create()
                .addCheckEmpty(user_id, "user_id 不能为空")
                .addCheckEmpty(company_id, "company_id 不能为空")
                .addCheckEmpty(order_id, "order_id 不能为空")
                .addCheckEmpty(reason, "reason 不能为空")
                .addCheckEmpty(type, "type 不能为空")
                .check();
    }

    /**
     * 查询操作人信息
     * @param user_id
     * @param company_id
     */
    public EmployeeContract getEmployeeContract(String user_id, String company_id) throws FinhubException {
        logger.info("查询操作人信息 入参是:user_id={}, company_id={}", user_id, company_id);
        CheckUtils.create()
                .addCheckEmpty(company_id, "company_id 不能为空")
                .addCheckEmpty(user_id, "user_id 不能为空")
                .check();

        EmployeeContract employee = null;
        try {
            employee = iBaseEmployeeExtService.queryEmployeeInfo(user_id, company_id);
        } catch (Exception e) {
            logger.error("：查询企业员工DUBBO 服务出错 错误信息为:{}  company_id ={},user_id={}", e.getLocalizedMessage(), company_id, user_id);
            throw new FinhubException(GlobalResponseCode.DUBBO_ERROR.getCode(), GlobalResponseCode.DUBBO_ERROR.getType(), GlobalResponseCode.DUBBO_ERROR.getMsg());
        }
        if (employee == null) {
            throw new FinhubException(GlobalResponseCode.EMPLOYEE_NOT_EXIST.getCode(), GlobalResponseCode.EMPLOYEE_NOT_EXIST.getType(), GlobalResponseCode.EMPLOYEE_NOT_EXIST.getMsg());
        }
        logger.info("操作人信息是: EmployeeContract={}", JsonUtils.toJson(employee));
        return employee;
    }

    /**
     * 查询发放单信息
     * @param order_id
     * @param bill_type
     */
    private AccountFbbIssuingBill getBill(String order_id, Integer bill_type) throws FinhubException {
        logger.info("查询发放单信息 入参是:order_id={}, bill_type={} ", order_id, bill_type);
        AccountFbbIssuingBillExample example = new AccountFbbIssuingBillExample();
        AccountFbbIssuingBillExample.Criteria criteria = example.createCriteria();
        criteria.andBillTypeEqualTo(BillType.GRANT.getKey());
        criteria.andIdEqualTo(order_id);
        List<AccountFbbIssuingBill> billRecordList = fbbIssuingBillMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(billRecordList))
            throw new FinhubException(GlobalResponseCode.DISPATCH_RECORD_NOT_EXIST.getCode(), GlobalResponseCode.DISPATCH_RECORD_NOT_EXIST.getType(), GlobalResponseCode.DISPATCH_RECORD_NOT_EXIST.getMsg());
        return billRecordList.get(0);
    }

    /**
     * 分贝币撤回
     *
     * @param user_id
     * @param company_id
     * @param order_id
     * @param reason
     * @param type
     * @param paramIdList
     * @return
     *//*
    @Deprecated
    @Transactional(transactionManager = "fenbeitong", readOnly = false, rollbackFor = Exception.class)
    public Map<String, Object> recall(String user_id, String company_id, String order_id, String reason, Boolean type, Object paramIdList) throws FinhubException {
        logger.info("分贝币撤回 入参是:user_id={}, company_id={}, order_id={}, reason={},type={},paramIdList={} ", user_id, company_id, order_id, reason, type,  JsonUtils.toJson(paramIdList));

        //参数校验
        this.checkRecallParam(user_id, company_id, order_id, reason, type);
        if (!type) {
            CheckUtils.create().addCheckEmpty(paramIdList, "idList 不能为空").check();
        }

        AccountFbbIssuingBill billRecord = getBill(order_id, BillType.GRANT.getKey());
        logger.info("发放单记录查询 : 结果 {} ",  JsonUtils.toJson(billRecord));

        if (billRecord.getStatus() == BillStatusType.RECALL_ALL.getKey())  //全部撤回则不可操作
            throw new FinhubException(GlobalResponseCode.BILL_ORDER_RECALLED.getCode(), GlobalResponseCode.BILL_ORDER_RECALLED.getType(), GlobalResponseCode.BILL_ORDER_RECALLED.getMsg());

        //判断距离系统时间
        long calculationHours = DateUtil.calculationHours(billRecord.getCreateTime());
        logger.info("该发放单发放时间为 : {} , 当前撤回时间为:{}", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(billRecord.getCreateTime()), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        if (calculationHours >= 48)
            throw new FinhubException(GlobalResponseCode.OVER_TIME.getCode(), GlobalResponseCode.OVER_TIME.getType(), GlobalResponseCode.OVER_TIME.getMsg());
        EmployeeContract employee = getEmployeeContract(user_id, company_id); //查询撤回操作人信息

        //获取该单发放员工ID 并校验重复撤回员工
        List<String> idList = findDispatchEmployeeIdList(billRecord, company_id, type, paramIdList, order_id);
        logger.info("该单发放员工数量：{},员工ID列表:{} ", idList.size(),  JsonUtils.toJson(idList));

        //锁定企业账户
        logger.info("锁定企业账户 ,企业ID:{} ", company_id);
        int lockR = accountExtMapper.lockAccount(company_id, AccountLockType.Lock.getKey(), PayCenterConstant.PAY_LOCK_REASON);
        Account account = getAccount(company_id);                            // 查询公司账户信息
        if (lockR <= 0) //账户被锁定 暂时不可操作
            throw new FinhubException(GlobalResponseCode.ACCOUNT_IS_LOCK.getCode(), GlobalResponseCode.ACCOUNT_IS_LOCK.getType(), account.getLockReason());

        //锁定员工账户

        List<AccountFbbFlow> accountFbbFlowList = Lists.newArrayList();
        List<PersonAccountFlow> personAccountFlowList = Lists.newArrayList();
        List<String> fullAmount = Lists.newArrayList();
        List<String> noFullAmount = Lists.newArrayList();
        BigDecimal sumReduce = BigDecimal.valueOf(0);
        BigDecimal grantAmount = billRecord.getFbbAmount();
        BigDecimal avgGrantAmount = grantAmount.divide(BigDecimal.valueOf(billRecord.getGrantEmployeeCount()), 2, RoundingMode.HALF_UP); //平均发放币数量
        logger.info("该单一共发放币数量是：{},发放员工数量是:{} ，平均发放额是: {}", billRecord.getFbbAmount(), billRecord.getGrantEmployeeCount(), avgGrantAmount);

        int count = 0;      //员工账户锁定失败数
        for (String employeeId : idList) {
            List<String> i = Lists.newArrayList();
            i.add(employeeId);
            int lockNum = personAccountExtMapper.lockAccount(i, AccountLockType.Lock.getKey(), PayCenterConstant.PAY_LOCK_REASON);
            PersonAccountFlow personAccountFlow = new PersonAccountFlow();
            AccountFbbFlow accountFbbFlow = new AccountFbbFlow();                      //企业分贝币账户流水
            PersonAccount personAccount = getPersonAccountByEmployeeId(employeeId);
            Date orderCreateTime = new Date();

            if (lockNum <= 0) {
                logger.error("员工账户锁定失败  员工号:{}", employeeId);
                count++;
                //此种情况，认为扣款失败（即已消费）
                personAccountFlow.setAmount(new BigDecimal("0"));            //个人扣款 0
                accountFbbFlow.setAmount(new BigDecimal("0"));              // 企业撤回 0
            } else {
                //锁定成功
                BigDecimal balance = personAccount.getBalance();       //账户余额
                BigDecimal balanceSum = balance.subtract(avgGrantAmount);  //扣款分发量后账户余额
                BigDecimal realReduce = avgGrantAmount;                    // 默认:真实扣款为当时发放额

                int result = balanceSum.compareTo(BigDecimal.valueOf(0)); //扣款后 账户余额是否大于0?  ==1 大于 ,==0 等于, ==-1 小于
                if (result == -1) {
                    realReduce = balance;                               //余额不够 实际扣款额=账户余额
                    noFullAmount.add(employeeId);
                    personAccountFlow.setBalance(new BigDecimal("0"));
                    logger.info("部分扣款  员工号:{}, 扣款金额:{}", employeeId, realReduce);
                } else {
                    fullAmount.add(employeeId);
                    personAccountFlow.setBalance(balanceSum);
                }
                sumReduce = sumReduce.add(realReduce);
                personAccountFlow.setAmount(realReduce.negate()); //金额（元），充值为正，消费为负
                accountFbbFlow.setAmount(realReduce);        // 金额，充值(撤回)为正，消费(发放)为负
            }

            //记录当前锁定账户流水
            personAccountFlow.setId(RandomUtils.bsonId());
            personAccountFlow.setAccountId(personAccount.getId());
            personAccountFlow.setGrantAmount(avgGrantAmount);//发放金额
            personAccountFlow.setBusinessType(PersonPayBusinessType.Exchange.getKey()); //消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回
            personAccountFlow.setOrderId(order_id);
            personAccountFlow.setCompanyId(company_id);
            personAccountFlow.setOperatorId(user_id);
            personAccountFlow.setOperatorName(employee.getName());
            personAccountFlow.setReason(reason);
            personAccountFlow.setCreateTime(orderCreateTime);

            Map<String, Object> commonJson = Maps.newHashMap();
            Map<String, String> companyInfo = Maps.newHashMap();
            companyInfo.put("companyName", employee.getCompany_name());
            companyInfo.put("operatorName", employee.getName());
            companyInfo.put("comment", reason);
            commonJson.put("companyInfo", companyInfo);
            personAccountFlow.setCommonJson(JSON.toJSONString(commonJson));

            personAccountFlowList.add(personAccountFlow);

            //记录分贝币撤回流水
            accountFbbFlow.setId(RandomUtils.bsonId());
            accountFbbFlow.setAccountId(account.getId());
            accountFbbFlow.setGrantAmount(avgGrantAmount);   //发放金额(撤回为-)
            accountFbbFlow.setBalance(account.getFbbBalance().add(sumReduce));      //当前企业分贝币余额
            accountFbbFlow.setBusinessType(BillBusinessType.Fbb_Dispatch.getKey());    //  消费类型：1、分贝币充值，2、分贝币发放，3、分贝币撤回
            accountFbbFlow.setPersonAccountId(personAccount.getId());          //撤回员工账户ID
            accountFbbFlow.setOrderId(order_id);                              //撤回的 发放单ID
            accountFbbFlow.setReason(reason);                                //撤回原因
            accountFbbFlow.setOperatorId(employee.getId());
            accountFbbFlow.setOperatorName(employee.getName());
            accountFbbFlow.setCreateTime(orderCreateTime);
            accountFbbFlowList.add(accountFbbFlow);
        }

        logger.info("目标锁定发放员工总数:{}, 实际锁定总数:{},失败总数:{}", idList.size(), idList.size() - count, count);
        logger.info("全额扣款员工数:{}, 部分扣款员工数:{},此次扣款总额：{}", fullAmount.size(), noFullAmount.size(), sumReduce);
        Integer status = BillStatusType.RECALL_ALL.getKey();              //该单撤回操作 最终结果状态  （一单允许多次撤回操作）
        Integer thisRecallStatus = BillStatusType.RECALL_ALL.getKey();   //此次撤回操作 结果状态
        if (noFullAmount.size() != 0 && fullAmount.size() != 0) {
            status = BillStatusType.RECALL_PART.getKey();
            thisRecallStatus = BillStatusType.RECALL_PART.getKey();
        }

        BigDecimal revokeAmount = billRecord.getRevokeAmount();
        logger.info("该单历史扣款总额：{}", revokeAmount);
        revokeAmount = revokeAmount.add(sumReduce);
        logger.info("该单扣款总额：{}", revokeAmount);
        if (sumReduce.compareTo(BigDecimal.valueOf(0)) == -1 || sumReduce.compareTo(grantAmount) == 1) {        //判断 扣减额异常
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), GlobalResponseCode.EXCEPTION.getMsg());

        }
        BigDecimal thisWillRecallSum = avgGrantAmount.multiply(BigDecimal.valueOf(idList.size()));

        if (sumReduce.compareTo(BigDecimal.valueOf(0)) == 0) {
            thisRecallStatus = BillStatusType.RECALL_FAIL.getKey();                                                                                        //扣款为0 失败

        }
        if (sumReduce.compareTo(BigDecimal.valueOf(0)) == 1 && sumReduce.compareTo(thisWillRecallSum) == -1) {   // 扣款大于0  小于发放额 部分
            thisRecallStatus = BillStatusType.RECALL_PART.getKey();

        }
        if (sumReduce.compareTo(thisWillRecallSum) == 0) {                                                      //相等 全额撤回
            thisRecallStatus = BillStatusType.RECALL_ALL.getKey();
        }


        if (revokeAmount.compareTo(BigDecimal.valueOf(0)) == 0) {
            status = BillStatusType.RECALL_FAIL.getKey();                                                                              //扣款为0 失败

        }
        if (revokeAmount.compareTo(BigDecimal.valueOf(0)) == 1 && revokeAmount.compareTo(grantAmount) == -1) {   // 扣款大于0  小于发放额 部分
            status = BillStatusType.RECALL_PART.getKey();

        }
        if (revokeAmount.compareTo(grantAmount) == 0) {                                                      //相等 全额撤回
            status = BillStatusType.RECALL_ALL.getKey();
        }

        int i = 0;

        //插入企业分贝币账户流水
        i = accountFbbFlowExtMapper.batchInsert(accountFbbFlowList);
        if (i != accountFbbFlowList.size())
            logger.error("插入异常，企业账户流水插入数量:{}， 总数量:{}", i, accountFbbFlowList.size());

        //插入个人账户流水
        i = personAccountFlowExtMapper.batchInsert(personAccountFlowList);
        if (i != personAccountFlowList.size())
            logger.error("插入异常，员工账户流水插入数量:{}， 总数量:{}", i, personAccountFlowList.size());


        //企业账户 -分贝币余额加款并解锁
        int companyEffect = accountExtMapper.updateAccountRecallBalance(sumReduce, company_id, account.getFbbBalance()); //添加此次撤回数量
        if (companyEffect != 1)
            throw new FinhubException(GlobalResponseCode.EXCEPTION.getCode(), GlobalResponseCode.EXCEPTION.getType(), "数据库操作失败");

        // fullAmount noFullAmount
        if (fullAmount.size() != 0) {   //全额扣款
            i = personAccountExtMapper.fullDeducting(avgGrantAmount, fullAmount);
            if (i != fullAmount.size())
                throw new FinhubException(GlobalResponseCode.PERSON_ACCOUNT_ERROR.getCode(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getType(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getMsg());
        }
        if (noFullAmount.size() != 0) {  //部分扣款
            i = personAccountExtMapper.partDeducting(avgGrantAmount, noFullAmount);
            if (i != noFullAmount.size())
                throw new FinhubException(GlobalResponseCode.PERSON_ACCOUNT_ERROR.getCode(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getType(), GlobalResponseCode.PERSON_ACCOUNT_ERROR.getMsg());
        }


        billRecord.setStatus(status);   //状态：0: 发放中 1、发放成功 2、发放失败 3、全部撤回 4、部分撤回  5,撤回失败 // 修改发放单状态
        billRecord.setUpdateTime(new Date());//
        billRecord.setRevokeAmount(revokeAmount); // 全部撤回量  因为可以多洗撤回 因此撤回操作人信息科变更 如需查看之前 可在个人账户流水 或企业分贝币账户流水中查看具体每次操作人信息
        billRecord.setRevokeReason(reason); //
        billRecord.setAccountId(account.getId());
        billRecord.setRevokeOperatorId(user_id);//
        billRecord.setRevokeOperatorName(employee.getName());//
        billRecord.setRevokeOperatorPhone(employee.getPhone_num());//
        i = fbbIssuingBillMapper.updateByPrimaryKeySelective(billRecord);
        if (i <= 0) {
            logger.error("修改发放单状态操作失败,, order_id={}", billRecord.getId());
            throw new FinhubException(GlobalResponseCode.BILL_ORDER_STATUS.getCode(), GlobalResponseCode.BILL_ORDER_STATUS.getType(), GlobalResponseCode.BILL_ORDER_STATUS.getMsg());
        }
        Map<String, Object> data = Maps.newHashMap();
        data.put("orderId", order_id);
        data.put("status", thisRecallStatus);
        data.put("number", sumReduce);
        data.put("targetNumber", thisWillRecallSum);
        data.put("grantNum", billRecord.getFbbAmount());
        data.put("allRecall", revokeAmount);
        return data;
    }
*/

    /**
     * 查询员工账户信息
     * @param employeeId
     */
    PersonAccount getPersonAccountByEmployeeId(String employeeId) {
        PersonAccountExample example1 = new PersonAccountExample();
        example1.createCriteria().andEmployeeIdEqualTo(employeeId);
        List<PersonAccount> personAccountList = personAccountMapper.selectByExample(example1);
        if (CollectionUtils.isEmpty(personAccountList))
            throw new FinhubException(GlobalResponseCode.PERSON_ACCOUNT_NOT_EXIST.getCode(), GlobalResponseCode.PERSON_ACCOUNT_NOT_EXIST.getType(), GlobalResponseCode.PERSON_ACCOUNT_NOT_EXIST.getMsg());
        return personAccountList.get(0);
    }


    /**
     * 获取发放员工ID
     * @param company_id
     * @param type
     * @param paramIdList
     */
    private List<String> getEmployeeList(String company_id, Boolean type, Object paramIdList) throws FinhubException{
        logger.info("获取发放员工 ID,入参 : company_id={}, type ={}，idList={} ", company_id, type, JsonUtils.toJson(paramIdList));

        List<String> idList = Lists.newArrayList();
        if (type) {
            List<EmployeeBaseInfo> employeeBaseInfoList = this.getCompanyEmployeeBycompanyId(company_id);
            for (EmployeeBaseInfo employeeBaseInfo : employeeBaseInfoList) {
                idList.add(employeeBaseInfo.getId());
            }
            logger.info("该企业员工共:{}名,   员工ID列表为 :{} ", employeeBaseInfoList.size(), JsonUtils.toJson(idList));
        } else {
            idList = (List<String>) paramIdList;
            logger.info("传入员工ID为 :{} " , JsonUtils.toJson(idList));
        }
        if (CollectionUtils.isEmpty(idList)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        logger.info("将要给发放的员工ID为:{}", idList);
        return idList;
    }


    /**
     * 获取该企业下所有员工（除去企业停用员工 status!=2
     * @param company_id
     */
    private List<EmployeeBaseInfo> getCompanyEmployeeBycompanyId(String company_id) throws FinhubException{
        logger.info("调用RPC服务, 查询该企业ID下所有员工,入参 : company_id={} " , company_id);
        List<EmployeeBaseInfo> employeeBaseInfoList;
        try {
            employeeBaseInfoList = iBaseEmployeeExtService.queryEmployeeBaseInfo(company_id, null, null, 0, 0,null);
            logger.info("调用RPC服务, 查询该企业ID下所有员工, 共:{}名 ", employeeBaseInfoList.size());
        } catch (Exception e) {
            logger.error("查询企业员工DUBBO 服务出错 ，错误信息为:{} 企业id为: {}", e.getLocalizedMessage(), company_id);
            throw new FinhubException(GlobalResponseCode.DUBBO_ERROR.getCode(), GlobalResponseCode.DUBBO_ERROR.getType(), GlobalResponseCode.DUBBO_ERROR.getMsg());
        }

        return employeeBaseInfoList;
    }


    /**
     * 获取发放对象id  并校验该单发放对象是否已有撤回记录
     * @param billRecord
     * @param company_id
     * @param type
     * @param paramIdList
     * @param order_id
     */
    private List<String> findDispatchEmployeeIdList(AccountFbbIssuingBill billRecord, String company_id, Boolean type, Object paramIdList, String order_id) {
        logger.info("获取发放对象id并校验该单发放对象是否已有撤回记录 入参是:billRecord={}, company_id={}, type={}, paramIdList={},order_id={} ",  JsonUtils.toJson(billRecord), company_id, type,  JsonUtils.toJson(paramIdList), order_id);

        List<String> idList = Lists.newArrayList();
        Map<String, Object> params = Maps.newHashMap();
        params.put("order_id", order_id);
        params.put("company_id", company_id);

        //全部发放
        if (type) {
            List<AccountFbbIssuingBillVo> dispatchEmployeeList = Lists.newArrayList();
            //订单状态为 发放成功--> 直接查询所有员工
            if (billRecord.getStatus() == BillStatusType.DISPATCH_SUCCESS.getKey()) {
                params.put("business_type", PersonPayBusinessType.Recharge.getKey());
                logger.info("查询该发放单所有发放员工,,入参 : params={} ",  JsonUtils.toJson(params));
                dispatchEmployeeList = findAllDispatchEmployee(params);
                logger.info("该发放单发放员工有: {} 个", dispatchEmployeeList.size());
            } else if (billRecord.getStatus() == BillStatusType.RECALL_PART.getKey() || billRecord.getStatus() == BillStatusType.RECALL_FAIL.getKey()) {
                //订单状态非发放成功（部分撤回，撤回失败） --> 查询已撤回员工
                params.put("business_type", PersonPayBusinessType.Exchange.getKey());
                List<AccountFbbIssuingBillVo> recallEmployee = accountFbbIssuingBillExtMapper.selectRecallEmployee(params);
                List<String> recallEmployeeIdList = Lists.newArrayList();
                if (!CollectionUtils.isEmpty(recallEmployee)) {
                    recallEmployee.stream().forEach(employee -> recallEmployeeIdList.add(employee.getId()));
                    logger.info("已有撤回记录的员工ID为 :{} ",  JsonUtils.toJson(recallEmployeeIdList));

                    //查询没有进行过操作记录的发放对象
                    params.remove("business_type");
                    params.put("business_type", PersonPayBusinessType.Recharge.getKey());
                    params.put("employeeIdList", recallEmployeeIdList);
                    logger.info("筛选参数为 :{} ", JSON.toJSONString(params));
                    dispatchEmployeeList = findAllDispatchEmployee(params);
                }
            }
            if (CollectionUtils.isEmpty(dispatchEmployeeList))
                throw new FinhubException(GlobalResponseCode.BILL_ORDER_RECALLED.getCode(), GlobalResponseCode.BILL_ORDER_RECALLED.getType(), GlobalResponseCode.BILL_ORDER_RECALLED.getMsg());
            for (AccountFbbIssuingBillVo employee : dispatchEmployeeList) {
                idList.add(employee.getId());
            }
        } else {
            List<String> recalledName = Lists.newArrayList();
            idList = (List<String>) paramIdList;
            if (billRecord.getStatus() != BillStatusType.DISPATCH_SUCCESS.getKey()) {
                params.put("business_type", PersonPayBusinessType.Exchange.getKey());
                //撤回记录
                List<AccountFbbIssuingBillVo> recallEmployee = accountFbbIssuingBillExtMapper.selectRecallEmployee(params);
                //校验是否有重复撤回员工
                for (AccountFbbIssuingBillVo recallEmployeeInfo : recallEmployee) {
                    for (String id : idList) {
                        if (recallEmployeeInfo.getId().equals(id)) {
                            recalledName.add(recallEmployeeInfo.getName());
                        }
                    }
                }
            }
            if (recalledName.size() > 0)
                throw new FinhubException(GlobalResponseCode.EMPLOYEE_HSA_RECALLED_ERROR.getCode(), GlobalResponseCode.EMPLOYEE_HSA_RECALLED_ERROR.getType(), "所示员工已进行过撤回操作，请重新选择撤回对象->" + recalledName);
        }
        logger.info("可撤回员工ID为 :{} ",  JsonUtils.toJson(idList));
        return idList;
    }


    /**
     * 查询所有企业 分贝币的总发放量和总购买量
     * @return
     */
    public Map<String, Object> getFbbAccount() throws FinhubException {

        Map<String, Object> result = Maps.newHashMap();

        Long buySum = accountFbbIssuingBillExtMapper.selectbuySum()==null?0:accountFbbIssuingBillExtMapper.selectbuySum();

        Long grantSum = accountFbbIssuingBillExtMapper.selectgrantSum()==null?0:accountFbbIssuingBillExtMapper.selectgrantSum();

        Long recallSum = accountFbbIssuingBillExtMapper.selectrecallSum()==null?0:accountFbbIssuingBillExtMapper.selectrecallSum();

        logger.info("查询总购买量:{} ", buySum);
        logger.info("查询总发放量:{} ", grantSum);
        logger.info("查询总撤回量 {} ", recallSum);

        result.put("buySum", buySum);
        result.put("grantSum", grantSum);
        result.put("recallSum", recallSum);
        return result;
    }


    /**
     * 校验企业是可进行撤回操作
     * @param company_id
     * @param order_id
     * @return
     */
    public Boolean checkIsRecall(String company_id, String order_id) throws FinhubException {
        Boolean canDelete = true;
        AccountFbbIssuingBill billRecord = getBill(order_id, BillType.GRANT.getKey());
        logger.info("发放单记录查询 : 结果 {} ", JsonUtils.toJson(billRecord));

        if (billRecord.getStatus() == BillStatusType.RECALL_ALL.getKey()) {

            canDelete = false;

        } else if (billRecord.getStatus() == BillStatusType.RECALL_PART.getKey() || billRecord.getStatus() == BillStatusType.RECALL_FAIL.getKey()) {
            Map<String, Object> params = Maps.newHashMap();
            params.put("order_id", order_id);
            params.put("company_id", company_id);
            params.put("business_type", PersonPayBusinessType.Recharge.getKey());
            int dispatchCount = getDispatchCount(params);   //查询发放数量
            logger.info("该单发放数量:{} :  ", dispatchCount);

            params.remove("business_type");
            params.put("business_type", PersonPayBusinessType.Exchange.getKey());  // '消费类型：1、订单消费支出，2、公司发放，3、退款，4、公司撤回';
            int recallCount = getRecallCount(params);     //查询发放数量
            logger.info("该单已撤回数量:{} :  ", recallCount);
            if (dispatchCount == recallCount) {
                canDelete = false;
            }
        }

        return canDelete;
    }


    /**
     * 查询购买记录
     * @param companyId
     * @param buyer_name
     * @param buyer_phone
     * @param bill_type
     * @param pageIndex
     * @param pageSize
     * @param buyer_time_start
     * @param buyer_time_end
     * @return
     */
    public Map<String, Object> selectFbbIssuingBuyBill(String companyId, String buyer_name, String buyer_phone, int bill_type, Integer pageIndex, Integer pageSize, String buyer_time_start, String buyer_time_end) {
        if (StringUtils.isEmpty(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        Map<String, Object> params = Maps.newHashMap();
        if (!StringUtils.isEmpty(buyer_time_start)) {
            buyer_time_start = buyer_time_start + " 00:00:00";
            validDateIllegal(buyer_time_start);
            Timestamp time_start = Timestamp.valueOf(buyer_time_start);
            params.put("time_start", time_start);
        }
        if (!StringUtils.isEmpty(buyer_time_end)) {
            buyer_time_end = buyer_time_end + " 23:59:59";
            validDateIllegal(buyer_time_end);
            Timestamp time_end = Timestamp.valueOf(buyer_time_end);
            params.put("time_end", time_end);
        }
        params.put("companyId", companyId);
        params.put("operator_name", buyer_name);
        params.put("operator_phone", buyer_phone);
        Integer offset = pageSize * (pageIndex - 1);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("bill_type", bill_type);      // bill_type =1  购买

        logger.info("/fbb/query/buyList 请求开始 入参为  = {}", params);


        Map<String, Object> data = Maps.newHashMap();
        List<Map<String, Object>> results = Lists.newArrayList();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        //获取购买记录
        List<AccountFbbIssuingBill> buyIssuingBillList = selectFbbIssuingBill(params);
        int count = selectCount(params);
        if (CollectionUtils.isNotEmpty(buyIssuingBillList)) {
            buyIssuingBillList.forEach(buyIssuingBill -> {
                Map<String, Object> buyIssuingBillResult = Maps.newHashMap();
                buyIssuingBillResult.put("order_id", buyIssuingBill.getId());
                buyIssuingBillResult.put("name", buyIssuingBill.getOperatorName());
                buyIssuingBillResult.put("phone", buyIssuingBill.getOperatorPhone());
                buyIssuingBillResult.put("remark", buyIssuingBill.getComment());
                buyIssuingBillResult.put("number", buyIssuingBill.getFbbAmount());
                buyIssuingBillResult.put("create_time", sf.format(buyIssuingBill.getCreateTime()));
                results.add(buyIssuingBillResult);
            });
        }
        data.put("results", results);
        data.put("count", count);

        return data;
    }


    /**
     * 分贝币发放记录查询
     * @param companyId
     * @param grant_name
     * @param grant_phone
     * @param status
     * @param pageIndex
     * @param pageSize
     * @param grant_time_start
     * @param grant_time_end
     * @return
     */
    public Map<String, Object> selectFbbIssuingDispatchBill(String companyId, String grant_name, String grant_phone, String grant_time_start, String grant_time_end, Integer status, Integer pageIndex, Integer pageSize, int bill_type) {

        if (StringUtils.isEmpty(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }
        Map<String, Object> params = Maps.newHashMap();
        Map<String, Object> data = Maps.newHashMap();

        if (!StringUtils.isEmpty(grant_time_start)) {
            grant_time_start = grant_time_start + " 00:00:00";
            validDateIllegal(grant_time_start);
            Timestamp time_start = Timestamp.valueOf(grant_time_start);
            params.put("time_start", time_start);
        }
        if (!StringUtils.isEmpty(grant_time_end)) {
            grant_time_end = grant_time_end + " 23:59:59";
            validDateIllegal(grant_time_end);
            Timestamp time_end = Timestamp.valueOf(grant_time_end);
            params.put("time_end", time_end);
        }
        params.put("operator_name", grant_name);
        params.put("operator_phone", grant_phone);
        Integer offset = pageSize * (pageIndex - 1);
        params.put("offset", offset);
        params.put("pageSize", pageSize);
        params.put("bill_type", bill_type);    // bill_type =2  发放
        params.put("status", status);          // 发放状态 -->   1、发放成功 2、发放失败 3、全部撤回 4、部分撤回  (不传时默认全部) 5 撤回失败
        params.put("companyId", companyId);

        logger.info("/fbb/query/dispatch/list 请求开始 入参为  = {}", params);

        //获取员工基本信息
        List<Map<String, Object>> results = Lists.newArrayList();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


        List<AccountFbbIssuingBill> grantIssuingBillList = selectFbbIssuingBill(params);
        int count = selectCount(params);
        if (CollectionUtils.isNotEmpty(grantIssuingBillList)) {
            grantIssuingBillList.forEach(grantIssuingBill -> {
                Map<String, Object> buyIssuingBillResult = Maps.newHashMap();
                boolean is_recall = false;
                if (grantIssuingBill.getStatus() == BillStatusType.RECALL_ALL.getKey() || grantIssuingBill.getStatus() == BillStatusType.RECALL_PART.getKey()  || grantIssuingBill.getStatus() == BillStatusType.RECALL_FAIL.getKey())
                    is_recall = true;   //有过撤回操作， 全部/部分/撤回失败
                buyIssuingBillResult.put("order_id", grantIssuingBill.getId());
                buyIssuingBillResult.put("name", grantIssuingBill.getOperatorName());
                buyIssuingBillResult.put("phone", grantIssuingBill.getOperatorPhone());
                buyIssuingBillResult.put("remark", grantIssuingBill.getComment());
                buyIssuingBillResult.put("status", grantIssuingBill.getStatus());
                buyIssuingBillResult.put("is_recall", is_recall);
                buyIssuingBillResult.put("number", grantIssuingBill.getFbbAmount());
                buyIssuingBillResult.put("create_time", sf.format(grantIssuingBill.getCreateTime()));
                results.add(buyIssuingBillResult);
            });
        }
        data.put("results", results);
        data.put("count", count);

        return data;
    }


    /**
     * 分贝币发放记录导出
     * @param companyId
     * @param grant_name
     * @param grant_phone
     * @param status
     * @param grant_time_start
     * @param grant_time_end
     * @return
     */
    public List<AccountFbbIssuingBill> exportDispatch(String companyId, String grant_name, String grant_phone, String grant_time_start, String grant_time_end, Integer status, Integer bill_type) {

        if (StringUtils.isEmpty(companyId)) {
            throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), GlobalResponseCode.ILLEGAL_ARGUMENT.getMsg());
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("operator_name", grant_name);
        params.put("operator_phone", grant_phone);
        params.put("bill_type", BillType.GRANT.getKey());    // bill_type =2  发放
        params.put("status", status);  // 发放状态 -->   1、发放成功 2、发放失败 3、全部撤回 4、部分撤回  (不传时默认全部)
        params.put("companyId", companyId);
        if (!StringUtils.isEmpty(grant_time_start)) {
            grant_time_start = grant_time_start + " 00:00:00";
            validDateIllegal(grant_time_start);
            Timestamp time_start = Timestamp.valueOf(grant_time_start);
            params.put("time_start", time_start);
        }
        if (!StringUtils.isEmpty(grant_time_end)) {
            grant_time_end = grant_time_end + " 23:59:59";
            validDateIllegal(grant_time_end);
            Timestamp time_end = Timestamp.valueOf(grant_time_end);
            params.put("time_end", time_end);
        }
        List<AccountFbbIssuingBill> buyIssuingBillList = selectFbbIssuingBill(params);
        logger.info("/fbb/dispatch/export 请求开始 入参为  = {}", params);

        return buyIssuingBillList;
    }


    private void validDateIllegal(String  time){
         try {
             Timestamp time_start_stamp = Timestamp.valueOf(time);
         }catch (IllegalArgumentException e){
             logger.info(" time:{}",time);
             throw new FinhubException(GlobalResponseCode.ILLEGAL_ARGUMENT.getCode(), GlobalResponseCode.ILLEGAL_ARGUMENT.getType(), "非法的日期格式");
         }
    }


}
