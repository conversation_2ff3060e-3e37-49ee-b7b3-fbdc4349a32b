package com.fenbeitong.fenbeipay.core.model.dto.account;

import com.fenbeitong.fenbeipay.api.model.dto.BaseDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2022-08-05 4:28 下午
 */
@Data
public class AcctGeneralCashWithDrawalDTO extends BaseDTO {

    private String companyId;

    private String companyMainId;

    private String bankName;

    private String bankAccountNo;

    private String operationAmount;

    private String operationUserId;

    private String operationUserName;

    private String targetBankName;

    private String targetBankAccountNo;

    private String verifyCode;

    private String verifyCodePhoneNum;

    private String operationDescription;

    private Integer operationChannelType;

    private String customerServiceId;

    private String customerServiceName;

    private String bizNo;

    private String smsId;
    
    /**
     * 银行交易流水号 类似连连二次交互时使用
     */
    private String bankTransNo;
}
