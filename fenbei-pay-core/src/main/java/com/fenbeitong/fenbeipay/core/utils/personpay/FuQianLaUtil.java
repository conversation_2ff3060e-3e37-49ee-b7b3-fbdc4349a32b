package com.fenbeitong.fenbeipay.core.utils.personpay;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.constant.paycenter.FuQianLaConstant;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.utils.HttpclientUtils;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 付钱拉工具类
 * Created by mac on 18/1/6.
 */
@Deprecated
public class FuQianLaUtil {

    private static Logger logger = LoggerFactory.getLogger(FuQianLaUtil.class);

    /**
     * 查询支付接口状态
     *
     * @param orderNo
     * @return
     */
    public static String postSingleQuery(String orderNo) {
        JSONObject para = new JSONObject();
        para.put("order_no", orderNo);
        para.put("app_id", FuQianLaConstant.appid);
        para.put("charset", "UTF-8");
        para.put("version", PayConstant.FUQIANLA_VERSION);
        para.put("sign_type", PayConstant.FUQIANLA_RSA);
        Map mapData = para;
        String signInfo = RsaVerifyUtil.getSignByMap(mapData);
        para.put("sign_info", signInfo);
        return HttpclientUtils.postJSONResult(FuQianLaConstant.fuqianla_singquery_url, para);
    }

    /**
     * 付钱拉退款
     *
     * @param orderNo
     * @return
     */
    public static Map refundPayRecord(String orderNo, int amount) {
        Map<String,String> map=new ConcurrentHashMap();
        JSONObject para = new JSONObject();
        para.put("app_id", FuQianLaConstant.appid);
        para.put("charset", "UTF-8");
        para.put("txn_id", orderNo);
        String refundNo=RandomUtils.bsonId();
        para.put("refund_no", refundNo);
        para.put("refund_amount", amount);
        para.put("refund_reason", "退款申请");
        para.put("currency", "CNY");
        para.put("operator_id", "system");
        para.put("version", PayConstant.FUQIANLA_VERSION);
        para.put("sign_type", PayConstant.FUQIANLA_RSA);
        Map mapData = para;
        String signInfo = RsaVerifyUtil.getSignByMap(mapData);
        para.put("sign_info", signInfo);
        logger.info("退款请求参数：" + JsonUtils.toJson(para));
        String repStr=HttpclientUtils.postJSONResult(FuQianLaConstant.fuqianla_refund_url, para);
        map.put(PayConstant.PRFUND_REQUEST,JsonUtils.toJson(para));
        map.put(PayConstant.PRFUND_REPONSE,repStr);
        return map;
    }
    /**
     * 付钱拉退款查询
     *
     * @param txnId
     * @return
     */
    public static Map<String, String> refundRefundRecord(String txnId,String refundNo) {
        Map<String,String> refundMap=new ConcurrentHashMap<>();
        JSONObject para = new JSONObject();
        para.put("app_id", FuQianLaConstant.appid);
        para.put("charset", "UTF-8");
        para.put("txn_id", txnId);
        para.put("refund_no", refundNo);
        para.put("version", PayConstant.FUQIANLA_VERSION);
        para.put("sign_type", PayConstant.FUQIANLA_RSA);
        Map mapData = para;
        String signInfo = RsaVerifyUtil.getSignByMap(mapData);
        para.put("sign_info", signInfo);
        String refundResponse=HttpclientUtils.postJSONResult(FuQianLaConstant.fuqianla_refund_query_url, para);
        logger.info("退款查询请求参数：" + JsonUtils.toJson(para));
        refundMap.put(PayConstant.PRFUND_REQUEST,JsonUtils.toJson(para));
        refundMap.put(PayConstant.PRFUND_REPONSE,refundResponse);
        return refundMap;
    }



}
