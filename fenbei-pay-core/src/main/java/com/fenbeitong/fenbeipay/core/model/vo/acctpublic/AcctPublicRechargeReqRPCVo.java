package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

/**
 * @Title: AcctPublicRechargeReqRPCVo
 * @ProjectName fenbei-pay
 * @Description: TODO
 * @author: wh
 * @date 2020/5/18 10:50
 */

import com.fenbeitong.finhub.common.validation.constraints.BigDecimalNotPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 充值金额到充值模式对公账户请求对象
 * @ClassName: AccountPublicRechargeReqRPCDTO
 * @Author: wh
 * @CreateDate: 2020/5/18 7:41 PM
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AcctPublicRechargeReqRPCVo extends AcctPublicBaseReqVo {

    /**
     * 银行帐号
     */
    @NotBlank
    private String bankAccountNo;

    /**
     * 操作金额 单位：分
     */
    @NotNull
    @Max(trillion)
    @BigDecimalNotPoint
    private BigDecimal operationAmount;

    /**
     * 银行交易时间
     */
    @NotNull
    private Date bizTime;

    /**
     * 付款方:公司银行号
     */
    @NotBlank
    private String buyBankNo;

    /* *//**
     * 付款方:银行账户名称
     *//*
    private String buyBankAcctName;*/

    /**
     * 付款方:银行开户行名
     */
    @NotBlank
    private String buyBankName;
    /*   *//**
     * 付款方:银行联行号
     *//*
    private String buyBankNo;*/

    /**
     * 操作渠道：
     *@see OperationChannelType
     */
    @NotNull
    private Integer operationChannel;
    /**
     * 交易说明
     */
    private String operationDescription;

    /**
     * 操作人Id
     */
    private String operationUserId;

    /**
     * 操作人姓名
     */
    private String operationUserName;

}
