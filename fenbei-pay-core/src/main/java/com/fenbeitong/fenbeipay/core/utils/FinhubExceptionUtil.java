package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;

public class FinhubExceptionUtil {

    public static FinhubException exceptionFrom (GlobalResponseCode globalResponseCode) {
        return new FinhubException(globalResponseCode.getCode(), globalResponseCode.getType(), globalResponseCode.getMsg());
    }

    public static FinhubException exceptionFrom (GlobalResponseCode globalResponseCode,String remark) {
        return new FinhubException(globalResponseCode.getCode(), globalResponseCode.getType(), globalResponseCode.getMsg() + remark);
    }
}
