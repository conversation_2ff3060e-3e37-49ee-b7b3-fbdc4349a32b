package com.fenbeitong.fenbeipay.core.service.kafka;

import com.fenbeitong.fenbeipay.core.enums.account.AccountOptTypeEnum;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.voucher.KafkaCompanyAccountMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.google.common.base.Strings;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class AccountInfoUpdateKafka {

    private static final String ELECTRONIC = "electronic";
    @Autowired
    private IKafkaProducerPublisher iKafkaProducerPublisher;

    @Autowired
    private DingDingMsgService dingDingMsgService;


    public void delayedSendAccountInfoUpdateMsg(String companyId, String accountId, String businessType, Integer optType) {
        FinhubLogger.info("【delayedSendAccountInfoUpdateMsg】invoking ...");
        try {
            Thread.sleep(200L);
        } catch (InterruptedException e) {
            FinhubLogger.error(e.getMessage(), e);
        }
        sendAccountInfoUpdateMsg(companyId, accountId, businessType, optType);
    }

    public void sendAccountInfoUpdateMsg(String companyId, String accountId, String businessType, Integer optType) {
        FinhubLogger.info("【sendAccountInfoUpdateMsg】req companyId = {} accountId = {} optType = {}", companyId, accountId, optType);
        if (Strings.isNullOrEmpty(companyId) || Strings.isNullOrEmpty(accountId) ||
                Objects.isNull(optType) || Objects.isNull(AccountOptTypeEnum.getEnum(optType))) {
            return;
        }
        if (Strings.isNullOrEmpty(businessType)) {
            businessType = ELECTRONIC;
        }
        try {
            KafkaCompanyAccountMsg companyAccountMsg = new KafkaCompanyAccountMsg();
            companyAccountMsg.setAccountId(accountId);
            companyAccountMsg.setBusinessType(businessType);
            companyAccountMsg.setOptType(optType);
            companyAccountMsg.setCompanyId(companyId);
            FinhubLogger.info("【账户信息变动Kafka消息】msg = {}", JsonUtils.toJson(companyAccountMsg));
            iKafkaProducerPublisher.publish(companyAccountMsg);
        } catch (Exception e) {
            StringBuilder sb = new StringBuilder();
            sb.append("【账户信息变动发送Kafka消息异常】").append("companyId=").append(companyId).append(",")
                    .append("accountId=").append(accountId);
            dingDingMsgService.sendMsg(sb.toString());
            FinhubLogger.error("【账户信息变动Kafka消息】异常 errorMsg = {} ", sb.toString(), e);
        }
    }
}
