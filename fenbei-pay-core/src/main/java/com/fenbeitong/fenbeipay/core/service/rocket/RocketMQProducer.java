package com.fenbeitong.fenbeipay.core.service.rocket;


import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;

/**
 * @Auther: SPL
 * @Date: 2019-07-09 13:57
 * @Description: 初始化生产者
 */

public class RocketMQProducer {

    private DefaultMQProducer defaultMQProducer;
    private String producerGroup;
    private String namesrvAddr;

    private int sendTimeOut;
    private int retryTimes;
    /**
     * Spring bean init-method 初始化
     */
    public void init() throws MQClientException {
        // 参数信息
        System.out.println("DefaultMQProducer initialize!");
        System.out.println(producerGroup);
        System.out.println(namesrvAddr);

        // 初始化
        defaultMQProducer = new DefaultMQProducer(producerGroup);
        defaultMQProducer.setNamesrvAddr(namesrvAddr);
        defaultMQProducer.setInstanceName(String.valueOf(System
                .currentTimeMillis()));
        defaultMQProducer.setSendMsgTimeout(sendTimeOut);
        defaultMQProducer.setRetryTimesWhenSendFailed(retryTimes);
        defaultMQProducer.start();
        System.out.println("DefaultMQProudcer start success!");
    }

    /**
     * Spring bean destroy-method
     */
    public void destroy() {
        defaultMQProducer.shutdown();
    }

    public DefaultMQProducer getDefaultMQProducer() {
        return defaultMQProducer;
    }

    // ---------------setter -----------------

    public void setProducerGroup(String producerGroup) {
        this.producerGroup = producerGroup;
    }

    public void setNamesrvAddr(String namesrvAddr) {
        this.namesrvAddr = namesrvAddr;
    }

    public int getSendTimeOut() {
        return sendTimeOut;
    }

    public void setSendTimeOut(int sendTimeOut) {
        this.sendTimeOut = sendTimeOut;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }

}


