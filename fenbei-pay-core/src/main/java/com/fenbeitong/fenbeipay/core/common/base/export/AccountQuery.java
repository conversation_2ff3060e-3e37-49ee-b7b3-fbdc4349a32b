package com.fenbeitong.fenbeipay.core.common.base.export;

import com.fenbeitong.fenbeipay.core.common.base.PageBean;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: java类作用描述
 * @ClassName: AccountQuery
 * @Author: zhangga
 * @CreateDate: 2019/4/18 3:44 PM
 * @UpdateUser:
 * @UpdateDate: 2019/4/18 3:44 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class AccountQuery extends PageBean {
    /**
     * 公司Id
     **/
    @NotBlank
    private String companyId;

    //主账户id
    private String accountGeneralId;

    //子账户id
    private String accountId;
    /**
     * 子账户类型
     **/
    private Integer accountSubType;

    /**
     * 交易类型 ：1-开户;2-充值;3-提现;4-消费;51-转至商户账户;52-转至个人账户;53-转至企业账户；6-冻结;7-解冻
     */
    private Integer operationType;

    private String accountFlowId;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 创建时间
     */
    @DateTimeFormat
    private Date startTime;
    @DateTimeFormat
    private Date endTime;

    private Integer accountModel;

}
