package com.fenbeitong.fenbeipay.core.common.base;

import com.fenbeitong.fenbeipay.core.common.base.export.BizResultDto;
import com.fenbeitong.fenbeipay.core.common.base.export.OssDataDto;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Maps;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件读取，上传
 * Created by cly on 2020/4/1.
 */
@Component
public class FileStreamService {

    private static final String PIC_GET = "GET";
    private static final String CONNECT_TIME_OUT = "sun.net.client.defaultConnectTimeout";
    private static final String DEFAULT_READ_TIMEOUT = "sun.net.client.defaultReadTimeout";
    private static final String REQUEST_PROPERTY = "application/x-www-form-urlencoded";

    @Value("${exception.remind.profile}")
    protected String currentEnvironment;
    @Value("${host.harmony}")
    protected String gatewayHarmonyUrl;

    @Value("${oss.upload.tempDir}")
    protected String ossUploadTempDir;
    @Autowired
    private DingDingMsgService dingDingMsgService;


    /**
     * 读取url获取stream
     * @param url
     * @param operateName 业务操作名称（如：对公支付下载银行电子回单）
     * @param uniqueId
     * @return
     */
    public InputStream getInputStream(String url, String operateName, String uniqueId) {
        InputStream is = null;
        try {
            URL urlGet = new URL(url);
            HttpURLConnection http = (HttpURLConnection) urlGet
                    .openConnection();
            // 必须是get方式请求
            http.setRequestMethod(PIC_GET);
            http.setRequestProperty("Content-Type", REQUEST_PROPERTY);
            http.setDoOutput(true);
            http.setDoInput(true);
            // 连接超时30秒
            System.setProperty(CONNECT_TIME_OUT, "30000");
            // 读取超时30秒
            System.setProperty(DEFAULT_READ_TIMEOUT, "30000");
            http.connect();
            // 获取文件转化为byte流
            is = http.getInputStream();

        } catch (Exception e) {
            String msgError = operateName + ",下载异常，订单号：" + uniqueId;
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.info(operateName+",下载异常，订单号", JsonUtils.toJson(uniqueId), e);
        }
        return is;

    }

    /**
     * 上传文件到oos
     * @param inputStream
     * @param userId
     * @param orderId
     * @return
     */
    public  String convertToOss(InputStream inputStream, String userId, String orderId,String fileName) {
        try {
            Map<String, Object> paramMap = Maps.newLinkedHashMap();
            Map<String, String> fileNameMap = Maps.newLinkedHashMap();
            paramMap.put("busi_code", "third_bill");
            paramMap.put("user_id", userId);
            paramMap.put("is_save_name", 1);
            paramMap.put("file1", inputStream);
            fileNameMap.put("file1", fileName);
            String result = HttpClientUtils.postMultipartForm(gatewayHarmonyUrl + "/harmony/upload/oss", paramMap,
                    null, fileNameMap);
            FinhubLogger.info("接口调用结束，返回结果为{}", result);
            if (result == null){
                return null;
            }
            BizResultDto bizResultDto = JsonUtils.toObj(result, BizResultDto.class);
            if (bizResultDto.getCode() == 0 && bizResultDto.getData().size() > 0) {
                OssDataDto dataDto = bizResultDto.getData().get(0);
                return dataDto.getUrl();
            }
            return null;
        } catch (Exception e) {
            String msgError = "third_bill【同步图片到OOS】异常" + orderId;
            dingDingMsgService.sendMsg(msgError);
            FinhubLogger.info("【同步图片到OOS】异常{},{}", JsonUtils.toJson(orderId), e);
            throw new FinhubException(GlobalResponseCode.PUBLIC_ORDER_PIC_ERROR.getCode(),
                    GlobalResponseCode.PUBLIC_ORDER_PIC_ERROR.getType(), GlobalResponseCode.PUBLIC_ORDER_PIC_ERROR.getMsg());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * 压缩成zip文件并删除
     * @param srcFiles
     * @return
     * @throws RuntimeException
     */
    public FileInputStream toZip(List<File> srcFiles) throws RuntimeException {
        ZipOutputStream zos = null;
        String tempFileName=DateUtils.format(new Date(), "yyyyMMddHHmmssSSS")+"_"+(int)(Math.random()*10000);
        try {
            File file = new File(ossUploadTempDir+ tempFileName+".zip");
            if(!file.exists()) {
                file.createNewFile();
            }
            FileOutputStream out = new FileOutputStream(file);
            zos = new ZipOutputStream(out);
            for (File srcFile : srcFiles) {
                byte[] buf = new byte[1024 * 10];
                zos.putNextEntry(new ZipEntry(srcFile.getName()));
                int len;
                FileInputStream in = new FileInputStream(srcFile);
                while ((len = in.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
                zos.closeEntry();
                in.close();
            }

            return new FileInputStream(new File(ossUploadTempDir + tempFileName + ".zip"));
        } catch (Exception e) {
            throw new RuntimeException("zip error from ZipUtils", e);
        } finally {
            File updateFile = new File(ossUploadTempDir+ tempFileName+".zip");
            if(updateFile.exists()){
                updateFile.delete();
            }
            if(CollectionUtils.isNotEmpty(srcFiles)) {
                for (File srcFile : srcFiles) {
                    srcFile.delete();
                }
            }
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
