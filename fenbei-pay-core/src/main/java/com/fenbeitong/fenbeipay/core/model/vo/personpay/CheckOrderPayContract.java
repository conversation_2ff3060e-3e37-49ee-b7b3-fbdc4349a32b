package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 检查订单实际支付金额
 * Created by mac on 17/12/29.
 */
public class CheckOrderPayContract implements Serializable {
    //是否需要个人支付
    private Boolean need_personal;
    //是否已支付
    private Boolean is_personal_paied;
    //需个人支付金额
    private BigDecimal personal_pay_price;
    private String order_id;
    //企业应支付金额
    private BigDecimal total_price;
    //订单总金额
    private BigDecimal total_pay_price;
    //企业实际支付金额
    private BigDecimal company_pay_price;
    //优惠券金额
    private BigDecimal coupon_amount;
    //1：因公 2：因私
    private Integer order_type;
    //超规金额
    private BigDecimal limit_price;
    //超时状态
    private Boolean is_over_time;
    //错误数据
    private errorData error_data;
    public static class errorData {
        private int error_code;
        private String error_msg;

        public int getError_code() {
            return error_code;
        }

        public void setError_code(int error_code) {
            this.error_code = error_code;
        }

        public String getError_msg() {
            return error_msg;
        }

        public void setError_msg(String error_msg) {
            this.error_msg = error_msg;
        }
    }
    public Boolean getIs_over_time() {
        return is_over_time;
    }

    public void setIs_over_time(Boolean is_over_time) {
        this.is_over_time = is_over_time;
    }

    public errorData getError_data() {
        return error_data;
    }

    public void setError_data(errorData error_data) {
        this.error_data = error_data;
    }

    public BigDecimal getLimit_price() {
        return limit_price;
    }

    public void setLimit_price(BigDecimal limit_price) {
        this.limit_price = limit_price;
    }

    public Boolean getNeed_personal() {
        return need_personal;
    }

    public void setNeed_personal(Boolean need_personal) {
        this.need_personal = need_personal;
    }

    public Boolean getIs_personal_paied() {
        return is_personal_paied;
    }

    public void setIs_personal_paied(Boolean is_personal_paied) {
        this.is_personal_paied = is_personal_paied;
    }

    public BigDecimal getPersonal_pay_price() {
        return personal_pay_price;
    }

    public void setPersonal_pay_price(BigDecimal personal_pay_price) {
        this.personal_pay_price = personal_pay_price;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public BigDecimal getTotal_price() {
        return total_price;
    }

    public void setTotal_price(BigDecimal total_price) {
        this.total_price = total_price;
    }

    public BigDecimal getTotal_pay_price() {
        return total_pay_price;
    }

    public void setTotal_pay_price(BigDecimal total_pay_price) {
        this.total_pay_price = total_pay_price;
    }

    public BigDecimal getCompany_pay_price() {
        return company_pay_price;
    }

    public void setCompany_pay_price(BigDecimal company_pay_price) {
        this.company_pay_price = company_pay_price;
    }

    public BigDecimal getCoupon_amount() {
        return coupon_amount;
    }

    public void setCoupon_amount(BigDecimal coupon_amount) {
        this.coupon_amount = coupon_amount;
    }

    public Integer getOrder_type() {
        return order_type;
    }

    public void setOrder_type(Integer order_type) {
        this.order_type = order_type;
    }
}
