package com.fenbeitong.fenbeipay.core.aspect;

import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctCommonBaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.gateway.req.AcctComGwByAcctTypeReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierCreatePayTradeRPCVo;
import com.fenbeitong.fenbeipay.api.service.gateway.IAcctCompanyGatewayService;
import com.fenbeitong.fenbeipay.core.annotation.LimitRequest;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.common.LimitRequestEnum;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.ObjUtils;
import net.jodah.expiringmap.ExpirationPolicy;
import net.jodah.expiringmap.ExpiringMap;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import static com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode.GW_APP_PAY_NOT_EXIST;

/**
 * LimitRequestAspect
 *
 * <AUTHOR>
 * @date 2023-04-26 20:23
 */
@Aspect
@Component
public class LimitRequestAspect {

    @Autowired
    private IAcctCompanyGatewayService iAcctCompanyGatewayService;

    private static ConcurrentHashMap<String, ExpiringMap<String, Integer>> book = new ConcurrentHashMap<>();

    private static volatile Map<String, Semaphore> limitPool = ExpiringMap.builder().expiration(30, TimeUnit.MINUTES).build();

    // 定义切点
    // 让所有有@LimitRequest注解的方法都执行切面方法
    @Pointcut("@annotation(limitRequest)")
    public void pointcut(LimitRequest limitRequest) {}

    @Around("pointcut(limitRequest)")
    public Object doAround(ProceedingJoinPoint pjp, LimitRequest limitRequest) throws Throwable {
        LimitRequestEnum value = limitRequest.value();
        /**
         * 创建支付交易流水+支付完成+返回支付结果+无管控预算
         * 使用场景：回填单+审批单，只能有商务账户(因公)扣款
         * 创建支付流水号，并且支付后，直接返回支付结果，没有支付回掉，没有APP上用户点击"立即支付"
         * 中信+1小时1000次
         */
        if (LimitRequestEnum.isCreateAndPayOrderTrade(value)) {
            // 获取参数
            CashierCreatePayTradeRPCVo param = (CashierCreatePayTradeRPCVo) pjp.getArgs()[0];
            AcctCommonBaseDTO actGwDto = checkGateway(param);

            boolean ok = shouldProceed(param.getCompanyId());
            if (!ok) {
            	FinhubLogger.warn("【接口限流】企业->{}请求被限流", param.getCompanyId());
            	throw new FinhubException(GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getCode(), GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getType(), GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getMsg());
            }

            try {
            	// 判断中信银行
                if (!BankNameEnum.isCitic(actGwDto.getBankName())) {
                    return pjp.proceed();
                }

                checkLimit(value, pjp);
                return pjp.proceed();
			} finally {
				release(param.getCompanyId());
			}
        } else {
            return pjp.proceed();
        }
    }

    private boolean shouldProceed(String companyId) {
    	Semaphore semaphore = null;
    	if (limitPool.containsKey(companyId)) {
    		semaphore = limitPool.get(companyId);
    	} else {
    		synchronized (limitPool) {
    			Semaphore tmp = new Semaphore(1);
    			semaphore = limitPool.putIfAbsent(companyId, tmp);
    			if (Objects.isNull(semaphore)) {
    				semaphore = tmp;
    			}
			}
    	}
    	try {
			return semaphore.tryAcquire(10, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			return false;
		}
    }

    private void release(String companyId) {
    	try {
			Semaphore semaphore = limitPool.get(companyId);
			if (Objects.nonNull(semaphore)) {
				semaphore.release();
			}
		} catch (Exception e) {}
    }

    private AcctCommonBaseDTO checkGateway(CashierCreatePayTradeRPCVo param) {
    	//获取网关信息
        AcctComGwByAcctTypeReqDTO comGwReqDTO = new AcctComGwByAcctTypeReqDTO(param.getCompanyId(), FundAccountSubType.BUSINESS_ACCOUNT.getKey());
        AcctCommonBaseDTO actGwDto = iAcctCompanyGatewayService.findActCommonByAcctType(comGwReqDTO);
        if(ObjUtils.isEmpty(actGwDto)){
            throw new FinhubException(GW_APP_PAY_NOT_EXIST.getCode(), GW_APP_PAY_NOT_EXIST.getType(), GW_APP_PAY_NOT_EXIST.getMsg());
        }
        
        return actGwDto;
    }

    private void checkLimit(LimitRequestEnum value, ProceedingJoinPoint pjp) {
    	// 自定义类型
        String type = value.getType();
        // 限制次数
        int count = value.getCount();
        // 单位时间
        long time = value.getTime();
        ExpiringMap<String, Integer> uc = book.getOrDefault(type, ExpiringMap.builder().variableExpiration().build());
        Integer requestCount = uc.getOrDefault(type, 0);
        // 超过次数，抛出异常
        if (requestCount >= count) {
            FinhubLogger.error("公共付款通道接口调用次数单位时间内已达到上限，req={}", JsonUtils.toJson(pjp.getArgs()[0]));
            throw new FinhubException(GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getCode(), GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getType(), GlobalResponseCode.CASHIER_LIMIT_REQUEST_ERROR.getMsg());
        } else if (requestCount == 0){
            // 第一次请求时，设置有效时间
            uc.put(type, requestCount + 1, ExpirationPolicy.CREATED, time, TimeUnit.MILLISECONDS);
        } else {
            // 未超过次数，记录加一
            uc.put(type, requestCount + 1);
        }
        book.put(type, uc);
    }
}