package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * @Author: liyi
 * @Date: 2022/3/30 下午4:01
 */
@Data
@ToString(callSuper = true)
public class CashierChangePriceTradeReqVo extends CashierBasePersonVo {
    private static final long serialVersionUID = -444164568816533886L;

    /**
     * 场景订单ID
     */
    @NotBlank
    private String fbOrderId;

    /**
     * 交易流水号，多次交易时必传
     */
    private String fbTradeId;

    /**
     * 请求单号，用于幂等控制
     */
    @NotBlank
    private String fbRequestNo;

    /**
     * 改价后的订单总金额
     */
    private BigDecimal totalPayChangePrice = BigDecimal.ZERO;

    /**
     * 改价后的企业应支付金额
     * 目前不支持企业支付金额的改价
     */
    private BigDecimal publicPayChangePrice = BigDecimal.ZERO;

    /**
     * 改价后的个人支付金额
     */
    private BigDecimal personalPayChangePrice = BigDecimal.ZERO;


}
