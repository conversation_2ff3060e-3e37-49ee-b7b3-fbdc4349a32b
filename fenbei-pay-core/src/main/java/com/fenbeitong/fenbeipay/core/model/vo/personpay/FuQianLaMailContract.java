package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by mac on 18/2/1.
 */
@Deprecated
public class FuQianLaMailContract implements Serializable {

    private String userName;
    private String phoneNum;
    private String companyName;
    private BigDecimal amount;
    private BigDecimal actureAmount;
    private String orderNo;
    private String fbOrderNo;
    private String completeTime;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getActureAmount() {
        return actureAmount;
    }

    public void setActureAmount(BigDecimal actureAmount) {
        this.actureAmount = actureAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFbOrderNo() {
        return fbOrderNo;
    }

    public void setFbOrderNo(String fbOrderNo) {
        this.fbOrderNo = fbOrderNo;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }
}
