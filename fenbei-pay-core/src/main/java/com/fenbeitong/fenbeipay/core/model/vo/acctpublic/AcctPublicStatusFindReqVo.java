package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.model.dto.BaseDTO;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Title: BankCardBaseReqDTO
 * @ProjectName fenbei-pay
 * @Description: 卡的基本信息
 * @author: wh
 * @date 2019/9/20 10:39
 */
@Data
public class AcctPublicStatusFindReqVo extends AcctPublicFindReqVo {

    /**
     * 虚拟卡：银行账户名称
     */
    @NotNull
    private List<Integer> accountPublicStatus;

    public void checkAcctPublicStatusReq(){
        if (CollectionUtils.isEmpty(accountPublicStatus)||ObjUtils.isBlank(this.companyId)){
            throw new ValidateException("参数错误，公司不可以为空！");
        }
    }
}
