package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.api.constant.enums.cashier.CashierRefundType;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.CashierRefundTradeBaseRPCVo;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Date;

/**
 * REFUND_DEFAULT(1, "按照默认顺序，进行退款三方-币-券(默认)"),
 * REFUND_ASSIGN(2, "指定某种支付能力具体退款金额，进行退款"),
 * 以上两种退款方式都会用到此VO
 */
@Data
@ToString
@NoArgsConstructor
@Accessors(chain = true)
public class CashierRefundTrade4ReimbursementReqVo extends CashierRefundTradeBaseRPCVo {

    /**
     * 指定要求三方退款金额
     */
    @Min(0)
    private BigDecimal personalThirdRefundPrice;
    /**
     * 指定要求分贝币退款金额
     */
    @Min(0)
    private BigDecimal personFbbRefundPrice;
    /**
     * 指定要求分贝券退款金额
     */
    @Min(0)
    private BigDecimal personVouchersRefundPrice;

    @Min(0)
    private BigDecimal companyRefundAmount;

    @Min(0)
    private BigDecimal redcouponRefundAmount;
    /**
     * @see CashierRefundType
     * 退款也分类型 CANCEL(1, "取消"),
     * REFUND(2, "退款");
     */
    private Integer cashierRefundType;

    @Builder
    public CashierRefundTrade4ReimbursementReqVo(String employeeId, String companyId, String fbOrderId, Integer fbApplyBind, Integer checkStatus, Integer categoryType, Date createTime, String fbTradeId, String refundOrderId, String cashierTxnId, String bankTransNo, String bankAccountNo, String bankName, Integer cashierRefundWay, Integer cashierPublicRefundWay, BigDecimal totalRefundAmount, BigDecimal personalRefundAmount, BigDecimal publicRefundAmount, String bizCallbackUrl, String refundReason, String remark, BigDecimal personalThirdRefundPrice, BigDecimal personFbbRefundPrice, BigDecimal personVouchersRefundPrice, BigDecimal companyRefundAmount, BigDecimal redcouponRefundAmount, Integer cashierRefundType) {
        super(employeeId, companyId, fbOrderId,fbApplyBind,checkStatus,categoryType,createTime, fbTradeId, refundOrderId, cashierTxnId, bankTransNo, bankAccountNo, bankName, cashierRefundWay, cashierPublicRefundWay, totalRefundAmount, personalRefundAmount, publicRefundAmount, bizCallbackUrl, refundReason, remark);
        this.personalThirdRefundPrice = personalThirdRefundPrice;
        this.personFbbRefundPrice = personFbbRefundPrice;
        this.personVouchersRefundPrice = personVouchersRefundPrice;
        this.companyRefundAmount = companyRefundAmount;
        this.redcouponRefundAmount = redcouponRefundAmount;
        this.cashierRefundType = cashierRefundType;
    }



    /**
     * 退款是取消类型
     */
    public Boolean isCancel() {
        if (ObjUtils.isEmpty(cashierRefundType)) {
            return false;
        }
        return CashierRefundType.isCancel(cashierRefundType);
    }

    public void checkReq() {
        if (!isCancel() || StringUtils.isBlank(refundOrderId) || ObjUtils.isBlank(bankAccountNo)  || ObjUtils.isBlank(bankName)) {
            throw new FinPayException(GlobalResponseCode.ILLEGAL_ARGUMENT);
        }
    }
}
