package com.fenbeitong.fenbeipay.core.service.kafka;

import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaBankCardOpenMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.CompletableFuture;

/**
 * Created by mac on 18/1/31.
 */
@Service
public class KafkaService {

    @Autowired
    protected IKafkaProducerPublisher ikafkaProducerPublisher;

    /**
     * 异步发送kafka消息
     * @param msg
     */
    public void asyncSendKafkaMsg4CardOpen(KafkaBankCardOpenMsg msg) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交完毕时，触发
                CompletableFuture.runAsync(() -> {
                    try {
                        ikafkaProducerPublisher.publish(msg);
                    } catch (Exception e) {
                        FinhubLogger.error("【发送kafka消息异常】msg:{}", JSONObject.toJSONString(msg), e);
                    }
                });
            }
        });
    }

}
