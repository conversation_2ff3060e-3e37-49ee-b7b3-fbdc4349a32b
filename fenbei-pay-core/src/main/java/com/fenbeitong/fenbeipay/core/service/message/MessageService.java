package com.fenbeitong.fenbeipay.core.service.message;


import com.fenbeitong.eventbus.event.common.IEvent;
import com.fenbeitong.fenbeipay.core.common.hyperloop.dto.PushAlertDto;
import com.fenbeitong.fenbeipay.core.service.message.dto.CommonPushDto;
import com.fenbeitong.fenbeipay.core.utils.notice.SmsContract;

import java.io.IOException;

public interface MessageService<T> {

    /**
     * 推送到友盟push服务
     * @param dto
     * @throws IOException
     */
    void pushAlertMsg(PushAlertDto dto) throws IOException;

    /**
     * 推送Event,到kafka
     * @param iEvent
     * @throws IOException
     */
    void pushEventMsg(IEvent iEvent);

    /**
     * 发送手机短信
     * @param msgBody
     * @throws IOException
     */
    void pushSMS(SmsContract msgBody) throws IOException;


    boolean pushMsgAll(T entity, CommonPushDto pushDTO);
}
