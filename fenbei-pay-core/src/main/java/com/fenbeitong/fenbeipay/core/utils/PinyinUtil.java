package com.fenbeitong.fenbeipay.core.utils;

import com.github.stuxuhai.jpinyin.PinyinFormat;
import com.github.stuxuhai.jpinyin.PinyinHelper;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/29.
 */
public class PinyinUtil {

    public static String convertChineseToPinyin(String chineseString){
        StringBuilder sb = new StringBuilder();
        for (char ch : chineseString.toCharArray()) {
            if (Character.toString(ch).matches("[\\u4E00-\\u9FA5]+")) {
                sb.append(PinyinHelper.convertToPinyinArray(ch, PinyinFormat.WITHOUT_TONE)[0]).append(" ");
            } else {
                sb.append(Character.toChars(ch));
            }
        }
        return sb.toString().trim().replaceAll("[\\s]{2,}", " ");
    }

}
