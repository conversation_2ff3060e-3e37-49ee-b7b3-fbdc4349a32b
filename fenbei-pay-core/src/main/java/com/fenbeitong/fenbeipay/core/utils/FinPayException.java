package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.core.enums.common.BusinessResponseCode;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;

/**
 * 为了减少，FinhubException(Integer code, Integer type, String msg) 在逻辑中太长代码，封装此异常，
 * 接受GlobalResponseCode的枚举
 */
@SuppressWarnings("serial")
public class FinPayException extends FinhubException {

    /** 日志优化，true时日志降级, error降为warn **/
    boolean ignoreReport = false;

    public FinPayException(GlobalResponseCode globalResponseCode) {
        super(globalResponseCode.getCode(),globalResponseCode.getType(),globalResponseCode.getMsg(),globalResponseCode.getTitle());
    }

    public FinPayException(GlobalResponseCode globalResponseCode, boolean ignoreReport) {
        super(globalResponseCode.getCode(),globalResponseCode.getType(),globalResponseCode.getMsg(),globalResponseCode.getTitle());
        this.ignoreReport = ignoreReport;
    }

    public FinPayException(BusinessResponseCode globalResponseCode) {
        super(globalResponseCode.getCode(),globalResponseCode.getType(),globalResponseCode.getMsg(),globalResponseCode.getTitle());
    }

    public boolean ignoreReport() {
        return ignoreReport;
    }
}