package com.fenbeitong.fenbeipay.core.utils.personpay;


import com.fenbeitong.fenbeipay.core.constant.personpay.FuQianLaConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

public class RsaVerifyUtil {

    private static Logger logger = LoggerFactory.getLogger(RsaVerifyUtil.class);

    private static final int ENCRPT_MAX_LEN = 117;
    private static final int DECRPT_MAX_LEN = 128;


    public static String getSignByMap(Map<String, ?> objMap) {
        String sha1Data = null;
        try {
            // 得到待签名数据
            Map<String, ?> filterMap = paraFilter(objMap);
            // 得到待签名数据
            String linkStr = createLinkString(filterMap);
            sha1Data = privateSignToHexString(linkStr, FuQianLaConstant.privateKey);
        } catch (Exception e) {
            logger.error("RSA获取签名数据异常：" + e.getLocalizedMessage());
        }
        return sha1Data;
    }

    public static String getSignByStr(String linkStr) {
        String sha1Data = null;
        try {
            // 得到待签名数据
            sha1Data = privateSignToHexString(linkStr, FuQianLaConstant.privateKey);
        } catch (Exception e) {
            logger.error("RSA获取签名数据异常：" + e.getLocalizedMessage());
        }
        return sha1Data;
    }


    /**
     * 私钥签名
     *
     * @param content    原文
     * @param privateKey Base64私钥
     * @return HexString
     * @throws Exception
     */
    public static String privateSignToHexString(String content, String privateKey) throws Exception {
        byte[] eData = new byte[0];
        try {
            byte[] srcData = SHA1.getDigestOfBytes(content.getBytes("UTF-8"));
            logger.info("SHA1:" + DataConvert.ByteArraytoHexString(srcData));
            eData = privateEncrypt(srcData, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return DataConvert.ByteArraytoHexString(eData);
    }

    public static byte[] privateEncrypt(byte[] content, String privateKey) throws Exception {
        PrivateKey priKey = getPrivateKey(privateKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, priKey);
        ByteArrayOutputStream out = doFinal(cipher, content, ENCRPT_MAX_LEN);
        return out.toByteArray();
    }

    /**
     * 验证签名数据
     *
     * @param objMap
     * @return
     */
    public static boolean verify(Map<String, ?> objMap) {
        boolean flag = false;
        try {
            String hexSign = String.valueOf(objMap.get("sign_info"));
            // 得到待签名数据
            Map<String, ?> filterMap = paraFilter(objMap);
            // 得到待签名数据
            String linkStr = createLinkString(filterMap);
            logger.info("linkStr:" + linkStr);
            String sha1Data = SHA1.getDigestOfString(linkStr.getBytes("UTF-8"));
            PublicKey pubKey = getPublicKey(FuQianLaConstant.publicKey);
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, pubKey);
            ByteArrayOutputStream out = doFinal(cipher, hex2byte(hexSign), 128);
            //字节数组转换为字符串
            String sign = byteArraytoHexString(out.toByteArray());
            if (sign.length() > sha1Data.length()) {
                sign = sign.substring(sign.length() - sha1Data.length());
            }
            flag = sha1Data.equalsIgnoreCase(sign);
        } catch (Exception e) {
            logger.error("签名信息异常：" + e.getLocalizedMessage());
        }
        return flag;

    }


    /**
     * This method convert byte array to String
     *
     * @return String
     * <AUTHOR>
     */
    private static String byteArraytoHexString(byte[] b) {
        int iLen = b.length;
        //每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuffer sb = new StringBuffer(iLen * 2);
        for (int i = 0; i < iLen; i++) {
            int intTmp = b[i];
            //把负数转换为正数
            while (intTmp < 0) {
                intTmp = intTmp + 256;
            }
            //小于0F的数需要在前面补0
            if (intTmp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(intTmp, 16));
        }
        return sb.toString().toUpperCase();
    }

    private static byte[] hex2byte(String hex) throws IllegalArgumentException {
        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException();
        }
        char[] arr = hex.toCharArray();
        byte[] b = new byte[hex.length() / 2];
        for (int i = 0, j = 0, l = hex.length(); i < l; i++, j++) {
            String swap = "" + arr[i++] + arr[i];
            int byteint = Integer.parseInt(swap, 16) & 0xFF;
            b[j] = new Integer(byteint).byteValue();
        }
        return b;
    }

    /**
     * 除去数组中的空值和签名参数
     *
     * @param sArray 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    private static Map<String, ?> paraFilter(Map<String, ?> sArray) {
        Map<String, Object> result = new HashMap<String, Object>();
        if ((sArray == null) || (sArray.size() <= 0)) {
            return result;
        }
        for (String key : sArray.keySet()) {
            Object value = sArray.get(key);
            if ((value == null) || value.equals("") || key.equalsIgnoreCase("sign_info")
                    || key.equalsIgnoreCase("sign_type")) {
                continue;
            }
            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, ?> m = (Map<String, ?>) value;
                result.put(key, paraFilter(m));
            } else if (value instanceof List) {
                continue;// 不应包含多集合数据
            } else {
                result.put(key, value);
            }
        }
        return result;
    }

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    private static String createLinkString(Map<String, ?> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        StringBuffer prestr = new StringBuffer("");
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object o = params.get(key);
            String value = String.valueOf(o);
            if (o instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, ?> m = (Map<String, ?>) o;
                value = "{" + createLinkString(m) + "}";
            }
            if (i == (keys.size() - 1)) {// 拼接时，不包括最后一个&字符
                prestr.append(key + "=" + value);
            } else {
                prestr.append(key + "=" + value + "&");
            }
        }
        return prestr.toString();
    }

    /**
     * 得到公钥
     * 密钥字符串（经过base64编码）
     *
     * @throws Exception
     */
    private static PublicKey getPublicKey(String pubKey) throws Exception {
        byte[] pubkeyBytes = Base64.decode(pubKey);
        X509EncodedKeySpec pubKeySpec = new X509EncodedKeySpec(pubkeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(pubKeySpec);
    }

    /**
     * 得到私钥
     *
     * @param priKey 密钥字符串（经过base64编码）
     * @throws Exception
     */
    private static PrivateKey getPrivateKey(String priKey) throws Exception {
        byte[] priKeyBytes = Base64.decode(priKey);
        PKCS8EncodedKeySpec priKeySpec = new PKCS8EncodedKeySpec(priKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(priKeySpec);
    }

    /**
     * 加密解密处理
     * </br>加解密的字节大小最多是128，将需要解密的内容，按128位拆开解密
     *
     * @param cipher
     * @param content
     * @return
     * @throws IllegalBlockSizeException
     * @throws BadPaddingException
     * @throws IOException
     */
    private static ByteArrayOutputStream doFinal(Cipher cipher, byte[] content, int maxLen) throws IllegalBlockSizeException,
            BadPaddingException, IOException {
        InputStream ins = new ByteArrayInputStream(content);
        ByteArrayOutputStream writer = new ByteArrayOutputStream();
        byte[] buf = new byte[maxLen];
        int bufl;
        while ((bufl = ins.read(buf)) != -1) {
            byte[] block = null;
            if (buf.length == bufl) {
                block = buf;
            } else {
                block = new byte[bufl];
                System.arraycopy(buf, 0, block, 0, bufl);
            }
            byte[] eData = cipher.doFinal(block);
            writer.write(eData);
        }
        return writer;
    }

}
