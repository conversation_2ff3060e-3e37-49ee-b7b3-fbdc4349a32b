package com.fenbeitong.fenbeipay.core.model.dto.account;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table bank_collection_account_info
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankCollectionAccountInfo {
    /**
     * Database Column Remarks:
     *   主键
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.ID
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   收款配置主键
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.CONFIG_ID
     *
     * @mbg.generated
     */
    private Integer configId;

    /**
     * Database Column Remarks:
     *   企业编码
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COMPANY_ID
     *
     * @mbg.generated
     */
    private String companyId;

    /**
     * Database Column Remarks:
     *   企业分贝ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COMPANY_FB_ID
     *
     * @mbg.generated
     */
    private String companyFbId;

    /**
     * Database Column Remarks:
     *   虚拟户
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.DMANBR
     *
     * @mbg.generated
     */
    private String dmanbr;

    /**
     * Database Column Remarks:
     *   企业名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COMPANY_NAME
     *
     * @mbg.generated
     */
    private String companyName;

    /**
     * Database Column Remarks:
     *   收款方公司名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_NAME
     *
     * @mbg.generated
     */
    private String collCompanyName;

    /**
     * Database Column Remarks:
     *   收款方公司地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_ADDRESS
     *
     * @mbg.generated
     */
    private String collCompanyAddress;

    /**
     * Database Column Remarks:
     *   收款方开户银行
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_OPEN_BANK
     *
     * @mbg.generated
     */
    private String collCompanyOpenBank;

    /**
     * Database Column Remarks:
     *   收款方银行账号(对外收费用)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_BANK_ACCOUNT
     *
     * @mbg.generated
     */
    private String collCompanyBankAccount;

    /**
     * Database Column Remarks:
     *   收款方联系方式
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_FIXED_TELEPHONE
     *
     * @mbg.generated
     */
    private String collCompanyFixedTelephone;

    /**
     * Database Column Remarks:
     *   收款方纳税人识别号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_DENTIFICATION_NUMBER
     *
     * @mbg.generated
     */
    private String collDentificationNumber;

    /**
     * Database Column Remarks:
     *   收款方主营业务
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.COLL_COMPANY_MAIN_BUSUBESS
     *
     * @mbg.generated
     */
    private String collCompanyMainBusubess;

    /**
     * Database Column Remarks:
     *   状态：1在用；2未使用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.STATUS
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.CREATE_TIME
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   最后修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column bank_collection_account_info.UPDATE_TIME
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * 支行名称
     */
    private String bankBranchName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.ID
     *
     * @return the value of bank_collection_account_info.ID
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.ID
     *
     * @param id the value for bank_collection_account_info.ID
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.CONFIG_ID
     *
     * @return the value of bank_collection_account_info.CONFIG_ID
     *
     * @mbg.generated
     */
    public Integer getConfigId() {
        return configId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.CONFIG_ID
     *
     * @param configId the value for bank_collection_account_info.CONFIG_ID
     *
     * @mbg.generated
     */
    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COMPANY_ID
     *
     * @return the value of bank_collection_account_info.COMPANY_ID
     *
     * @mbg.generated
     */
    public String getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COMPANY_ID
     *
     * @param companyId the value for bank_collection_account_info.COMPANY_ID
     *
     * @mbg.generated
     */
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COMPANY_FB_ID
     *
     * @return the value of bank_collection_account_info.COMPANY_FB_ID
     *
     * @mbg.generated
     */
    public String getCompanyFbId() {
        return companyFbId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COMPANY_FB_ID
     *
     * @param companyFbId the value for bank_collection_account_info.COMPANY_FB_ID
     *
     * @mbg.generated
     */
    public void setCompanyFbId(String companyFbId) {
        this.companyFbId = companyFbId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.DMANBR
     *
     * @return the value of bank_collection_account_info.DMANBR
     *
     * @mbg.generated
     */
    public String getDmanbr() {
        return dmanbr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.DMANBR
     *
     * @param dmanbr the value for bank_collection_account_info.DMANBR
     *
     * @mbg.generated
     */
    public void setDmanbr(String dmanbr) {
        this.dmanbr = dmanbr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COMPANY_NAME
     *
     * @return the value of bank_collection_account_info.COMPANY_NAME
     *
     * @mbg.generated
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COMPANY_NAME
     *
     * @param companyName the value for bank_collection_account_info.COMPANY_NAME
     *
     * @mbg.generated
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_NAME
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_NAME
     *
     * @mbg.generated
     */
    public String getCollCompanyName() {
        return collCompanyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_NAME
     *
     * @param collCompanyName the value for bank_collection_account_info.COLL_COMPANY_NAME
     *
     * @mbg.generated
     */
    public void setCollCompanyName(String collCompanyName) {
        this.collCompanyName = collCompanyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_ADDRESS
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_ADDRESS
     *
     * @mbg.generated
     */
    public String getCollCompanyAddress() {
        return collCompanyAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_ADDRESS
     *
     * @param collCompanyAddress the value for bank_collection_account_info.COLL_COMPANY_ADDRESS
     *
     * @mbg.generated
     */
    public void setCollCompanyAddress(String collCompanyAddress) {
        this.collCompanyAddress = collCompanyAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_OPEN_BANK
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_OPEN_BANK
     *
     * @mbg.generated
     */
    public String getCollCompanyOpenBank() {
        return collCompanyOpenBank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_OPEN_BANK
     *
     * @param collCompanyOpenBank the value for bank_collection_account_info.COLL_COMPANY_OPEN_BANK
     *
     * @mbg.generated
     */
    public void setCollCompanyOpenBank(String collCompanyOpenBank) {
        this.collCompanyOpenBank = collCompanyOpenBank;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_BANK_ACCOUNT
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_BANK_ACCOUNT
     *
     * @mbg.generated
     */
    public String getCollCompanyBankAccount() {
        return collCompanyBankAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_BANK_ACCOUNT
     *
     * @param collCompanyBankAccount the value for bank_collection_account_info.COLL_COMPANY_BANK_ACCOUNT
     *
     * @mbg.generated
     */
    public void setCollCompanyBankAccount(String collCompanyBankAccount) {
        this.collCompanyBankAccount = collCompanyBankAccount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_FIXED_TELEPHONE
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_FIXED_TELEPHONE
     *
     * @mbg.generated
     */
    public String getCollCompanyFixedTelephone() {
        return collCompanyFixedTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_FIXED_TELEPHONE
     *
     * @param collCompanyFixedTelephone the value for bank_collection_account_info.COLL_COMPANY_FIXED_TELEPHONE
     *
     * @mbg.generated
     */
    public void setCollCompanyFixedTelephone(String collCompanyFixedTelephone) {
        this.collCompanyFixedTelephone = collCompanyFixedTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_DENTIFICATION_NUMBER
     *
     * @return the value of bank_collection_account_info.COLL_DENTIFICATION_NUMBER
     *
     * @mbg.generated
     */
    public String getCollDentificationNumber() {
        return collDentificationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_DENTIFICATION_NUMBER
     *
     * @param collDentificationNumber the value for bank_collection_account_info.COLL_DENTIFICATION_NUMBER
     *
     * @mbg.generated
     */
    public void setCollDentificationNumber(String collDentificationNumber) {
        this.collDentificationNumber = collDentificationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.COLL_COMPANY_MAIN_BUSUBESS
     *
     * @return the value of bank_collection_account_info.COLL_COMPANY_MAIN_BUSUBESS
     *
     * @mbg.generated
     */
    public String getCollCompanyMainBusubess() {
        return collCompanyMainBusubess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.COLL_COMPANY_MAIN_BUSUBESS
     *
     * @param collCompanyMainBusubess the value for bank_collection_account_info.COLL_COMPANY_MAIN_BUSUBESS
     *
     * @mbg.generated
     */
    public void setCollCompanyMainBusubess(String collCompanyMainBusubess) {
        this.collCompanyMainBusubess = collCompanyMainBusubess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.STATUS
     *
     * @return the value of bank_collection_account_info.STATUS
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.STATUS
     *
     * @param status the value for bank_collection_account_info.STATUS
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.CREATE_TIME
     *
     * @return the value of bank_collection_account_info.CREATE_TIME
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.CREATE_TIME
     *
     * @param createTime the value for bank_collection_account_info.CREATE_TIME
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bank_collection_account_info.UPDATE_TIME
     *
     * @return the value of bank_collection_account_info.UPDATE_TIME
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bank_collection_account_info.UPDATE_TIME
     *
     * @param updateTime the value for bank_collection_account_info.UPDATE_TIME
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }
}
