package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收银创建支付流水请求基础类，给多种退款方式提供基础封装
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@AllArgsConstructor
public class CashierRefundTradeBaseReqVo extends CashierBasePersonVo {


    //===========交易订单信息==============
    /**
     * 场景正向交易订单的ID
     */
    @NotBlank
    protected String fbOrderId;

    protected Integer checkStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date createTime;

    private String fbTradeId;
    /**
     * 售后场景逆向订单号
     */
    protected String refundOrderId;

    protected String cashierTxnId;

    //===================分贝通虚拟卡支付==============
    /**
     * 银行交易流水id
     */
    protected String bankTransNo;
    /**
     * 开户：分贝通虚拟卡号
     */
    protected String bankAccountNo;
    /**
     * 银行名称简写
     */
    protected String bankName;

    //===================总金额==============


    /**
     * 退款金额
     */
    @NotNull
    @Min(0)
    protected BigDecimal totalRefundAmount;
    /**
     * 个人退款总金额
     */
    @Min(0)
    protected BigDecimal personalRefundAmount;
    /**
     * 因公退款总金额
     */
    @Min(0)
    protected BigDecimal publicRefundAmount;



    /**
     * 因公退款方式()
     * CashierRefundWay
     */
    protected Integer cashierPublicRefundWay;

    /**
     * 因私退款方式
     * CashierRefundWay
     */
    protected Integer cashierRefundWay;

    //================附属信息=============
    /**
     * 通知场景方，退款已经完成
     */
    protected String bizCallbackUrl;
    /**
     * 退款原因
     */
    protected String refundReason = "其他原因";
    /**
     * 扩展字段1：存子订单id
     */
    protected String remark;
    /**
     * 操作渠道类型: 1:普通下单，2:后台回填（默认1)
     * @see com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType
     */
    protected Integer operationChannelType;

    /**
     * 客服id 如果是Stereo后台操作，请填写当前操作人
     */
    protected String customerServiceId;

    /**
     * 客服姓名  如果是Stereo后台操作，请填写当前操作人
     */
    protected String customerServiceName;


    public boolean hasPersonalRefund() {
        return personalRefundAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    public boolean hasTotalRefund() {
        return totalRefundAmount.compareTo(BigDecimal.ZERO) > 0;
    }


    /**
     * 退款总额金额是否超过了canMaxRefund
     *
     * @param canMaxRefund
     * @return
     */
    public boolean overTotalRefund(BigDecimal canMaxRefund) {
        return totalRefundAmount.compareTo(canMaxRefund) > 0;
    }

    public BigDecimal getPersonalRefundAmount(){

        if(ObjUtils.isEmpty(personalRefundAmount)){
            personalRefundAmount = BigDecimal.ZERO;
        }
        return personalRefundAmount;
    }
}