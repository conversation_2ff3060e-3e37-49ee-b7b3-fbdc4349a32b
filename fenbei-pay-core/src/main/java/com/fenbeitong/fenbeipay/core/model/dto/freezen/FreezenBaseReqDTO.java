package com.fenbeitong.fenbeipay.core.model.dto.freezen;

import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 冻结指定账户的金额
 * @Author: wh
 * @Date: 2019/3/20 5:15 PM
 */
@Data
public class FreezenBaseReqDTO implements Serializable {
    /**
     * 公司id
     */
    @NotBlank
    public String companyId;
    /**
     * 账户类型：4-主账户;1-商务账户；2-个人账户；3-企业账户 6红包券账户
     */
    @Min(0)
    @NotNull
    public Integer accountSubType;

    /**
     * @Description: 枚举冻结类型key
     * @see FreezenUseType
     */
    @NotNull
    public FreezenUseType freezenUseType;
    /**
     * 企业资金账户Id
     */
    @NotBlank
    private String accountId;
    /**
     * 账户模式
     **/
    private Integer accountModel;
    /**
     * 银行账号
     */
    private String bankAccountNo;

    /**
     * 银行账户虚户ID
     */
    private String bankAcctId;
    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行担保账号
     */
    private String guaranteeBankAccountNo;

    /**
     * 银行担保账号虚户ID
     */
    private String guaranteeBankAcctId;
    private String guaranteeBankName;


    /**
     * 冻结池id
     */
    private String freezenId;

    /**
     * 主账户Id
     */
    private String accountGeneralId;

    /**
     * 公司Id
     */
    private String companyMainId;


}
