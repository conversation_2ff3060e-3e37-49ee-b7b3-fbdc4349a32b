package com.fenbeitong.fenbeipay.core.service.bigdata;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.bigdata.HoloAdsBillFlowRelationADMapper;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationAD;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationADExample;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * 大数据-账单流水查询
 */
@Service
@Slf4j
public class AccountBillFlowRelationADManager {
    @Autowired
    private HoloAdsBillFlowRelationADMapper mapper;

    public List<HoloAdsBillFlowRelationAD> querySummaryInfoByAccountFlowIdsList(List<String> accountFlowIdList) {
        HoloAdsBillFlowRelationADExample example = new HoloAdsBillFlowRelationADExample();
        HoloAdsBillFlowRelationADExample.Criteria criteria = example.createCriteria();
        criteria.andAccountFlowIdIn(accountFlowIdList);
        example.setOrderByClause("create_time desc");
        return mapper.selectSummaryInfo(example);
    }

    public Map<String, List<HoloAdsBillFlowRelationAD>> querySummaryInfoByAccountFlowIdsMap(List<String> accountFlowIdList) {
        List<HoloAdsBillFlowRelationAD> billFlowRelationADList = querySummaryInfoByAccountFlowIdsList(accountFlowIdList);
        if(CollectionUtils.isEmpty(billFlowRelationADList)){
            return Maps.newHashMap();
        }

       return billFlowRelationADList.stream().collect(Collectors.groupingBy(HoloAdsBillFlowRelationAD::getAccountFlowId));
    }

    public List<HoloAdsBillFlowRelationAD> queryEntityByCondition(HoloAdsBillFlowRelationADExample example){
        return mapper.selectByExample(example);
    }

}
