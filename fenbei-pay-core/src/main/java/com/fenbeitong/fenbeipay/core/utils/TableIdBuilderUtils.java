package com.fenbeitong.fenbeipay.core.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据库表唯一主键生成器
 * <AUTHOR>
 * @date 2022/5/17
 */
public class TableIdBuilderUtils {
    /**
     * 随机数
     * @return Long
     */
    public static Long genTableId() {
        Snowflake snowflake = IdUtil.createSnowflake(1, 1);
        return snowflake.nextId();
    }

    public static Snowflake genSnowflake() {
        return IdUtil.createSnowflake(1, 1);
    }

    public static void test1(){
        Map<Long, Long> map = new HashMap<>();
        for (int i = 0; i < 1000000; i++) {
            long id = genTableId();
            if (map.containsKey(id)) {
                System.out.println("test1出现重复");
//                break;
            }
//            System.out.println("id1=*********" + id);
            map.put(id, id);
        }
    }
    public static void test2() {
        Snowflake snowflake = genSnowflake();
        Map<Long, Long> map = new HashMap<>();
        for (int i = 0; i < 1000000; i++) {
            long id = snowflake.nextId();
            if (map.containsKey(id)) {
                System.out.println("test2出现重复");
                break;
            }
//            System.out.println("id2=*********" + id);
            map.put(id, id);
        }
    }

}
