package com.fenbeitong.fenbeipay.core.utils;


import com.fenbeitong.finhub.common.constant.FinhubMessageCode;

/**
 * User: zhuminghua
 * Date: 2017/5/8 下午1:49
 * Desc:
 */
public interface StereoMessageCode extends FinhubMessageCode {

    /**
     * 没有数据
     */
    int NO_DATA = 603;

    /**
     * hyperloop接口返回参数异常
     */
    int HYPERLOOP_EXCEPTION = 701;

    /**
     * 缓存失效
     */
    int CACHE_EXPIRE = 702;

    /**
     * 日期重复
     */
    int DATE_REPEAT = 703;

    /**
     * 数据重复
     */
    int DATA_REPEAT = 704;

    /**
     * 用户不存在
     */
    int SYS_USER_NOT_EXIST = 10001;

    /**
     * 用户被禁用
     */
    int SYS_USER_DISABLE = 10002;
    /**
     * 用户名或密码不匹配
     */
    int SYS_USER_PASSWORD_INVALID = 10003;
    /**
     * token创建失败
     */
    int SYS_USER_TOKEN_CREATE_ERROR = 10004;
    /**
     * token无效
     */
    int SYS_USER_TOKEN_INVALID = 10005;
    /**
     * 保存操作日志参数为空
     */
    int SYS_OP_LOG_ILLEGAL_ARGUMENT = 10006;

    /**
     * 用户已存在
     */
    int SYS_USER_IS_EXIST = 10007;

    /**
     * 角色已存在
     */
    int SYS_ROLE_IS_EXIST = 10007;

    /**
     * 该公司已存在
     */
    int COMPANY_NAME_EXIST = 20001;

    /**
     * 该授权负责人电话已存在
     */
    int AUTHORIZATION_MOBIE_EXIST = 20002;

    /**
     * 营业执照号已存在
     */
    int COMPANY_CODE_EXIST = 20003;

    /**
     * HL酒店预下单异常
     * "code": 300016／300017/300018/300020
     * "msg": "无效价格计划"/"价格变更"/”满房”/”酒店房间配额不足”
     */
    int HL_SUCCESS = 0;
    int HL_VOID_PRICE_PLAN = 300016;
    int HL_PRICE_CHANGE = 300017;
    int HL_FULL_HOUSE = 300018;
    int HL_LACK_ROOM = 300020;

    /**
     * SAAS接口返回值为null
     */
    int SAAS_NULL_RESULT = 400001;

    /**
     * 外部接口调用错误
     */
    int INTERFACE_EXCEPTION = 999999;

    /**
     * 企业不存在或收件邮箱为空
     */
    int COMPANY_EMAIL_ISNULL = 500001;

    /**
     * 短信发送失败
     */
    int SEND_SMS_ERROR = 500002;

    /**
     * 文件格式错误
     */
    int FORMAT_ERROR = 800001;

    /**
     * 文件数据条数超过限制
     */
    int DATA_TOO_MUCH = 800002;

    /**
     * 采购售后单状态异常
     */
    int MALL_CUSTOMER_ERROR = 700001;

    /**
     * 协议酒店相关-公司已经存在
     */

    int PROTOCOL_COMPANY_NOT_EMPTY = 400005;

    /**
     * 协议酒店相关-协议酒店已经存在
     */
    int PROTOCOL_HOTEL_NOT_EMPTY = 400002;

    /**
     * 协议酒店相关－房型为空
     */
    int PROTOCOL_ROOM_LIST_EMPTY = 400003;

    /**
     * 协议酒店相关-价格计划为空
     */
    int PROTOCOL_PLAN_EMPTY = 400004;

    /**
     * 协议酒店不存在
     */
    int PROTOCOL_HOTEL_EMPTY = 400006;

    /**
     * 协议酒店相关-同一价格计划不能重复添加
     */
    int PROTOCOL_PLAN_NOT_EMPTY = 400007;

    /**
     * 协议酒店相关－同一房型不能重复添加
     */
    int PROTOCOL_ROOM_LIST_NOT_EMPTY = 400008;

    /**
     * IVR客服外呼组接口验证错误标识
     */
    String  IVR_CUST_PHONE_GROUP_ERROR = "400";

    /**
     * 需要双因子认证
     */
    String  NEED_DOUBLE_FACTOR = "1";

    /**
     * 密码错误次数超过5次
     */
    int LOGIN_PASSWORD_TIME = 10009;

    /**
     * 发票明细表异常
     */
    int INVOICE_DETAIL_ERROR = 6699;

    /**
     * 提现处理中
     * 与前端定义的交互码 弹窗提示
     */
    int WITHDRAWAL_PROCESS = 30000;
}

