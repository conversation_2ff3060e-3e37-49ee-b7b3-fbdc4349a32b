package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.core.model.CashierVOConverter;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 仍需退款金额汇总
 */

@Data
@NoArgsConstructor
public class CashierRefundSettlementRemainReturnStatVo {

    private String fbOrderId;

    private BigDecimal refundRemainAmount = BigDecimal.ZERO;

    private BigDecimal refundPublicRemainAmount = BigDecimal.ZERO;
    private BigDecimal refundCompanyRemainAmount = BigDecimal.ZERO;
    private BigDecimal refundRedcouponRemainAmount = BigDecimal.ZERO;

    private BigDecimal refundPersonalRemainAmount = BigDecimal.ZERO;

    private BigDecimal refundFBBRemainAmount = BigDecimal.ZERO;

    private BigDecimal refundFBQRemainAmount = BigDecimal.ZERO;

    private BigDecimal refundThirdRemainAmount = BigDecimal.ZERO;


    public CashierRefundSettlementRemainReturnStatVo(CashierOrderSettlement cashier, CashierRefundSettlementHistoryAmountStatVo refundHistoryStat) {
        this.fbOrderId = cashier.getFbOrderId();
        this.refundRemainAmount = CashierVOConverter.getTotalNetSettlePrice(cashier).subtract(refundHistoryStat.getRefundAmount());
        this.refundPublicRemainAmount = CashierVOConverter.getPublicNetSettlePrice(cashier).subtract(refundHistoryStat.getRefundAmountPublic());
        this.refundCompanyRemainAmount = cashier.getAmountCompany().subtract(refundHistoryStat.getRefundAmountCompany());
        this.refundPersonalRemainAmount = cashier.getAmountPersonal().subtract(refundHistoryStat.getRefundAmountPersonal());
        this.refundFBBRemainAmount = cashier.getAmountFbb().subtract(refundHistoryStat.getRefundAmountFbb());
        this.refundFBQRemainAmount = cashier.getAmountVoucher().subtract(refundHistoryStat.getRefundAmountVoucher());
        this.refundThirdRemainAmount = cashier.getAmountThird().subtract(refundHistoryStat.getRefundAmountThird());
    }
}
