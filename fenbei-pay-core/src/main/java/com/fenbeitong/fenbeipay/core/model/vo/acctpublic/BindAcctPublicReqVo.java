package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * @CreateDate: 2020年05月10日18:07:54
 * @Version: 4.0.0
 * 支付
 */
@Data
@ToString(callSuper = true)
public class BindAcctPublicReqVo extends AcctPublicBaseReqVo {
    /**
     * FBT虚拟卡：银行帐号
     */
    @NotBlank
    private String bankAccountNo;

    /**
     * 银行卡户状态
     */
    private Integer bankStatus;

    /**
     * 银行开户身份证件号
     */
    private String idEntityCard;

    /**
     * 银行开户手机号
     */
    private String employeePhone;

    /**
     * 操作渠道：
     *@see OperationChannelType
     */
    @NotNull
    private Integer operationChannel;


    public void checkReq(BindAcctPublicReqVo reqDTO) {
        ValidateUtils.validate(reqDTO);
        if (ObjUtils.isEmpty(reqDTO.getBankAccountName())){
            throw new ValidateException("开户行名称不能为空！");
        }
    }
}
