package com.fenbeitong.fenbeipay.core.model.dto;

import lombok.Data;

@Data
public class AcctFlowWithBillStereoDTO extends AcctFlowBaseDTO {


    /**
     * 企业id
     */
    private String companyName;

    /**
     * 平台方
     */
    private String fundPlatformName;

    /**
     * 银行账号
     */
    private String bankAccountNo;


    /**
     * 账户类型
     */
    private String accountSubTypeName;

    /**
     * 业务账户类型
     */
    private String accountModelName;

    /**
     * 业务类型
     */
    private String operationTypeName;


    /**
     * 场景 stereo
     */
    private String orderType;

    /**
     * 银行记账时间
     */
    private String syncBankTime;

    /**
     * 备注
     */
    private String remark;

}
