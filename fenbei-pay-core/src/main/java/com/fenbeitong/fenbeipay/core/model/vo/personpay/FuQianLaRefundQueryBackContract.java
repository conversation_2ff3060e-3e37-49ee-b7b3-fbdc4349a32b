package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;

/**
 * 付钱啦查询结果返回信息
 */
@Deprecated
public class FuQianLaRefundQueryBackContract implements Serializable {

    private String ret_code;
    private String ret_desc;
    private String charset;
    private String sign_type;
    private String sign_info;
    private String version;
    private ChargeData ret_data;

    public static class ChargeData {

        private String txn_id;
        private String refund_no;
        private Integer refund_amount;
        private String refund_reason;
        private String currency;
        private String status;
        private String operator_id;
        private String ex_code;
        private String ex_msg;
        private String receive_time;
        private String complete_time;
        private String order_no;

        public String getTxn_id() {
            return txn_id;
        }

        public void setTxn_id(String txn_id) {
            this.txn_id = txn_id;
        }

        public String getRefund_no() {
            return refund_no;
        }

        public void setRefund_no(String refund_no) {
            this.refund_no = refund_no;
        }

        public Integer getRefund_amount() {
            return refund_amount;
        }

        public void setRefund_amount(Integer refund_amount) {
            this.refund_amount = refund_amount;
        }

        public String getRefund_reason() {
            return refund_reason;
        }

        public void setRefund_reason(String refund_reason) {
            this.refund_reason = refund_reason;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getOperator_id() {
            return operator_id;
        }

        public void setOperator_id(String operator_id) {
            this.operator_id = operator_id;
        }

        public String getEx_code() {
            return ex_code;
        }

        public void setEx_code(String ex_code) {
            this.ex_code = ex_code;
        }

        public String getEx_msg() {
            return ex_msg;
        }

        public void setEx_msg(String ex_msg) {
            this.ex_msg = ex_msg;
        }

        public String getReceive_time() {
            return receive_time;
        }

        public void setReceive_time(String receive_time) {
            this.receive_time = receive_time;
        }

        public String getComplete_time() {
            return complete_time;
        }

        public void setComplete_time(String complete_time) {
            this.complete_time = complete_time;
        }

        public String getOrder_no() {
            return order_no;
        }

        public void setOrder_no(String order_no) {
            this.order_no = order_no;
        }
    }

    public String getRet_code() {
        return ret_code;
    }

    public void setRet_code(String ret_code) {
        this.ret_code = ret_code;
    }

    public String getRet_desc() {
        return ret_desc;
    }

    public void setRet_desc(String ret_desc) {
        this.ret_desc = ret_desc;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getSign_type() {
        return sign_type;
    }

    public void setSign_type(String sign_type) {
        this.sign_type = sign_type;
    }

    public String getSign_info() {
        return sign_info;
    }

    public void setSign_info(String sign_info) {
        this.sign_info = sign_info;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public ChargeData getRet_data() {
        return ret_data;
    }

    public void setRet_data(ChargeData ret_data) {
        this.ret_data = ret_data;
    }
}
