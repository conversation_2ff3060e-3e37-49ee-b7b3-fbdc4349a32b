package com.fenbeitong.fenbeipay.core.service.redis;


import com.fenbeitong.fenbeipay.core.constant.core.RedisKeyConstant;
import com.fenbeitong.fenbeipay.core.constant.core.TimeCheckConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * redis加锁操作
 */
public class RedisDistributionLock {

    private static final Logger logger = LoggerFactory.getLogger(RedisDistributionLock.class);

    /**
     * 加锁字符串
     *
     * @param lockKeyIn
     * @param redisTemplate
     * @return
     */
    public static Long lock(String lockKeyIn, RedisTemplate<Serializable, Serializable> redisTemplate) {
        Long initTimeOut = TimeCheckConstant.INIT_TIMEOUT;
        if (StringUtils.isEmpty(lockKeyIn)) {
            return initTimeOut;
        }
        final String lockKey = MessageFormat.format(RedisKeyConstant.USER_CENTER_REDIS_KEY, lockKeyIn);
        //锁时间
        Long lockTimeOut = System.currentTimeMillis() + TimeCheckConstant.LOCK_TIMEOUT + 1;
        try {
            return getLockStrTime(redisTemplate, lockKey, lockTimeOut, initTimeOut);
        } catch (Exception e) {
            logger.error("加锁字符串异常：" + e.getLocalizedMessage());
        }
        return initTimeOut;
    }

    /**
     * 解锁字符串
     *
     * @param lockKeyIn
     * @param lockValue
     * @param redisTemplate
     */
    public static void unlock(String lockKeyIn, long lockValue, RedisTemplate<Serializable, Serializable> redisTemplate) {
        final String lockKey = MessageFormat.format(RedisKeyConstant.USER_CENTER_REDIS_KEY, lockKeyIn);
        if (!StringUtils.isEmpty(lockKeyIn)) {
            try {
                Object timeLock = redisTemplate.opsForValue().get(lockKey);
                String currtLockTimeoutStr = (String) timeLock; // redis里的时间
                //如果是加锁者 则删除锁 如果不是则等待自动过期 重新竞争加锁
                if (currtLockTimeoutStr != null && Long.parseLong(currtLockTimeoutStr) == lockValue) {
                    redisTemplate.delete(lockKey); //删除键
                }
            } catch (Exception e) {
                logger.error("解锁字符串异常：" + e.getLocalizedMessage());
            }
        }
    }

    /**
     * 等待超时时间获取锁
     *
     * @param lockKeyIn
     * @param redisTemplate
     * @return
     */
    public static Long lockWaitTimeOut(String lockKeyIn, RedisTemplate<Serializable, Serializable> redisTemplate) {
        //logger.info("等待超时时间获取锁开始，lockKeyIn＝{}", lockKeyIn);
        Long initTimeOut = TimeCheckConstant.INIT_TIMEOUT;
        if (StringUtils.isEmpty(lockKeyIn)) {
            return initTimeOut;
        }
        final String lockKey = MessageFormat.format(RedisKeyConstant.USER_CENTER_REDIS_KEY, lockKeyIn);
        //锁时间
        Long lockTimeOut = System.currentTimeMillis() + TimeCheckConstant.LOCK_WAIT_TIMEOUT;
        Long lockWaitTime = TimeCheckConstant.INIT_TIMEOUT + TimeCheckConstant.LOCK_WAIT_TIMEOUT;
        Long lockStrTime = initTimeOut;
        try {
            lockStrTime = getLockStrTime(redisTemplate, lockKey, lockTimeOut, initTimeOut);
            //未超时并且没有获取锁，重试
            while (lockWaitTime > 0 && lockStrTime.longValue() == initTimeOut.longValue()) {
                //logger.info("进入重试阶段，lockWaitTime={},lockKey={}", lockWaitTime, lockKey);
                lockStrTime = getLockStrTime(redisTemplate, lockKey, lockTimeOut, initTimeOut);
                if (lockStrTime.longValue() == initTimeOut.longValue() && lockTimeOut > 0) {
                    lockWaitTime -= TimeCheckConstant.LOCK_AWATI_TIME;
                    Thread.sleep(TimeCheckConstant.LOCK_AWATI_TIME);
                }
            }
        } catch (Exception e) {
            logger.error("加锁字符串异常：" + e.getLocalizedMessage());
        } finally {
            //未超时且未获取到锁时继续等待（重新排队获取）
            if (lockWaitTime > 0 && lockStrTime.longValue() == initTimeOut.longValue()) {
                //logger.info("未超时且未获取到锁时继续等待（重新排队获取）,lockWaitTime={},lockStrTime={},lockKey={}", lockWaitTime, lockStrTime, lockKeyIn);
                return lockWaitTimeOut(lockKeyIn, redisTemplate);
            }
            //logger.info("等待超时时间获取锁结束，");
            return lockStrTime;
        }
    }

    /**
     * 竞争锁
     *
     * @param redisTemplate
     * @param lockKey
     * @param lockTimeOut
     * @param initTimeOut
     * @return
     */
    private static Long getLockStrTime(RedisTemplate<Serializable, Serializable> redisTemplate, String lockKey, Long lockTimeOut, Long initTimeOut) {
        try {
            if (redisTemplate.opsForValue().setIfAbsent(lockKey, lockTimeOut.toString())) {
                //logger.info("竞争锁成功，lockKey＝{}", lockKey);
                //设置超时时间，释放内存
                redisTemplate.expire(lockKey, TimeCheckConstant.LOCK_TIMEOUT, TimeUnit.MILLISECONDS);
                return lockTimeOut;
            } else {
                String currtLockTimeoutStr = (String) redisTemplate.opsForValue().get(lockKey);
                //锁已经失效
                if (currtLockTimeoutStr != null && Long.parseLong(currtLockTimeoutStr) < System.currentTimeMillis()) {
                    String oldLockTimeoutStr = (String) redisTemplate.opsForValue().getAndSet(lockKey, lockTimeOut.toString());
                    // 获取上一个锁到期时间，并设置现在的锁到期时间
                    if (oldLockTimeoutStr != null && oldLockTimeoutStr.equals(currtLockTimeoutStr)) {
                        //设置超时时间，释放内存
                        //logger.info("失效后竞争锁成功，lockKey＝{}", lockKey);
                        redisTemplate.expire(lockKey, TimeCheckConstant.LOCK_TIMEOUT, TimeUnit.MILLISECONDS);
                        return lockTimeOut;
                    }
                }
                return initTimeOut;
            }
        } catch (Exception e) {
            logger.error("竞争锁异常:{}", e.getLocalizedMessage());
        }
        return initTimeOut;
    }
}