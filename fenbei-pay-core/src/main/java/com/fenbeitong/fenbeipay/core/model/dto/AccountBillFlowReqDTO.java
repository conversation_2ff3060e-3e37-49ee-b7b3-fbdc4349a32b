package com.fenbeitong.fenbeipay.core.model.dto;

import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundAcctCreditOptType;
import com.fenbeitong.finhub.common.constant.FundAcctTradeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountBillFlowReqDTO {

    /**
     * 交易编码（交易编号）
     */
    private String accountFlowId;

    /**
     * 账户模式
     *
     * @see com.fenbeitong.finhub.common.constant.FundAccountModelType
     */
    private Integer accountModel;

    private String bankAccountNo;

    private String bankName;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 请求银行订单号
     */
    private String syncBankTransNo;

    /**
     * 电子回单状态
     */
    private Integer costImageStatus;

    /**
     * 查询开始时间
     */
    private Date startTime;
    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 业务类型：取里面部分枚举
     *
     * @see FundAcctCreditOptType
     */
    private Integer operationType;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 操作人所属公司ID
     */
    private String operationUserCompanyId;

    /**
     * 当前页数
     **/
    @Min(1)
    private Integer pageNo = 1;

    /**
     * 每页条数
     **/
    @Min(1)
    private Integer pageSize = 10;


    /**
     * 公司id
     *
     * @Author: wh
     * @Date: 2021/3/2 3:14 PM
     */
    private String companyId;

    /**
     * 交易类型
     *
     * @see FundAcctTradeType
     */
    private Integer tradeType;

    /**
     * 场景
     */
    private Integer orderType;
    
    /**
     * 对手账户枚举code @see CollectionAccountEntityForBizDebitEnum
     */
    private Integer targetAcctCode;

    /**
     * 账户类型：1,总账户 2 商务消费 3个人消费4企业账户5虚拟卡6红包券7对公付款
     *
     * @see FundAccountSubType
     */
    private Integer accountSubType;

    /**
     * 账单编号
     */
    private String billNo;

}
