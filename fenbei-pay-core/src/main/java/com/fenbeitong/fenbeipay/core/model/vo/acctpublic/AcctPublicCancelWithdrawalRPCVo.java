package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.req.AcctPublicBaseReqRPCDTO;
import com.fenbeitong.finhub.common.validation.constraints.BigDecimalNotPoint;
import com.luastar.swift.base.exception.ValidateException;
import com.luastar.swift.base.utils.ObjUtils;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 充值金额到充值模式对公账户请求对象
 * @ClassName: AcctPublicRechargeReqRPCDTO
 * @Author: wh
 * @CreateDate: 2020/5/18 7:41 PM
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AcctPublicCancelWithdrawalRPCVo extends AcctPublicBaseReqVo {

    /**
     * FBT:虚户银行帐号
     */
    @NotBlank
    private String bankAccountNo;

    /**
     * 操作金额 单位：分
     */
    @NotNull
    @Max(trillion)
    @BigDecimalNotPoint
    private BigDecimal operationAmount;

    /**
     * 银行交易时间
     */
    private Date bizTime;

    /**
     * 操作渠道：
     *@see OperationChannelType
     */
    @NotNull
    private Integer operationChannel;
    /**
     * 交易说明
     */
    private String operationDescription;

    /**
     * 操作人Id
     */
    private String operationUserId;

    /**
     * 操作人姓名
     */
    private String operationUserName;



}
