package com.fenbeitong.fenbeipay.core.model.vo.newaccount;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class AccountSubRespVo extends BaseVo {

    /**
     * 公司Id
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 授信额度（授信企业）
     */
    private BigDecimal initCredit;

    /**
     * 子账户余额 单位：分
     */
    private BigDecimal balance;

    /**
     * 账户类型：2-商务账户；3-个人账户；4-企业账户
     */
    private Integer accountSubType;

    /**
     * 账户状态
     */
    private Integer accountStatus;

    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;

    private Integer activeStatus;

    private Integer showStatus;


}