package com.fenbeitong.fenbeipay.core.model.dto;

import com.fenbeitong.fenbeipay.api.model.dto.acct.resp.KeyValue;
import com.fenbeitong.fenbeipay.core.enums.account.BillUserVisibleStateEnum;
import com.luastar.swift.base.utils.DateUtils;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
public abstract class AcctFlowBaseDTO implements Serializable {

    /**
     * 平台流水号（交易编号）
     */
    private String accountFlowId;

    /**
     * 交易类型
     */
    private String tradeTypeName;

    /**
     * 业务类型
     */
    private Integer operationType;

    /**
     * 是否展示账户信息
     */
    private Boolean showTargetAcct;

    /**
     * 对手账户
     */
    private String targetBankShow;

    /**
     * 目标账户名
     */
    private String targetAccountName;

    /**
     * 目标账户
     */
    private String targetAccount;

    /**
     * 银行名称包括支行
     */
    private String targetBankAllName;


    /**
     * 相关单号（关联订单）
     */
    private String bizNo;


    /**
     * 操作金额:单位：分（交易金额）
     */
    private BigDecimal operationAmount;

    /**
     * 余额:单位：分
     */
    private BigDecimal balance;

    /**
     * 操作人
     */
    private String operationUserName;

    /**
     * 操作人所属公司ID
     */
    private String operationUserCompanyId;

    /**
     * 操作人所属公司名称
     */
    private String operationUserCompanyName;

    /**
     * 操作时间（交易时间）
     */
    private String createTime;

    /**
     * 银行流水号（银行交易流水）
     */
    private String bankTransNo;

    /**
     * 请求银行订单号（银行上账交易流水号）
     */
    private String syncBankTransNo;

    /**
     * 流水关联账单
     * 对应stereo List<BillSummaryDTO.SummaryInfoBean>
     */
    private List<SummaryInfoBean> billSummary = new ArrayList<>();

    /**
     * 流水关联账单
     * 样例数据 账单编号：****************，订单编号：620dc899601345c53f6b5421，账单日：2022-03-01，金额：110.00，账单状态：未出账\r\n账单编号：****************，订单编号：620dc899601345c53f6b5421，账单日：2022-03-01，金额：110.00，账单状态：未出账\r\n
     */
    private String billSummaryDesc;



    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 票 id
     */
    private String productId;

    /**
     * 账单日期
     */
    private String billDate;


    /**
     * 金额
     */
    private BigDecimal totalAmount;

    /**
     * 用户可见状态：1.未出账、2.已发送待确认、3.已确认未结清、4.已结清、5.已确认、6.已出账待发送
     */
    private String userVisibleState;



    /**
     *  场景 web
     */
    private String orderTypeName;


    public void addSummaryInfoBean(String billNo, String orderId, String productId, Date billDate, BigDecimal totalAmount, Integer userVisibleState) {
        KeyValue<Integer, String> userVisibleStateKv = new KeyValue<>();
        userVisibleStateKv.setKey(userVisibleState);
        userVisibleStateKv.setValue(BillUserVisibleStateEnum.getValueByKey(userVisibleState));
        SummaryInfoBean summaryInfoBean = SummaryInfoBean.builder()
                .billNo(billNo)
                .orderId(orderId)
                .productId(productId)
                .billDate(Optional.ofNullable(billDate).map(date -> DateUtils.format(date, "yyyy-MM-dd")).orElse(null))
                .totalAmount(totalAmount)
                .userVisibleState(userVisibleStateKv).build();
        billSummary.add(summaryInfoBean);
    }

    public void addSummaryInfoBean(String billNo) {
        SummaryInfoBean summaryInfoBean = SummaryInfoBean.builder().billNo(billNo).build();
        billSummary.add(summaryInfoBean);
    }


    @Getter
    @Setter
    @Builder
    public static class SummaryInfoBean implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 账单编号
         */
        private String billNo;

        /**
         * 订单编号
         */
        private String orderId;

        /**
         * 票 id
         */
        private String productId;

        /**
         * 账单日期
         */
        private String billDate;


        /**
         * 金额
         */
        private BigDecimal totalAmount;

        /**
         * 用户可见状态：1.未出账、2.已发送待确认、3.已确认未结清、4.已结清、5.已确认、6.已出账待发送
         */
        private KeyValue<Integer, String> userVisibleState;

    }
}
