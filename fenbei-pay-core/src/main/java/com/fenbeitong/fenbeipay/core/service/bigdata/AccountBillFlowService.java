package com.fenbeitong.fenbeipay.core.service.bigdata;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fenbeitong.fenbeipay.api.constant.enums.account.AccountAllFlowFlagEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.account.CategoryTypeMappingEnum;
import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.acct.AcctAllFlowConvertDTO;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowBaseDTO;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowWithBillStereoDTO;
import com.fenbeitong.fenbeipay.core.model.dto.AcctFlowWithBillWebDTO;
import com.fenbeitong.fenbeipay.core.model.dto.BillFlowPageQuery;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowAD;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsAccountAllFlowADExample;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationAD;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.bigdata.HoloAdsBillFlowRelationADExample;
import com.fenbeitong.finhub.common.constant.BankCoreConstant;
import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.constant.CategoryTypeEnum;
import com.fenbeitong.finhub.common.constant.FundAccountModelType;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.constant.FundAcctCompanyCardOptType;
import com.fenbeitong.finhub.common.constant.FundAcctCostImageStatus;
import com.fenbeitong.finhub.common.constant.FundAcctDebitOptType;
import com.fenbeitong.finhub.common.constant.FundAcctPublicOptType;
import com.fenbeitong.finhub.common.constant.FundAcctSyncBankStatus;
import com.fenbeitong.finhub.common.constant.FundAcctTradeType;
import com.fenbeitong.finhub.common.constant.FundPlatformEnum;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.company.CompanyNewReqDTO;
import com.fenbeitong.usercenter.api.model.po.company.CompanyNew;
import com.fenbeitong.usercenter.api.service.company.ICompanyInfoService;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.DateUtils;

@Service
public class AccountBillFlowService {
    
    @Autowired
    private AccountAllFlowADManager accountAllFlowADManager;
    
    @Autowired
    private AccountBillFlowRelationADManager accountBillFlowRelationADManager;
    
    @Autowired
    private ICompanyInfoService iCompanyInfoService;

    private AcctFlowWithBillWebDTO convertDbEntity2WebDto(HoloAdsAccountAllFlowAD flowAD, BillFlowPageQuery condition) {
        AcctFlowWithBillWebDTO dto = new AcctFlowWithBillWebDTO();
        dto.setCreateTime(DateUtils.format(flowAD.getCreateTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        dto.setAccountFlowId(flowAD.getAccountFlowId());
        dto.setTargetBankAllName(BankNameEnum.getBankEnum(flowAD.getTargetBankName()).getName());
        dto.setTradeTypeName(AcctAllFlowConvertDTO.getTradeTypeName(flowAD.getTradeType()));
        dto.setOperationType(flowAD.getOperationType());
        dto.setOperationTypeDesc(AcctAllFlowConvertDTO.getOperationTypeDesc(flowAD.getFlowFlag(), flowAD.getOperationType()));
        dto.setTargetAccount(flowAD.getTargetAccount());
        Integer accountSubType = Optional.ofNullable(condition.getAccountSubType()).orElse(flowAD.getAccountSubType());
        dto.setShowTargetAcct(getShowTargetAcct(flowAD.getBankAccountNo(), flowAD.getTargetAccount(), accountSubType, flowAD.getTargetBankAcctId(), flowAD.getOperationType()));
        if (BooleanUtils.isFalse(dto.getShowTargetAcct())) {
            dto.setTargetAccount(null);
        }
        dto.setTargetBankShow(AcctAllFlowConvertDTO.getTargetBankShow(flowAD.getBankName(), flowAD.getOperationType(), flowAD.getBankAccountNo(), flowAD.getTargetAccountSubType(), flowAD.getTargetAccount(), dto.getTargetBankAllName(), flowAD.getTargetBankName()));
        dto.setTargetAccountName(initDefaultValueIfNull(flowAD.getCompanyMainName()));
        dto.setOperationAmount(flowAD.getOperationAmount());
        dto.setBalance(flowAD.getBalance());
        // 操作渠道类型: 1:普通下单，2:后台回填（默认1)
        dto.setOperationUserName(flowAD.getOperationUserName());
        Integer operationChannelType = flowAD.getOperationChannelType();
        if (Objects.isNull(operationChannelType) || OperationChannelType.isStereo(operationChannelType)) {
            dto.setOperationUserName("分贝通");
        }
        dto.setOperationUserCompanyName(flowAD.getOperationUserCompanyName());
        dto.setOperationUserCompanyId(flowAD.getOperationUserCompanyId());
        dto.setTradeTime(DateUtils.format(flowAD.getSyncBankTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        dto.setOrderTypeName(Optional.ofNullable(flowAD.getCategoryType()).map(CategoryTypeEnum::valueOf).map(CategoryTypeEnum::getName).orElse(StringUtils.EMPTY));
        dto.setBizNo(flowAD.getBizId());
        dto.setOperationDescription(initDefaultValueIfNull(flowAD.getRemark()));
        dto.setCostImageDesc(Optional.ofNullable(flowAD.getReceiptStatus()).map(FundAcctCostImageStatus::getEnum).orElse(FundAcctCostImageStatus.COST_IMAGE_NON).getDesc());
        dto.setBankTransNo(flowAD.getBankTransNo());
        dto.setSyncBankTransNo(flowAD.getSyncBankTransNo());
        return dto;
    }

    private AcctFlowWithBillWebDTO convertDbEntity2WebDto4Export(HoloAdsAccountAllFlowAD flowAD, BillFlowPageQuery condition) {
        AcctFlowWithBillWebDTO dto = new AcctFlowWithBillWebDTO();
        dto.setCreateTime(DateUtils.format(flowAD.getCreateTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        dto.setAccountFlowId(flowAD.getAccountFlowId());
        dto.setTargetBankAllName(BankNameEnum.getBankEnum(flowAD.getTargetBankName()).getName());
        dto.setTradeTypeName(AcctAllFlowConvertDTO.getTradeTypeName(flowAD.getTradeType()));
        dto.setOperationType(flowAD.getOperationType());
        dto.setOperationTypeDesc(AcctAllFlowConvertDTO.getOperationTypeDesc(flowAD.getFlowFlag(), flowAD.getOperationType()));
        dto.setTargetAccount(flowAD.getTargetAccount());
        Integer accountSubType = Optional.ofNullable(condition.getAccountSubType()).orElse(flowAD.getAccountSubType());
        dto.setShowTargetAcct(getShowTargetAcct(flowAD.getBankAccountNo(), flowAD.getTargetAccount(), accountSubType, flowAD.getTargetBankAcctId(), flowAD.getOperationType()));
        if (BooleanUtils.isFalse(dto.getShowTargetAcct())) {
            dto.setTargetAccount(null);
        }
        dto.setTargetBankShow(AcctAllFlowConvertDTO.getTargetBankShow(flowAD.getBankName(), flowAD.getOperationType(), flowAD.getBankAccountNo(), flowAD.getTargetAccountSubType(), flowAD.getTargetAccount(), dto.getTargetBankAllName(), flowAD.getTargetBankName()));
        dto.setTargetAccountName(flowAD.getCompanyMainName());
        dto.setOperationAmount(BigDecimalUtils.fen2yuan(flowAD.getOperationAmount()));
        dto.setBalance(BigDecimalUtils.fen2yuan(flowAD.getBalance()));
        // 操作渠道类型: 1:普通下单，2:后台回填（默认1)
        dto.setOperationUserName(flowAD.getOperationUserName());
        Integer operationChannelType = flowAD.getOperationChannelType();
        if (Objects.isNull(operationChannelType) || OperationChannelType.isStereo(operationChannelType)) {
            dto.setOperationUserName("分贝通");
        }
        dto.setOperationUserCompanyName(flowAD.getOperationUserCompanyName());
        dto.setOperationUserCompanyId(flowAD.getOperationUserCompanyId());
        dto.setTradeTime(DateUtils.format(flowAD.getSyncBankTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        dto.setOrderTypeName(Optional.ofNullable(flowAD.getCategoryType()).map(CategoryTypeEnum::valueOf).map(CategoryTypeEnum::getName).orElse(StringUtils.EMPTY));
        dto.setBizNo(flowAD.getBizId());
        dto.setOperationDescription(flowAD.getRemark());
        dto.setCostImageDesc(Optional.ofNullable(flowAD.getReceiptStatus()).map(FundAcctCostImageStatus::getEnum).orElse(FundAcctCostImageStatus.COST_IMAGE_NON).getDesc());
        dto.setBankTransNo(flowAD.getBankTransNo());
        dto.setSyncBankTransNo(flowAD.getSyncBankTransNo());
        return dto;
    }

    public Boolean getShowTargetAcct(String bankAccountNo, String targetAccount, Integer accountSubType, String targetBankAcctId, Integer operationType) {
        if (StringUtils.isBlank(bankAccountNo) || StringUtils.isBlank(targetAccount)) {
            // 虚拟卡业务，QX 2022-03-24
            return Objects.nonNull(accountSubType) && 
                accountSubType.equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey()) && 
                StringUtils.isNotBlank(targetBankAcctId);
        }
        
        if (Objects.equals(bankAccountNo, targetAccount) && 
            (Objects.nonNull(operationType) && 
                operationType != FundAcctPublicOptType.DISHONOURED.getKey())) {
            // 虚拟卡业务，QX 2022-03-24
            return Objects.nonNull(accountSubType) && 
                accountSubType.equals(FundAccountSubType.BANK_VIRTUAL_ACCOUNT.getKey()) && 
                StringUtils.isNotBlank(targetBankAcctId);
        } else {
            return true;
        }
    }

    private AcctFlowWithBillStereoDTO convertDbEntity2StereoDto(HoloAdsAccountAllFlowAD billFlowRelationAD, Map<String, String> companyIdNameMap, BillFlowPageQuery condition) {
        AcctFlowWithBillStereoDTO dto = new AcctFlowWithBillStereoDTO();
        dto.setAccountFlowId(billFlowRelationAD.getAccountFlowId());
        String fundPlatform = billFlowRelationAD.getBankName();
        if (StringUtils.isNotEmpty(billFlowRelationAD.getBankName())) {
            // 众邦银行间连单独处理
            if (Objects.equals(fundPlatform, BankCoreConstant.ZBBANKH_CODE)) {
                dto.setFundPlatformName(BankCoreConstant.ZBBANKH_NAME);
            } else {
                dto.setFundPlatformName(FundPlatformEnum.findPlatformByCode(fundPlatform).getName());
            }
        }
        dto.setCompanyName(companyIdNameMap.getOrDefault(billFlowRelationAD.getCompanyId(), StringUtils.EMPTY));
        dto.setBankAccountNo(billFlowRelationAD.getBankAccountNo());
        dto.setBankTransNo(billFlowRelationAD.getBankTransNo());
        dto.setSyncBankTransNo(billFlowRelationAD.getSyncBankTransNo());
        Integer accountSubType = Optional.ofNullable(condition.getAccountSubType()).orElse(billFlowRelationAD.getAccountSubType());
        if (Objects.nonNull(accountSubType)) {
            dto.setAccountSubTypeName(FundAccountSubType.getEnum(accountSubType).getValue());
        }
        Integer accountModel = billFlowRelationAD.getAccountModel();
        if (Objects.nonNull(accountModel)) {
            dto.setAccountModelName(FundAccountModelType.getEnum(accountModel).getValue());
        }
        Integer tradeType = billFlowRelationAD.getTradeType();
        if (Objects.nonNull(tradeType) && Objects.nonNull(FundAcctTradeType.getEnum(tradeType))) {
            dto.setTradeTypeName(AcctAllFlowConvertDTO.getTradeTypeName(tradeType));
        }
        dto.setOperationType(billFlowRelationAD.getOperationType());
        dto.setOperationTypeName(AcctAllFlowConvertDTO.getOperationTypeDesc(billFlowRelationAD.getFlowFlag(), billFlowRelationAD.getOperationType()));
        dto.setShowTargetAcct(getShowTargetAcct(billFlowRelationAD.getBankAccountNo(), billFlowRelationAD.getTargetAccount(), accountSubType, billFlowRelationAD.getTargetBankAcctId(), billFlowRelationAD.getOperationType()));
        dto.setTargetAccount(billFlowRelationAD.getTargetAccount());

        if (BooleanUtils.isFalse(dto.getShowTargetAcct())) {
            dto.setTargetAccount(null);
        }
        dto.setTargetAccountName(billFlowRelationAD.getCompanyMainName());
        dto.setTargetBankAllName(BankNameEnum.getBankEnum(billFlowRelationAD.getTargetBankName()).getName());
        // 场景
        dto.setOrderType(Optional.ofNullable(billFlowRelationAD.getCategoryType()).map(CategoryTypeEnum::valueOf).map(CategoryTypeEnum::getName).orElse(StringUtils.EMPTY));
        dto.setBizNo(billFlowRelationAD.getBizId());
        dto.setOperationAmount(BigDecimalUtils.fen2yuan(billFlowRelationAD.getOperationAmount()));
        dto.setBalance(BigDecimalUtils.fen2yuan(billFlowRelationAD.getBalance()));
        // 操作渠道类型: 1:普通下单，2:后台回填（默认1)
        dto.setOperationUserName(billFlowRelationAD.getOperationUserName());
        Integer operationChannelType = billFlowRelationAD.getOperationChannelType();
        if (Objects.isNull(operationChannelType) || OperationChannelType.isStereo(operationChannelType)) {
            dto.setOperationUserName("分贝通");
        }
        dto.setOperationUserCompanyName(billFlowRelationAD.getOperationUserCompanyName());
        dto.setOperationUserCompanyId(billFlowRelationAD.getOperationUserCompanyId());
        dto.setCreateTime(DateUtils.format(billFlowRelationAD.getCreateTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        if (FundAcctSyncBankStatus.noSyncNew(billFlowRelationAD.getSyncBankStatus())) {
            dto.setSyncBankTime(null);
        } else {
            dto.setSyncBankTime(DateUtils.format(billFlowRelationAD.getSyncBankTime(), DateUtils.FORMAT_TIME_WITH_BAR));
        }
        dto.setRemark(billFlowRelationAD.getRemark());


        /*------------ 特殊逻辑--------------*/
        /**
         * 总账户，不应该显示
         * QX 2022-03-19
         */
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey().equals(billFlowRelationAD.getFlowFlag())) {
            dto.setTargetAccountName(null); // 如果是总账户，对手账户（户名）为空
            dto.setTargetAccount(null); // 如果是总账户，对手账户（账号）为空
            dto.setTargetBankAllName(null); // 如果是总账户，对手账户（银行）为空
        }
        /**
         * 对公付款，页面没有值
         * QX 2022-03-19
         */
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_PUBLIC_FLOW.getKey().equals(billFlowRelationAD.getFlowFlag())) {
            dto.setTargetBankAllName(billFlowRelationAD.getTargetBankFullName()); // 如果是对公付款账户，对手账户（银行）取这个值
        }

        // 虚拟卡
        if (AccountAllFlowFlagEnum.TB_ACCT_COMPANY_CARD_FLOW.getKey().equals(billFlowRelationAD.getFlowFlag())) {
            // 额度发放 / 额度退还, 展示对手账户详情
            if (StringUtils.isNotBlank(billFlowRelationAD.getTargetBankAcctId())) {
                dto.setTargetBankShow(billFlowRelationAD.getEmployeeName());
                dto.setShowTargetAcct(true);
                dto.setTargetAccount(billFlowRelationAD.getBankAccountNo());
                dto.setTargetAccountName(billFlowRelationAD.getEmployeeName());
                dto.setTargetBankAllName(BankNameEnum.getBankEnum(billFlowRelationAD.getBankName()).getName());
            }
            //兼容代码,落流水时有为空数据
            if (StringUtils.isBlank(dto.getOperationTypeName())) {
                dto.setOperationTypeName(FundAcctCompanyCardOptType.getEnum(billFlowRelationAD.getOperationType()).getValue());
            }
        }

        // 如果是汇总账户，设置为不显示 QX 2022-03-24
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey().equals(billFlowRelationAD.getFlowFlag())) {
            dto.setShowTargetAcct(false);
        }

        // 注意顺序，放到最后。 QX 2022-03-24
        dto.setTargetBankShow(AcctAllFlowConvertDTO.getTargetBankShow(billFlowRelationAD.getBankName(), billFlowRelationAD.getOperationType(), billFlowRelationAD.getBankAccountNo(), billFlowRelationAD.getTargetAccountSubType(), billFlowRelationAD.getTargetAccount(), dto.getTargetBankAllName(), billFlowRelationAD.getTargetBankName()));
        return dto;
    }
    
    /**
     * 字符串赋初值
     * @param src
     * @return
     */
    private static String initDefaultValueIfNull(String src) {
        return initDefaultValueIfNull(src, StringUtils.EMPTY);
    }
    
    /**
     * 字符串赋初值
     */
    private static String initDefaultValueIfNull(String src, String defaultValue) {
        if (StringUtils.isBlank(src) || StringUtils.equals("null", src)) {
            return defaultValue;
        }
        return src;
    }

    /**
     * 根据条件查询总数
     *
     * @param criteria
     * @return
     */
    public Long queryBillFlowCount(HoloAdsAccountAllFlowADExample.Criteria criteria) {
        HoloAdsAccountAllFlowADExample flowADCountExample = new HoloAdsAccountAllFlowADExample();
        flowADCountExample.getOredCriteria().add(criteria);
        return accountAllFlowADManager.queryCount(flowADCountExample);
    }

    /**
     * 根据条件查询分页数据
     *
     * @param criteria
     * @param condition
     * @return
     */
    public List<AcctFlowBaseDTO> queryBillFlowPageData(HoloAdsAccountAllFlowADExample.Criteria criteria, BillFlowPageQuery condition) {
        // 账户类型是红包券的情况下，平台方必须是分贝通，否则不返回数据
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey().equals(condition.getFlowFlag()) && 
            StringUtils.isNotBlank(condition.getFundPlatform()) && 
            !Objects.equals(FundPlatformEnum.FBT.getCode(), condition.getFundPlatform())) {
            return Collections.emptyList();
        }

        HoloAdsAccountAllFlowADExample flowADIdsExample = new HoloAdsAccountAllFlowADExample();
        flowADIdsExample.getOredCriteria().add(criteria);
        flowADIdsExample.setOrderByClause("create_time desc, account_flow_id desc");
        flowADIdsExample.setOffset(condition.getPageNo());
        flowADIdsExample.setLimit(condition.getPageSize());
        List<HoloAdsAccountAllFlowAD> accountFlowEntityList = accountAllFlowADManager.queryEntityByCondition(flowADIdsExample);
        if (CollectionUtils.isEmpty(accountFlowEntityList)) {
            return Collections.emptyList();
        }
        FinhubLogger.info("AccountBillFlowService#queryBillFlowPageData#condition:{}", JSON.toJSONString(condition));
        List<AcctFlowBaseDTO> accountFlowDTOList;
        if (condition.isStereo()) {
            // 补充企业名称信息
            accountFlowDTOList = new ArrayList<>();
            Map<String, String> companyIdNameMap = new HashMap<>();
            List<String> companyIdList = accountFlowEntityList.stream().map(HoloAdsAccountAllFlowAD::getCompanyId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyIdList)) {
                List<CompanyNew> companyNews = queryCompanyNewListByCompanyIds(companyIdList);
                companyIdNameMap = Optional.ofNullable(companyNews).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CompanyNew::getCompanyReferenceId, CompanyNew::getCompanyName));
            }
            // 数据库结构实体转成内部实体
            for (HoloAdsAccountAllFlowAD allFlowAD : accountFlowEntityList) {
                accountFlowDTOList.add(convertDbEntity2StereoDto(allFlowAD, companyIdNameMap, condition));
            }
        } else {
            FinhubLogger.info("AccountBillFlowService#queryBillFlowPageData#convertDbEntity2WebDto");
            // 数据库结构实体转成内部实体
            accountFlowDTOList = accountFlowEntityList.stream().map(flowAD -> convertDbEntity2WebDto(flowAD, condition)).collect(Collectors.toList());
        }

        List<String> accountFlowIdList = accountFlowEntityList.stream().map(HoloAdsAccountAllFlowAD::getAccountFlowId).distinct().collect(Collectors.toList());
        Map<String, List<HoloAdsBillFlowRelationAD>> billFlowId2EntityMap = accountBillFlowRelationADManager.querySummaryInfoByAccountFlowIdsMap(accountFlowIdList);

        for (AcctFlowBaseDTO baseDTO : accountFlowDTOList) {
            List<HoloAdsBillFlowRelationAD> summaryInfoList = billFlowId2EntityMap.get(baseDTO.getAccountFlowId());
            if (CollectionUtils.isEmpty(summaryInfoList)) {
                Integer operationType = baseDTO.getOperationType();
                if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                    baseDTO.addSummaryInfoBean("无账单");
                } else {
                    baseDTO.addSummaryInfoBean("未入账单");
                }
                continue;
            }

            for (HoloAdsBillFlowRelationAD summaryInfo : summaryInfoList) {
                if (StringUtils.isNotBlank(summaryInfo.getBillNo())) {
                    baseDTO.addSummaryInfoBean(summaryInfo.getBillNo(),
                            summaryInfo.getOrderId(),
                            summaryInfo.getProductId(),
                            summaryInfo.getBillDate(),
                            summaryInfo.getAmtCompanyAccountPay(),
                            summaryInfo.getUserVisibleState());
                } else {
                    Integer operationType = baseDTO.getOperationType();
                    if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                        baseDTO.addSummaryInfoBean("无账单");
                    } else {
                        baseDTO.addSummaryInfoBean("未入账单");
                    }
                }
            }
        }
        return accountFlowDTOList;
    }
    
    /**
     * 分页查询所有符合条件的记录
     * @param param
     * @return
     */
    public List<AcctFlowBaseDTO> queryBillFlowData4Export(BillFlowPageQuery param){
        List<AcctFlowBaseDTO> result = Lists.newArrayList();
        param.setPageSize(1000);
        HoloAdsAccountAllFlowADExample.Criteria criteria = buildBillFlowSearchForWeb(param);
        List<AcctFlowBaseDTO> list = queryBillFlowPageData4Export(criteria, param);
        while (CollectionUtils.isNotEmpty(list)) {
            result.addAll(list);
            param.setPageIndex(param.getPageIndex() + 1);
            list = queryBillFlowPageData4Export(criteria, param);
        }
        
        return result;
    }

    //TODO 下次再优化提取,本次只做转换 convertDbEntity2WebDto4Export
    public List<AcctFlowBaseDTO> queryBillFlowPageData4Export(HoloAdsAccountAllFlowADExample.Criteria criteria, BillFlowPageQuery condition){
        // 账户类型是红包券的情况下，平台方必须是分贝通，否则不返回数据
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey().equals(condition.getFlowFlag()) && 
            StringUtils.isNotBlank(condition.getFundPlatform()) && 
            !Objects.equals(FundPlatformEnum.FBT.getCode(), condition.getFundPlatform())) {
            return Collections.emptyList();
        }

        HoloAdsAccountAllFlowADExample flowADIdsExample = new HoloAdsAccountAllFlowADExample();
        flowADIdsExample.getOredCriteria().add(criteria);
        flowADIdsExample.setOrderByClause("create_time desc, account_flow_id desc");
        flowADIdsExample.setOffset(condition.getPageNo());
        flowADIdsExample.setLimit(condition.getPageSize());
        List<HoloAdsAccountAllFlowAD> accountFlowEntityList = accountAllFlowADManager.queryEntityByCondition(flowADIdsExample);
        if (CollectionUtils.isEmpty(accountFlowEntityList)) {
            return Collections.emptyList();
        }

        FinhubLogger.info("AccountBillFlowService#queryBillFlowPageData4Export#flowADIdsExample:{}", JSON.toJSONString(flowADIdsExample));
        List<AcctFlowBaseDTO> accountFlowDTOList;
        if (condition.isStereo()) {
            // 补充企业名称信息
            accountFlowDTOList = new ArrayList<>();
            Map<String, String> companyIdNameMap = new HashMap<>();
            List<String> companyIdList = accountFlowEntityList.stream()
                .map(HoloAdsAccountAllFlowAD :: getCompanyId)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyIdList)) {
                List<CompanyNew> companyNews = queryCompanyNewListByCompanyIds(companyIdList);
                companyIdNameMap = Optional.ofNullable(companyNews)
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(CompanyNew :: getCompanyReferenceId, CompanyNew :: getCompanyName));
            }
            // 数据库结构实体转成内部实体
            for (HoloAdsAccountAllFlowAD allFlowAD : accountFlowEntityList) {
                accountFlowDTOList.add(convertDbEntity2StereoDto(allFlowAD, companyIdNameMap, condition));
            }
        } else {
            FinhubLogger.info("AccountBillFlowService#queryBillFlowPageData4Export#convertDbEntity2WebDto4Export");
            // 数据库结构实体转成内部实体
            accountFlowDTOList = accountFlowEntityList.stream()
                .map(flowAD -> convertDbEntity2WebDto4Export(flowAD, condition))
                .collect(Collectors.toList());
        }

        List<String> accountFlowIdList = accountFlowEntityList.stream().map(HoloAdsAccountAllFlowAD::getAccountFlowId).distinct().collect(Collectors.toList());
        Map<String, List<HoloAdsBillFlowRelationAD>> billFlowId2EntityMap = accountBillFlowRelationADManager.querySummaryInfoByAccountFlowIdsMap(accountFlowIdList);

        for (AcctFlowBaseDTO baseDTO : accountFlowDTOList) {
            List<HoloAdsBillFlowRelationAD> summaryInfoList = billFlowId2EntityMap.get(baseDTO.getAccountFlowId());
            if (CollectionUtils.isEmpty(summaryInfoList)) {
                Integer operationType = baseDTO.getOperationType();
                if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                    baseDTO.addSummaryInfoBean("无账单");
                } else {
                    baseDTO.addSummaryInfoBean("未入账单");
                }
                continue;
            }

            for (HoloAdsBillFlowRelationAD summaryInfo : summaryInfoList) {
                if (StringUtils.isNotBlank(summaryInfo.getBillNo())) {
                    baseDTO.addSummaryInfoBean(summaryInfo.getBillNo(),
                            summaryInfo.getOrderId(),
                            summaryInfo.getProductId(),
                            summaryInfo.getBillDate(),
                            summaryInfo.getAmtCompanyAccountPay(),
                            summaryInfo.getUserVisibleState());
                } else {
                    Integer operationType = baseDTO.getOperationType();
                    if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                        baseDTO.addSummaryInfoBean("无账单");
                    } else {
                        baseDTO.addSummaryInfoBean("未入账单");
                    }
                }
            }
        }
        return accountFlowDTOList;
    }

    /**
     * 页面列表页查询，包括总数和分页数据
     *
     * @param criteria
     * @param condition
     * @return
     */
    private Map<String, Object> queryBillFlowList(HoloAdsAccountAllFlowADExample.Criteria criteria, BillFlowPageQuery condition) {
        Map<String, Object> result = new HashMap<>();
        Long count = queryBillFlowCount(criteria);
        if (count < 1) {
            result.put("count", count);
            result.put("data", Collections.emptyList());
            return result;
        }

        List<AcctFlowBaseDTO> data = queryBillFlowPageData(criteria, condition);
        result.put("count", count);
        result.put("data", data);
        return result;
    }

    /**
     * stereo 流水查询条件构造
     *
     * @param query
     * @return
     */
    @Deprecated
    public HoloAdsAccountAllFlowADExample.Criteria buildBillFlowSearchForStereo(BillFlowPageQuery query) {
        // 账户类型
        Integer accountSubType = query.getAccountSubType();
        boolean isFlowTableHaveNoAccountSubType = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey(), query.getFlowFlag())
                || Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_PUBLIC_FLOW.getKey(), query.getFlowFlag())
                || Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoAccountSubType) {
            accountSubType = null;
        }

        boolean isFlowTableHaveNoOrderType = Objects.equals(AccountAllFlowFlagEnum.TB_ACCOUNT_GENERAL_FLOW.getKey(), query.getFlowFlag());
        if (isFlowTableHaveNoOrderType) {
            query.setOrderTypes(null);
        }

        HoloAdsAccountAllFlowADExample.Criteria criteria = new HoloAdsAccountAllFlowADExample.Criteria();
        //操作时间
        if (Objects.nonNull(query.getStartTime()) && Objects.nonNull(query.getEndTime())) {
            criteria.andCreateTimeBetween(query.getStartTime(), query.getEndTime());
        }

        // 平台方
        // 虽然库里有 fund_platform 字段，但没有使用。
        // 在 com.fenbeitong.fenbeipay.rpc.service.acct.flow.IAcctFlowServiceImpl#acctFlowSearchStereoPage 业务代码里面实际将 fundPlatform 映射到了 bank_name 列。
        String fundPlatform = query.getFundPlatform();
        if (StringUtils.isNotBlank(fundPlatform)) {
            criteria.andBankNameEqualTo(fundPlatform);
        }

        // 业务账户类型
        Integer accountModel = query.getAccountModel();
        if (Objects.nonNull(accountModel)) {
            criteria.andAccountModelEqualTo(accountModel.longValue());
        }

        // 账户类型
        if (Objects.nonNull(accountSubType)) {
            criteria.andAccountSubTypeEqualTo(accountSubType);
        }

        if (Objects.nonNull(query.getFlowFlag())) {
            criteria.andFlowFlagEqualTo(query.getFlowFlag());
        }

        // 企业
        if (StringUtils.isNotBlank(query.getCompanyId())) {
            criteria.andCompanyIdEqualTo(query.getCompanyId());
        }

        // 交易类型
        if (CollectionUtils.isNotEmpty(query.getTradeTypes())) {
            criteria.andTradeTypeIn(query.getTradeTypes());
        }

        // 业务类型
        if (CollectionUtils.isNotEmpty(query.getOperationTypes())) {
            criteria.andOperationTypeIn(query.getOperationTypes());
        }

        // 场景
        if (CollectionUtils.isNotEmpty(query.getOrderTypes())) {
            criteria.andCategoryTypeIn(query.getOrderTypes());
        }

        // 交易编码
        if (StringUtils.isNotBlank(query.getAccountFlowId())) {
            criteria.andAccountFlowIdEqualTo(query.getAccountFlowId());
        }

        //请求银行订单号（原：平台上帐流水号）
        if (StringUtils.isNotBlank(query.getSyncBankTransNo())) {
            criteria.andSyncBankTransNoEqualTo(query.getSyncBankTransNo());
        }
        // 银行流水号（新增）
        if (StringUtils.isNotBlank(query.getBankTransNo())) {
            criteria.andBankTransNoEqualTo(query.getBankTransNo());
        }

        // 相关单号（新增）
        if (StringUtils.isNotBlank(query.getBizNo())) {
            criteria.andBizIdEqualTo(query.getBizNo());
        }

        //操作人
        if (StringUtils.isNotBlank(query.getOperationUserName())) {
            criteria.andOperationUserNameLike("%" + query.getOperationUserName() + "%");
        }

        return criteria;
    }

    /**
     * web 流水查询条件构造
     *
     * @param query
     * @return
     */
    public HoloAdsAccountAllFlowADExample.Criteria buildBillFlowSearchForWeb(BillFlowPageQuery query) {
        HoloAdsAccountAllFlowADExample.Criteria criteria = new HoloAdsAccountAllFlowADExample.Criteria();
        //操作时间
        if (Objects.nonNull(query.getStartTime()) && Objects.nonNull(query.getEndTime())) {
            criteria.andCreateTimeBetween(query.getStartTime(), query.getEndTime());
        }
        // 企业
        criteria.andCompanyIdEqualTo(query.getCompanyId());

        // 银行名称
        if (StringUtils.isNotBlank(query.getBankName())) {
            criteria.andBankNameEqualTo(query.getBankName());
        }

        // 银行卡号
        if (StringUtils.isNotBlank(query.getBankAccountNo())) {
            criteria.andBankAccountNoEqualTo(query.getBankAccountNo());
        }

        // 账户模式
        if (Objects.nonNull(query.getAccountModel())) {
            criteria.andAccountModelEqualTo(query.getAccountModel().longValue());
        }

        // 业务账户类型
        if (Objects.nonNull(query.getAccountSubType())) {
            criteria.andAccountSubTypeEqualTo(query.getAccountSubType());
        }

        // 交易类型
        if (CollectionUtils.isNotEmpty(query.getTradeTypes())) {
            criteria.andTradeTypeIn(query.getTradeTypes());
        } else {
            criteria.andTradeTypeIn(FundAcctTradeType.businessDebitTypes());
        }

        // 业务类型
        if (CollectionUtils.isNotEmpty(query.getOperationTypes())) {
            criteria.andOperationTypeIn(query.getOperationTypes());
        } else {
            criteria.andOperationTypeIn(FundAcctDebitOptType.businessDebitFlowOptWebEnums());
        }

        // 交易编码
        if (StringUtils.isNotBlank(query.getAccountFlowId())) {
            criteria.andAccountFlowIdEqualTo(query.getAccountFlowId());
        }

        // 电子回单状态
        if (Objects.nonNull(query.getReceiptStatus())) {
            criteria.andReceiptStatusEqualTo(query.getReceiptStatus());
        }

        // 操作人
        if (StringUtils.isNotBlank(query.getOperationUserName())) {
            criteria.andOperationUserNameLike("%" + query.getOperationUserName() + "%");
        }

        // 操作人企业
        if (StringUtils.isNotBlank(query.getOperationUserCompanyId())) {
            criteria.andOperationUserCompanyIdEqualTo(query.getOperationUserCompanyId());
        }

        // 银行流水号
        if (StringUtils.isNotBlank(query.getBankTransNo())) {
            criteria.andBankTransNoEqualTo(query.getBankTransNo());
        }

        // 请求银行订单号
        if (StringUtils.isNotBlank(query.getSyncBankTransNo())) {
            criteria.andSyncBankTransNoEqualTo(query.getSyncBankTransNo());
        }
        
        if (CollectionUtils.isNotEmpty(query.getOrderTypes())) {
        	criteria.andCategoryTypeIn(query.getOrderTypes());
        }
        
        if (CollectionUtils.isNotEmpty(query.getTargetAccounts())) {
        	criteria.andTargetAccountIn(query.getTargetAccounts());
        }
        
        if (Objects.nonNull(query.getOrderType())) {
    		criteria.andCategoryTypeIn(CategoryTypeMappingEnum.mapCategoryType(query.getOrderType()));
    	}
                
        return criteria;
    }

    public Map<String, Object> queryBillFlowForWeb(BillFlowPageQuery condition) {
        FinhubLogger.info("AccountBillFlowService#queryBillFlowForWeb#req:{}", JSON.toJSONString(condition));
        // 新增账单编号字段，如果为空保持原有查询逻辑
        if (StringUtils.isBlank(condition.getBillNo())) {
            HoloAdsAccountAllFlowADExample.Criteria criteria = buildBillFlowSearchForWeb(condition);
            return queryBillFlowList(criteria, condition);
        } else {
            FinhubLogger.info("企业web商务消费账户流水查询账单流水，账单编号不为空走新逻辑");
            return queryBillFlowListWithBillNo(condition);
        }
    }

    private Map<String, Object> queryBillFlowListWithBillNo(BillFlowPageQuery condition) {
        Map<String, Object> result = new HashMap<>();
        HoloAdsAccountAllFlowADExample.Criteria criteria = buildBillFlowSearchForWeb(condition);
        Long count = queryBillFlowListWithBillNoPageCount(criteria, condition);
        if (count < 1) {
            result.put("count", count);
            result.put("data", Collections.emptyList());
            return result;
        }
        List<AcctFlowBaseDTO> data = queryBillFlowListWithBillNoPageData(criteria, condition);
        result.put("count", count);
        result.put("data", data);
        return result;
    }

    private Long queryBillFlowListWithBillNoPageCount(HoloAdsAccountAllFlowADExample.Criteria criteria, BillFlowPageQuery condition) {
        // 账户类型是红包券的情况下，平台方必须是分贝通，否则不返回数据
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey().equals(condition.getFlowFlag()) && 
            StringUtils.isNotBlank(condition.getFundPlatform()) && 
            !Objects.equals(FundPlatformEnum.FBT.getCode(), condition.getFundPlatform())) {
            return 0L;
        }
        // 先查询账单流水表
        List<HoloAdsBillFlowRelationAD> billFlow = queryBillFlowListByBillNo(condition);
        if (CollectionUtils.isEmpty(billFlow)) {
            return 0L;
        }
        // 账单下所有流水id作为查询条件
        List<String> accountFlowIds = billFlow.stream()
            .map(HoloAdsBillFlowRelationAD :: getAccountFlowId)
            .collect(Collectors.toList());
        criteria.andAccountFlowIdIn(accountFlowIds);
        // 查询流水条数
        return queryBillFlowCount(criteria);
    }

    private List<AcctFlowBaseDTO> queryBillFlowListWithBillNoPageData(HoloAdsAccountAllFlowADExample.Criteria criteria, BillFlowPageQuery condition) {
        // 账户类型是红包券的情况下，平台方必须是分贝通，否则不返回数据
        if (AccountAllFlowFlagEnum.TB_ACCOUNT_REDCOUPON_FLOW.getKey().equals(condition.getFlowFlag()) && 
            StringUtils.isNotBlank(condition.getFundPlatform()) && 
            !Objects.equals(FundPlatformEnum.FBT.getCode(), condition.getFundPlatform())) {
            return Collections.emptyList();
        }
        // 先查询账单流水表
        List<HoloAdsBillFlowRelationAD> billFlow = queryBillFlowListByBillNo(condition);
        if (CollectionUtils.isEmpty(billFlow)) {
            return Collections.emptyList();
        }
        // 账单下所有流水id作为查询条件
        List<String> accountFlowIds = billFlow.stream()
            .map(HoloAdsBillFlowRelationAD :: getAccountFlowId)
            .collect(Collectors.toList());
        criteria.andAccountFlowIdIn(accountFlowIds);
        // 查询流水
        HoloAdsAccountAllFlowADExample flowADIdsExample = new HoloAdsAccountAllFlowADExample();
        flowADIdsExample.getOredCriteria().add(criteria);
        flowADIdsExample.setOrderByClause("create_time desc, account_flow_id desc");
        flowADIdsExample.setOffset(condition.getPageNo());
        flowADIdsExample.setLimit(condition.getPageSize());
        List<HoloAdsAccountAllFlowAD> accountFlowEntityList = accountAllFlowADManager.queryEntityByCondition(flowADIdsExample);
        if (CollectionUtils.isEmpty(accountFlowEntityList)) {
            return Collections.emptyList();
        }

        List<AcctFlowBaseDTO> accountFlowDTOList;
        if (condition.isStereo()) {
            // 补充企业名称信息
            accountFlowDTOList = new ArrayList<>();
            Map<String, String> companyIdNameMap = new HashMap<>();
            List<String> companyIdList = accountFlowEntityList.stream()
                .map(HoloAdsAccountAllFlowAD :: getCompanyId)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(companyIdList)) {
                List<CompanyNew> companyNews = queryCompanyNewListByCompanyIds(companyIdList);
                companyIdNameMap = Optional.ofNullable(companyNews)
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(CompanyNew :: getCompanyReferenceId, CompanyNew :: getCompanyName));
            }
            // 数据库结构实体转成内部实体
            for (HoloAdsAccountAllFlowAD allFlowAD : accountFlowEntityList) {
                accountFlowDTOList.add(convertDbEntity2StereoDto(allFlowAD, companyIdNameMap, condition));
            }
        } else {
            // 数据库结构实体转成内部实体
            accountFlowDTOList = accountFlowEntityList.stream()
                .map(flowAD -> convertDbEntity2WebDto(flowAD, condition))
                .collect(Collectors.toList());
        }
        // 将流水账单的查询结果转化为map
        Map<String, List<HoloAdsBillFlowRelationAD>> billFlowId2EntityMap = billFlow.stream()
            .collect(Collectors.groupingBy(HoloAdsBillFlowRelationAD :: getAccountFlowId));
        for (AcctFlowBaseDTO baseDTO : accountFlowDTOList) {
            List<HoloAdsBillFlowRelationAD> summaryInfoList = billFlowId2EntityMap.get(baseDTO.getAccountFlowId());
            if (CollectionUtils.isEmpty(summaryInfoList)) {
                Integer operationType = baseDTO.getOperationType();
                if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || 
                    Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                    baseDTO.addSummaryInfoBean("无账单");
                } else {
                    baseDTO.addSummaryInfoBean("未入账单");
                }
                continue;
            }

            for (HoloAdsBillFlowRelationAD summaryInfo : summaryInfoList) {
                if (StringUtils.isNotBlank(summaryInfo.getBillNo())) {
                    baseDTO.addSummaryInfoBean(summaryInfo.getBillNo(),
                            summaryInfo.getOrderId(),
                            summaryInfo.getProductId(),
                            summaryInfo.getBillDate(),
                            summaryInfo.getAmtCompanyAccountPay(),
                            summaryInfo.getUserVisibleState());
                } else {
                    Integer operationType = baseDTO.getOperationType();
                    if (Objects.equals(FundAcctDebitOptType.TRANSFER_INTO.getKey(), operationType) || 
                        Objects.equals(FundAcctDebitOptType.TRANSFER_OUT.getKey(), operationType)) {
                        baseDTO.addSummaryInfoBean("无账单");
                    } else {
                        baseDTO.addSummaryInfoBean("未入账单");
                    }
                }
            }
        }
        return accountFlowDTOList;
    }

    private List<HoloAdsBillFlowRelationAD> queryBillFlowListByBillNo(BillFlowPageQuery condition) {
        HoloAdsBillFlowRelationADExample example = new HoloAdsBillFlowRelationADExample();
        HoloAdsBillFlowRelationADExample.Criteria criteria = example.createCriteria();
        criteria.andBillNoEqualTo(condition.getBillNo());
        //操作时间
        if (Objects.nonNull(condition.getStartTime()) && Objects.nonNull(condition.getEndTime())) {
            criteria.andCreateTimeBetween(condition.getStartTime(), condition.getEndTime());
        }
        // 企业
        criteria.andCompanyIdEqualTo(condition.getCompanyId());

        // 银行名称
        if (StringUtils.isNotBlank(condition.getBankName())) {
            criteria.andBankNameEqualTo(condition.getBankName());
        }

        // 银行卡号
        if (StringUtils.isNotBlank(condition.getBankAccountNo())) {
            criteria.andBankAccountNoEqualTo(condition.getBankAccountNo());
        }
        // 业务账户类型
        if (Objects.nonNull(condition.getAccountModel())) {
            criteria.andAccountModelEqualTo(condition.getAccountModel().longValue());
        }
        example.setOrderByClause("create_time desc");
        return accountBillFlowRelationADManager.queryEntityByCondition(example);
    }
    
    public List<CompanyNew> queryCompanyNewListByCompanyIds(List<String> companyIdList) {
        if (CollectionUtils.isEmpty(companyIdList)) {
            return Collections.emptyList();
        }
        CompanyNewReqDTO dto = new CompanyNewReqDTO();
        dto.setCompanyIds(companyIdList);
        List<CompanyNew> companyNewList = iCompanyInfoService.getCompanyNewList(dto);
        if (CollectionUtils.isEmpty(companyNewList)) {
            return Collections.emptyList();
        }
        return companyNewList;
    }

}