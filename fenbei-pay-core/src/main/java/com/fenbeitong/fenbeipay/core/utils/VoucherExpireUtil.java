package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.ExpireInfo;

import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2022/12/20 20:27
 * @description:
 */
public class VoucherExpireUtil {
    /**
     * 中文 天/小时/分钟
     */
    private final static String ZH_DAY = "天";
    private final static String ZH_HOUR = "小时";
    private final static String ZH_MINUTE = "分钟";

    /**
     * 一秒的毫秒数
     */
    public static final Long LONG_MS_SECOND = 1000L;
    /**
     * 一分钟的毫秒数
     */
    public static final Long LONG_MS_MINUTES = 60L * LONG_MS_SECOND;

    /**
     * 当前时间距离分贝券过期时间介于72-96小时，显示剩3天到期；介于48-72小时，显示剩2天到期；介于24-48小时，显示剩1天到期；
     * 小于24小时，显示剩xx小时到期，小时数直接取整数；
     * 小于1小时，显示剩xx分钟到期，分钟数直接取整，
     * 小于1小分钟显示0分钟。
     * 如剩xx到期和“新”有冲突，显示“剩xx到期”，不显示“新”
     */

    public static final Long MINUTES = 60L;

    public static final Long DAY_1_MINUTES = 1L * 24 * MINUTES;
    public static final Long DAY_2_MINUTES = 2L * 24 * MINUTES;
    public static final Long DAY_3_MINUTES = 3L * 24 * MINUTES;
    public static final Long DAY_4_MINUTES = 4L * 24 * MINUTES;

    public static ExpireInfo getExpireInfo(Date expiryTime){
        if (expiryTime == null){
            return null;
        }
        Long minutes = getNumberBetweenDates(null, expiryTime, new Date(), VoucherExpireUtil.LONG_MS_MINUTES);
        if (minutes == 0){
            return ExpireInfo.builder().unit(ZH_MINUTE).time(0L).build();
        }else if (minutes <= MINUTES){
            return ExpireInfo.builder().unit(ZH_MINUTE).time(minutes).build();
        }else if (minutes <= DAY_1_MINUTES){
            return ExpireInfo.builder().unit(ZH_HOUR).time(minutes / MINUTES).build();
        }else if (minutes <= DAY_2_MINUTES){
            return ExpireInfo.builder().unit(ZH_DAY).time(1L).build();
        }else if (minutes <= DAY_3_MINUTES){
            return ExpireInfo.builder().unit(ZH_DAY).time(2L).build();
        }else if (minutes <= DAY_4_MINUTES){
            return ExpireInfo.builder().unit(ZH_DAY).time(3L).build();
        }else {
            return null;
        }
    }

    /**
     * 获得两个日期差多少单位时间
     *
     * @param timeZone  时区
     * @param endDate   结束时间
     * @param beginDate 开始时间
     * @param unitMS    单位毫秒
     * @return Long         数量
     */
    public static Long getNumberBetweenDates(TimeZone timeZone, Date endDate, Date beginDate, Long unitMS) {
        if (beginDate == null || endDate == null) {
            throw new IllegalArgumentException("Date cannot be null.");
        }
        if (timeZone == null) {
            timeZone = TimeZone.getDefault();
        }
        long beginOffset = timeZone.getRawOffset();
        long endOffset = beginOffset;
        if (timeZone.inDaylightTime(beginDate)) {
            beginOffset += timeZone.getDSTSavings();
        }
        if (timeZone.inDaylightTime(endDate)) {
            endOffset += timeZone.getDSTSavings();
        }
        long endDays = (endDate.getTime() + endOffset) / unitMS;
        long beginDays = (beginDate.getTime() + beginOffset) / unitMS;
        return endDays - beginDays;
    }

}
