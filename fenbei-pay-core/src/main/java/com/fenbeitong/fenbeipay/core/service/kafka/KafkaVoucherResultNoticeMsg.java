package com.fenbeitong.fenbeipay.core.service.kafka;

import com.fenbeitong.finhub.kafka.annotation.FinhubKafkaMsgHeader;
import com.fenbeitong.finhub.kafka.constant.KafkaOrderTypeEnum;
import com.fenbeitong.finhub.kafka.msg.IMessage;
import lombok.Data;
import lombok.ToString;

/**
 * 分贝券结果推送消息生产者消息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/11
 */
@Data
@ToString
@FinhubKafkaMsgHeader( orderType = KafkaOrderTypeEnum.system)
public class KafkaVoucherResultNoticeMsg extends IMessage {
    /**
     * 分贝券任务Id
     */
    private String vouchersTaskId;
    /**
     * 业务类型：{@link com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType}
     */
    private Integer vouchersTaskType;

    /**
     * 分贝券任务发放状态  {@link com.fenbeitong.fenbeipay.core.enums.voucher.VoucherStatus}
     */
    private Integer status;

    /**
     *   业务Id
     */
    private String bizNo;

    /**
     *   任务失败原因
     */
    private String taskFailureReasons;
}