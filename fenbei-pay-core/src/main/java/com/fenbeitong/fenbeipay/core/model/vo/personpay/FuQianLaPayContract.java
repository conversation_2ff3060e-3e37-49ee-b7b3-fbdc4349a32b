package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by mac on 17/12/19.
 */
@Deprecated
public class FuQianLaPayContract implements Serializable {

    private String app_id;
    private Charge charge;
    private String sign_type; //签名类型
    ////支付状态 1:支付成功 2:支付中
    private Integer needPay;

    public String getApp_id() {
        return app_id;
    }

    public void setApp_id(String app_id) {
        this.app_id = app_id;
    }

    public Charge getCharge() {
        return charge;
    }

    public void setCharge(Charge charge) {
        this.charge = charge;
    }

    public String getSign_type() {
        return sign_type;
    }

    public void setSign_type(String sign_type) {
        this.sign_type = sign_type;
    }

    public Integer getNeedPay() {
        return needPay;
    }

    public void setNeedPay(Integer needPay) {
        this.needPay = needPay;
    }

    public static class Charge {
        private String order_no;
        private String currency;
        private BigDecimal orderAmount; //订单总金额
        private BigDecimal companyAmount;//企业支付
        private BigDecimal companyCardAmount; //企业优惠劵
        private BigDecimal companyActualAmount; //企业实际支付
        private BigDecimal amount; //员工支付金额
        private BigDecimal limitPrice; //员工支付金额
        private String subject;
        private String body;
        private String optional;
        private String notifyUrl;

        public String getOrder_no() {
            return order_no;
        }

        public void setOrder_no(String order_no) {
            this.order_no = order_no;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public BigDecimal getOrderAmount() {
            return orderAmount;
        }

        public void setOrderAmount(BigDecimal orderAmount) {
            this.orderAmount = orderAmount;
        }

        public BigDecimal getCompanyAmount() {
            return companyAmount;
        }

        public void setCompanyAmount(BigDecimal companyAmount) {
            this.companyAmount = companyAmount;
        }

        public BigDecimal getCompanyCardAmount() {
            return companyCardAmount;
        }

        public void setCompanyCardAmount(BigDecimal companyCardAmount) {
            this.companyCardAmount = companyCardAmount;
        }

        public BigDecimal getCompanyActualAmount() {
            return companyActualAmount;
        }

        public void setCompanyActualAmount(BigDecimal companyActualAmount) {
            this.companyActualAmount = companyActualAmount;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getSubject() {
            return subject;
        }

        public void setSubject(String subject) {
            this.subject = subject;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }

        public String getOptional() {
            return optional;
        }

        public void setOptional(String optional) {
            this.optional = optional;
        }

        public String getNotifyUrl() {
            return notifyUrl;
        }

        public void setNotifyUrl(String notifyUrl) {
            this.notifyUrl = notifyUrl;
        }

        public BigDecimal getLimitPrice() {
            return limitPrice;
        }

        public void setLimitPrice(BigDecimal limitPrice) {
            this.limitPrice = limitPrice;
        }
    }
}
