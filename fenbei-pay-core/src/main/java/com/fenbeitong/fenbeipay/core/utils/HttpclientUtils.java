package com.fenbeitong.fenbeipay.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.constant.core.CoreConstant;
import com.fenbeitong.finhub.common.constant.FinhubMessageCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Maps;
import com.luastar.swift.base.config.PropertyUtils;
import com.luastar.swift.base.net.HttpClientUtils;
import com.luastar.swift.base.net.HttpResult;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;

import java.util.HashMap;
import java.util.Map;

/**
 * httpcleint工具
 */
public class HttpclientUtils {

    private static final String BASE_URL = PropertyUtils.getString("api.hyperloop.host", "");
    private static final String TOKEN_URL = BASE_URL + "/auth/credentials";
    private static final String TOKEN_JSON = PropertyUtils.getString("api.json.param", "");

    /**
     * 获取hyperloop项目的token
     *
     * @return
     * @throws Exception
     */
    public static String getToken() {
        HttpResult result = HttpClientUtils.postBodyHttpResult(TOKEN_URL, TOKEN_JSON);
        JSONObject jsonObject = parseHttpResult(result);
        if (jsonObject != null) {
            JSONObject data = jsonObject.getJSONObject("data");
            return data.getString("token");
        }
        return null;
    }

    public static JSONObject get(String url) throws FinhubException {
        HttpResult result = HttpClientUtils.getHttpResult(url);
        return parseHttpResult(result);
    }

    public static JSONObject get(String url, String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put(CoreConstant.KEY_NAME_TOKEN, token);
        HttpResult result = HttpClientUtils.getHttpResult(url, headers);
        return parseHttpResult(result);
    }

    public static String getResult(String url) {
        return HttpClientUtils.get(url);
    }

    public static String getResult(String url, String token) {
        Map<String, String> headMap = Maps.newLinkedHashMap();
        headMap.put(CoreConstant.KEY_NAME_TOKEN, token);
        return HttpClientUtils.get(url, 60000, "UTF-8", headMap);
    }

    public static String post(String url, Map<String, String> paramMap) {
        return HttpClientUtils.post(url, paramMap);
    }

    public static String post(String url, Map<String, String> paramMap, String token) {
        Map<String, String> headMap = Maps.newLinkedHashMap();
        headMap.put(CoreConstant.KEY_NAME_TOKEN, token);
        return HttpClientUtils.post(url, paramMap, headMap);
    }

    public static JSONObject postJSON(String url, JSONObject params) throws FinhubException {
        return postJSON(url, params.toJSONString());
    }

    public static JSONObject postJSON(String url, String json) throws FinhubException {
        HttpResult result = HttpClientUtils.postBodyHttpResult(url, json);
        return parseHttpResult(result);
    }

    public static JSONObject postJSON(String url, JSONObject params, String token) throws FinhubException {
        return postJSON(url, params.toJSONString(), token);
    }

    public static JSONObject postJSON(String url, String json, String token) throws FinhubException {
        Map<String, String> headMap = Maps.newLinkedHashMap();
        headMap.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
        headMap.put(CoreConstant.KEY_NAME_TOKEN, token);
        HttpResult result = HttpClientUtils.postBodyHttpResult(url, json, headMap);
        return parseHttpResult(result);
    }

    public static String postJSONResult(String url, JSONObject params) {
        return postJSONResult(url, params.toJSONString());
    }

    public static String postJSONResult(String url, String json) {
        Map<String, String> headMap = Maps.newLinkedHashMap();
        headMap.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
        return HttpClientUtils.postBody(url, json);
    }

    public static String postJSONResult(String url, JSONObject params, String token) {
        return postJSONResult(url, params.toJSONString(), token);
    }

    public static String postJSONResult(String url, String json, String token) {
        Map<String, String> headMap = Maps.newLinkedHashMap();
        headMap.put(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
        headMap.put(CoreConstant.KEY_NAME_TOKEN, token);
        return HttpClientUtils.postBody(url, json, headMap);
    }
    public static String postBody(String url, String data) {
        return HttpClientUtils.postBody(url, data, null);
    }

    private static JSONObject parseHttpResult(HttpResult result) {
        if (result.getStatus() == HttpStatus.SC_UNAUTHORIZED) {
            throw new FinhubException(FinhubMessageCode.NO_AUTH, "没有权限");
        } else if (result.getStatus() == HttpResult.STATUS_EXP) {
            throw new FinhubException(FinhubMessageCode.ILLEGAL_ARGUMENT, "获取信息失败，请稍候再试！");
        } else {
            return JSON.parseObject(result.getResult());
        }
    }

}