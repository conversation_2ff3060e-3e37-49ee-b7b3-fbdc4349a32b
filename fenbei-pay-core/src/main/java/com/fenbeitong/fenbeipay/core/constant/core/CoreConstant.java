package com.fenbeitong.fenbeipay.core.constant.core;

/**
 * User: zhuminghua
 * Date: 2017/5/4 下午9:12
 * Desc:
 */
public interface CoreConstant {

    /**
     * 有效
     */
    int STATUS_VALID = 1;
    /**
     * 无效
     */
    int STATUS_INVALID = 0;

    /**
     * 删除
     */
    int DELETE_TRUE = 1;
    /**
     * 未删除
     */
    int DELETE_FALSE = 0;

    /**
     * 启用
     */
    int STATUS_ENABLE = 0;
    /**
     * 禁用
     */
    int STATUS_DISABLE = 1;

    /**
     * 不是
     */
    int NO = 0;
    /**
     * 是
     */
    int YES = 1;

    String KEY_NAME_TOKEN = "X-Auth-Token";

    static final String FBT = "分贝通";
    public static final String PREPAY = "prepay";

    String SYSTEM = "系统操作";

    /**
     * 初始化状态
     */
    Byte STATUS_RECOVER_INIT = 0;

    /**
     * 无效状态
     */
    Byte STATUS_RECOVER_DISABLE = 3;

    /**
     * 已恢复临时额度
     */
    Byte STATUS_RECOVER_RECOVER_ED = 1;

    /**
     * 临时额度恢复执行pageSize
     */
    int TEMP_AMOUNT_RECOVER_PAGE_SIZE = 100;

    /**
     * 额度调整功能锁前缀
     */
    String ACCT_ADJUST_CREDIT_PREFIX = "ACCT_ADJUST_CREDIT_";

    /**
     * 超时时间
     */
    long ACCT_ADJUST_CREDIT_LOCK_TIME = 10000;

    /**
     * 等待时间
     */
    long ACCT_ADJUST_CREDIT_WAIT_TIME = 0;

    /**
     * 集团版登陆类型: 1-登陆自己企业 2-登陆集团版授权登录的其他企业 3- 登陆集团管理端
     */
    Integer ACCT_GROUP_TAG = 3;

    /**
     * 集团版操作公司id companyId
     */
    String ACCT_GROUP_COMPANY_ID = "companyId";

    /**
     * 集团版操作账户id accountId
     */
    String ACCT_GROUP_ACCOUNT_ID = "accountId";

    /**
     * 集团版menuCode
     */
    String ACCT_GROUP_MENU_CODE = "groupCode";

    /**
     * 集团版menuCode 后缀
     */
    String ACCT_GROUP_MENU_CODE_SUFFIX = "group_";

    /**
     * 集团版标识字段
     */
    String ACCT_GROUP_COMPANY_ACCOUNT_INFOS = "ACCT_GROUP_COMPANY_ACCOUNT_INFOS";

    /**
     * 集团版操作bankNo
     */
    String ACCT_GROUP_BANK_ACCOUNT_NO = "bankAccountNo";

    /**
     * 权限相关，提供给uc账户类型：外币类型
     */
    Integer FX_UC_ACCOUNT_MODEL = 3;

    String PREPAYMENT_PREFIX = "PREPAYMENT_PREFIX_";

    /**
     * 账单消息商务账户
     */
    Integer BILL_FEE_ACCT_TYPE_BUSINESS = 1;

    /**
     * 账单消息补助福利账户
     */
    Integer BILL_FEE_ACCT_TYPE_INDIVIDUAL = 2;
}
