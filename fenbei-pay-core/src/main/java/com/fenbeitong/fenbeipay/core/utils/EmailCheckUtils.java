package com.fenbeitong.fenbeipay.core.utils;

import java.util.regex.Pattern;

/**
 * Created by FBT on 2023/7/14.
 */
public class EmailCheckUtils {

    final static Pattern partern = Pattern.compile("[a-zA-Z0-9]+[\\.]{0,1}[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]+");
    /**
     * 验证输入的邮箱格式是否符合
     * @param email
     * @return 是否合法
     */
    public static boolean emailFormat(String email){
        try {
            boolean isMatch = partern.matcher(email).matches();
            return isMatch;
        }catch (Exception e){
            return false ;
        }
    }

}
