package com.fenbeitong.fenbeipay.core.model.vo.paycenter;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.PublicPayCommonJsonDto;

import java.math.BigDecimal;

/**
 * 因公支付统一参数信息
 * Created by xjf on 2018/8/10.
 */
public class PersonalPayParameterVo extends BaseVo {
    //当前消费/退款员工id（必填）
    private String currentEmployeeId;
    //供应商公司id（必填）
    private String supplierCompanyId;
    //订单类型：3、用车，7、国内机票，11、酒店，15、火车，20、采购，30、用餐，40、国际机票，100、用车保险，101、国内机票保险，102、酒店保险，103、火车保险（必填）
    private Integer orderType;
    //消费类型：1、消费支出，2、充值，3、退款，4、分贝币兑换（必填)
    private Integer businessType;
    //订单金额（必填）
    private BigDecimal amount;
    //采购金额（必填）
    private BigDecimal purchaseAmount;
    //订单id（必填）
    private String orderId;
    //操作人id（必填）
    private String operatorId;
    //操作人姓名（必填）
    private String operatorName;
    //是否需要回调确认，1、必须确认 2、不需要确认（必填）
    private Integer callbackCheck;
    //回调确认地址
    private String callbackUrl;
    //公用参数
    private PublicPayCommonJsonDto commonJson;
    //原因
    private String reason;
    //备注
    private String comment;
    //操作类型：1:普通下单，2:后台回填（默认1）
    private Integer operatorType;
    //客服id（回填必填）
    private String customerServiceId;
    //客服姓名（回填必填）
    private String customerServiceName;

    public String getCurrentEmployeeId() {
        return currentEmployeeId;
    }

    public void setCurrentEmployeeId(String currentEmployeeId) {
        this.currentEmployeeId = currentEmployeeId;
    }

    public String getSupplierCompanyId() {
        return supplierCompanyId;
    }

    public void setSupplierCompanyId(String supplierCompanyId) {
        this.supplierCompanyId = supplierCompanyId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(BigDecimal purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getCallbackCheck() {
        return callbackCheck;
    }

    public void setCallbackCheck(Integer callbackCheck) {
        this.callbackCheck = callbackCheck;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public PublicPayCommonJsonDto getCommonJson() {
        return commonJson;
    }

    public void setCommonJson(PublicPayCommonJsonDto commonJson) {
        this.commonJson = commonJson;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(String customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public String getCustomerServiceName() {
        return customerServiceName;
    }

    public void setCustomerServiceName(String customerServiceName) {
        this.customerServiceName = customerServiceName;
    }
}
