package com.fenbeitong.fenbeipay.core.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by mac on 17/12/7.
 */
public class EncryptionUtil {

    public static String formateStringData(String key) {
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        if (key.length() <= 2) {
            return key;
        }
        return key.substring(0, 1) + getStringTimes(key.length() - 2, "*") + key.substring(key.length() - 1);
    }

    public static String getStringTimes(int times, String str) {
        StringBuffer timestr = new StringBuffer();
        if (times > 0) {
            for (int i = 0; i < times; i++) {
                timestr.append(str);
            }
        }
        return timestr.toString();
    }

}
