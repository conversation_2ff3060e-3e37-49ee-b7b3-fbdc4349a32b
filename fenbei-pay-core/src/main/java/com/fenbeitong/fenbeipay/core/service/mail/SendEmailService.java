package com.fenbeitong.fenbeipay.core.service.mail;


import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherTaskType;
import com.fenbeitong.fenbeipay.api.constant.enums.account.FbTasksType;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.FbbGrantRecallTasks;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersTask;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.FuQianLaMailContract;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.PersonPayMailContract;
import com.fenbeitong.fenbeipay.core.utils.notice.EmailContract;
import com.fenbeitong.fenbeipay.core.utils.notice.NoticeUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.DateUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by mac on 18/2/1.
 */
@Service
public class SendEmailService {

    private static final String NEXTLINE = "\n";

    @Value("${exception.remind.profile}")
    private String currentEnvironment;

    /**
     * 发送用车支付异常邮件
     *
     * @param fuQianLaMailContract
     */
    public void sendPersonErrorMsg(FuQianLaMailContract fuQianLaMailContract) {
        EmailContract emailContract = new EmailContract();
        // 收件人
        Set<String> emailSet = new HashSet<>();
        emailSet.add("<EMAIL>");
        emailContract.setToList(emailSet);
        // 邮件标题
        String subject = "个人支付异常";
        emailContract.setSubject(subject);
        StringBuffer text = new StringBuffer();
        text.append("个人支付信息").append("\n\n");
        text.append("用户：").append(fuQianLaMailContract.getUserName()).append(NEXTLINE);
        text.append("手机号：").append(fuQianLaMailContract.getPhoneNum()).append(NEXTLINE);
        text.append("公司：").append(fuQianLaMailContract.getCompanyName()).append(NEXTLINE);
        text.append("需要支付金额：").append(fuQianLaMailContract.getAmount()).append("元").append(NEXTLINE);
        text.append("实际支付金额：").append(fuQianLaMailContract.getActureAmount()).append("元").append(NEXTLINE);
        text.append("分贝订单ID：").append(fuQianLaMailContract.getFbOrderNo()).append(NEXTLINE);
        text.append("个人支付订单ID：").append(fuQianLaMailContract.getOrderNo()).append(NEXTLINE);
        text.append("支付时间：").append(fuQianLaMailContract.getCompleteTime()).append(NEXTLINE);
        emailContract.setText(text.toString());
        // 发送邮件
        NoticeUtils.sendEmail(emailContract);
    }


    public void sendRefundErrorMail(String subject, String content) {
        Set<String> emailSet = new HashSet<>();
        emailSet.add("<EMAIL>");
        sendEmail(subject, content, emailSet);
    }

    public void sendPayErrorMail(String subject, String content) {
        Set<String> emailSet = new HashSet<>();
        emailSet.add("<EMAIL>");
        sendEmail(subject, content, emailSet);
    }

    private void sendEmail(String subject, String content, Set<String> emailSet) {
        EmailContract emailContract = new EmailContract();
        emailContract.setToList(emailSet);
        emailContract.setSubject(subject);
        emailContract.setText(content);
        NoticeUtils.sendEmail(emailContract);
    }

    /**
     * 发送支付钱异常邮件
     *
     * @param personPayMailContract
     */
    public void sendPersonPayErrorMsg(PersonPayMailContract personPayMailContract) {
        EmailContract emailContract = new EmailContract();
        // 收件人
        Set<String> emailSet = new HashSet<>();
        emailSet.add("<EMAIL>");
        emailContract.setToList(emailSet);
        // 邮件标题
        String subject = "个人支付异常";
        emailContract.setSubject(subject);
        StringBuffer text = new StringBuffer();
        text.append("个人支付信息").append("\n\n");
        //text.append("用户：").append(personPayMailContract.getUserName()).append(NEXTLINE);
        // text.append("手机号：").append(personPayMailContract.getPhoneNum()).append(NEXTLINE);
        // text.append("公司：").append(personPayMailContract.getCompanyName()).append(NEXTLINE);
        text.append("需要支付金额：").append(personPayMailContract.getAmount()).append("元").append(NEXTLINE);
        text.append("实际支付金额：").append(personPayMailContract.getActureAmount()).append("元").append(NEXTLINE);
        text.append("分贝订单ID：").append(personPayMailContract.getFbOrderNo()).append(NEXTLINE);
        text.append("个人支付订单ID：").append(personPayMailContract.getOrderNo()).append(NEXTLINE);
        text.append("支付方式：").append(personPayMailContract.getPayChannel().getValue()).append(NEXTLINE);
        text.append("支付时间：").append(personPayMailContract.getCompleteTime()).append(NEXTLINE);
        emailContract.setText(text.toString());
        // 发送邮件
        NoticeUtils.sendEmail(emailContract);
    }

    /**
     * sendVoucherTaskExceptionMail
     *
     * @return void
     * @Description 发送分贝券发放撤回异常邮件
     * @Date 上午10:59 2018/12/20
     * @Param [exceptionTaskList]
     **/
    public void sendVoucherTaskExceptionMail(List<VouchersTask> exceptionTaskList) {
        if (ObjUtils.isEmpty(exceptionTaskList)) {
            return;
        }
        String subject = "项目【fenbei-pay】【" + currentEnvironment + "】【分贝券任务异常】分贝券任务（发放|撤回）执行异常缓慢";
        StringBuffer emailContent = new StringBuffer();
        String eTaskId = "异常任务ID：";
        String eTaskType = "异常任务类型：";
        String eTaskCreateTime = "异常任务创建时间：";
        String eReason = "异常原因：发放超时";
        String dividingLine = "****************************************************";
        exceptionTaskList.forEach(task -> {
            emailContent.append(eTaskId).append(task.getVouchersTaskId()).append(NEXTLINE);
            emailContent.append(eTaskType).append(VoucherTaskType.getMsgFromValue(task.getVouchersTaskType())).append(NEXTLINE);
            emailContent.append(eTaskCreateTime).append(DateUtils.format(task.getCreateTime())).append(NEXTLINE);
            emailContent.append(eReason).append(NEXTLINE);
            emailContent.append(dividingLine).append(NEXTLINE);
        });
        try {
            //开启发送异常邮件
            Set<String> emailSet = new HashSet<>();
            emailSet.add("<EMAIL>");
            sendEmail(subject, emailContent.toString(), emailSet);
        } catch (Exception e) {
            FinhubLogger.error("邮件发放异常:{}", e.getMessage(), e);
        }
    }


    /**
     * sendVoucherTaskExceptionMail
     *
     * @return void
     * @Description 发送分贝券发放撤回异常邮件
     * @Date 上午10:59 2018/12/20
     * @Param [exceptionTaskList]
     **/
    public void sendFbbTaskExceptionMail(List<FbbGrantRecallTasks> fbbGrantRecallTasksList) {
        if (CollectionUtils.isEmpty(fbbGrantRecallTasksList)) {
            return;
        }
        String subject = "项目【fenbei-pay】【" + currentEnvironment + "】【分贝币任务异常】分贝币任务（发放|撤回）执行异常缓慢";
        StringBuffer emailContent = new StringBuffer();
        String eTaskId = "异常任务ID：";
        String eTaskType = "异常任务类型：";
        String eTaskCreateTime = "异常任务创建时间：";
        String eReason = "异常原因：发放超时";
        String dividingLine = "****************************************************";
        fbbGrantRecallTasksList.forEach(task -> {
            emailContent.append(eTaskId).append(task.getTasksId()).append(NEXTLINE);
            emailContent.append(eTaskType).append(FbTasksType.getEnum(task.getTaskType()).getValue()).append(NEXTLINE);
            emailContent.append(eTaskCreateTime).append(DateUtils.format(task.getDispatchTime())).append(NEXTLINE);
            emailContent.append(eReason).append(NEXTLINE);
            emailContent.append(dividingLine).append(NEXTLINE);
        });
        try {
            //开启发送异常邮件
            Set<String> emailSet = new HashSet<>();
            emailSet.add("<EMAIL>");
            sendEmail(subject, emailContent.toString(), emailSet);
        } catch (Exception e) {
            FinhubLogger.error("邮件发放异常:{}", e.getMessage(), e);
        }
    }

    /**
     * @Description: 发送提醒邮件
     * @methodName: sendNoticeMail
     * @Param: [subject, emailContent, email]
     * @return: void
     * @Author: zhangga
     * @Date: 2019/6/15 10:20 AM
     **/
    public void sendNoticeMail(String subject, String emailContent, String email) {
        try {
            Set<String> emailSet = new HashSet<>();
            emailSet.add(email);
            sendEmail(subject, emailContent, emailSet);
        } catch (Exception e) {
            FinhubLogger.error("邮件发放异常:{}", e.getMessage(), e);
        }
    }


}
