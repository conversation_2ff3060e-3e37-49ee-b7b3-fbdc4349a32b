package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoAcctCheckingMsg;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaAutoVirtualcardCheckingMsg;
import com.fenbeitong.finhub.kafka.producer.ISaturnKafkaProducerPublisher;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.concurrent.CompletableFuture;

@Service
public class AutoAcctCheckingEventUtil {

    @Autowired
    private ISaturnKafkaProducerPublisher iSaturnKafkaProducerPublisher;

    /**
     * 事务提交后，执行（适用：定义了事务，没有包装afterCommit）
     * @param kafkaAutoAcctCheckingMsg
     */
    public void sendAccountChangeEvent(KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg) {
        /**
         * 如果为FBT，不入对账库
         * QX 2021-12-30 FBT-9264、FBT-9674
         */
        if(BankNameEnum.isFbt(kafkaAutoAcctCheckingMsg.getBankName())){
            return;
        }
        // 注册事务同步处理
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                // 事务提交完毕时，触发
                try {
                    //银行手上账的才需要发送消息
                    CompletableFuture.runAsync(() -> {
                        iSaturnKafkaProducerPublisher.publish(kafkaAutoAcctCheckingMsg);
                        FinhubLogger.info("自动对账-sendAccountChangeEvent-银行上账消息发送, 通知消息为:{}", JsonUtils.toJson(kafkaAutoAcctCheckingMsg));
                    });
                } catch (Exception e) {
                    FinhubLogger.error("自动对账-sendAccountChangeEvent-发送失败", e);
                    FinhubLogger.error("自动对账-sendAccountChangeEvent-银行上账消息发送失败, 通知消息为:{}", JsonUtils.toJson(kafkaAutoAcctCheckingMsg));
                }
            }
        });

    }

    /**
     * 不需要在事务提交后执行（适用：无事务，或者定义了事务，包装了afterCommit）
     * @param kafkaAutoAcctCheckingMsg
     */
    public void sendAccountChangeMsgEvent(KafkaAutoAcctCheckingMsg kafkaAutoAcctCheckingMsg) {
        /**
         * 如果为FBT，不入对账库
         * QX 2021-12-30 FBT-9264、FBT-9674
         */
        if(BankNameEnum.isFbt(kafkaAutoAcctCheckingMsg.getBankName())){
            return;
        }
        FinhubLogger.info("sendAccountChangeMsgEvent消息内容{}",JsonUtils.toJson(kafkaAutoAcctCheckingMsg));
        try {
            //银行手上账的才需要发送消息
            CompletableFuture.runAsync(() -> {
                iSaturnKafkaProducerPublisher.publish(kafkaAutoAcctCheckingMsg);
            });
        } catch (Exception e) {
            FinhubLogger.error("自动对账-sendAccountChangeMsgEvent-发送失败", e);
            FinhubLogger.error("自动对账-sendAccountChangeMsgEvent-银行上账消息发送失败, 通知消息为:{}", JsonUtils.toJson(kafkaAutoAcctCheckingMsg));
        }
    }

    public void sendVirtualCardChangeMsgEvent(KafkaAutoVirtualcardCheckingMsg msg) {
        if (BankNameEnum.isFbt(msg.getBankCode())) {
            return;
        }
        FinhubLogger.info("虚拟卡对账消息msg={}", JsonUtils.toJson(msg));
        try {
            CompletableFuture.runAsync(() -> {
                iSaturnKafkaProducerPublisher.publish(msg);
            });
        } catch (Exception e) {
            FinhubLogger.error("虚拟卡对账消息发送失败", e);
        }
    }
}
