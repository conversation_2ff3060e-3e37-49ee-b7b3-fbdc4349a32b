package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.io.Serializable;

/**
 * 订单支付状态查询
 * Created by mac on 17/12/29.
 */
@Deprecated
public class FuQianLaPayReturnStatusContract implements Serializable {

    private String ret_code;
    private String ret_desc;
    private String charset;
    private String sign_type;
    private String sign_info;
    private String version;
    private ChargeData ret_data;

    public static class ChargeData {

        private String merch_id;
        private String charge_id;
        private String order_no;
        private String channel;
        private String currency;
        private Integer amount;
        private String subject;
        private String body;
        private String app_id;
        private String device;
        private String status;
        private String optional;
        private String receive_time;
        private String complete_time;

        public String getMerch_id() {
            return merch_id;
        }

        public void setMerch_id(String merch_id) {
            this.merch_id = merch_id;
        }

        public String getCharge_id() {
            return charge_id;
        }

        public void setCharge_id(String charge_id) {
            this.charge_id = charge_id;
        }

        public String getOrder_no() {
            return order_no;
        }

        public void setOrder_no(String order_no) {
            this.order_no = order_no;
        }

        public String getChannel() {
            return channel;
        }

        public void setChannel(String channel) {
            this.channel = channel;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public String getSubject() {
            return subject;
        }

        public void setSubject(String subject) {
            this.subject = subject;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }

        public String getApp_id() {
            return app_id;
        }

        public void setApp_id(String app_id) {
            this.app_id = app_id;
        }

        public String getDevice() {
            return device;
        }

        public void setDevice(String device) {
            this.device = device;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getOptional() {
            return optional;
        }

        public void setOptional(String optional) {
            this.optional = optional;
        }

        public String getReceive_time() {
            return receive_time;
        }

        public void setReceive_time(String receive_time) {
            this.receive_time = receive_time;
        }

        public String getComplete_time() {
            return complete_time;
        }

        public void setComplete_time(String complete_time) {
            this.complete_time = complete_time;
        }
    }

    public String getRet_code() {
        return ret_code;
    }

    public void setRet_code(String ret_code) {
        this.ret_code = ret_code;
    }

    public String getRet_desc() {
        return ret_desc;
    }

    public void setRet_desc(String ret_desc) {
        this.ret_desc = ret_desc;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public String getSign_type() {
        return sign_type;
    }

    public void setSign_type(String sign_type) {
        this.sign_type = sign_type;
    }

    public String getSign_info() {
        return sign_info;
    }

    public void setSign_info(String sign_info) {
        this.sign_info = sign_info;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public ChargeData getRet_data() {
        return ret_data;
    }

    public void setRet_data(ChargeData ret_data) {
        this.ret_data = ret_data;
    }
}
