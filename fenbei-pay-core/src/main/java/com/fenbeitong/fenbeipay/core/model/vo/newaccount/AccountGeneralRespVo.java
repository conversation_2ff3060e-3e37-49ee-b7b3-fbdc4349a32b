package com.fenbeitong.fenbeipay.core.model.vo.newaccount;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class AccountGeneralRespVo extends BaseVo {

    /**
     * 公司Id
     */
    private String companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 总账户余额 单位：分
     */
    private BigDecimal balance;

    /**
     * 账户状态
     */
    private Integer accountStatus;


}
