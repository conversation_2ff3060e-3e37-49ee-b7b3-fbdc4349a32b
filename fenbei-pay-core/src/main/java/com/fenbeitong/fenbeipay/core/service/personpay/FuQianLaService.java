package com.fenbeitong.fenbeipay.core.service.personpay;

import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.constant.personpay.EmployeeStatus;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.fbb.PersonAccountMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayCallbackLogMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayRecordMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonPayRefundLogMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.mysql.thirdpay.PersonRefundRecordMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.company.CompanyMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.employee.EmployeeMapper;
import com.fenbeitong.fenbeipay.core.dao.fenbeitong.psql.personpay.PersonOrderMapper;
import com.fenbeitong.fenbeipay.core.enums.personpay.BizType;
import com.fenbeitong.fenbeipay.core.enums.personpay.PayCommonType;
import com.fenbeitong.fenbeipay.core.enums.personpay.PersonPayType;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.company.Company;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccount;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.fbb.PersonAccountExample;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.personpay.PersonOrder;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.*;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.*;
import com.fenbeitong.fenbeipay.core.service.mail.SendEmailService;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.fenbeitong.fenbeipay.core.utils.HttpclientUtils;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.usercenter.api.model.po.employee.Employee;
import com.luastar.swift.base.json.JsonUtils;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.fenbeitong.fenbeipay.core.constant.paycenter.FuQianLaConstant.FQL_CHANNLE_LIKE;

/**
 * Created by mac on 17/12/27.
 */
@Deprecated
@Service
public class FuQianLaService {



     static final Logger logger = LoggerFactory.getLogger(FuQianLaService.class);


    @Autowired
    public PersonAccountMapper personAccountMapper;
    @Autowired
    public PersonOrderMapper personOrderMapper;
    @Autowired
    public PersonPayRecordMapper personPayRecordMapper;
    @Autowired
    public EmployeeMapper employeeMapper;
    @Autowired
    public PersonPayCallbackLogMapper personPayCallbackLogMapper;
    @Autowired
    public SendEmailService sendEmailService;
    @Autowired
    public CompanyMapper companyMapper;
    @Autowired
    public PersonPayRefundLogMapper refundLogMapper;
    @Autowired
    public PersonRefundRecordMapper refundRecordMapper;
    /**
     *
     * @param orderId
     * @return
     */
    private PersonOrder queryPersonOrderByOrderId(String orderId) {
        return personOrderMapper.selectByPrimaryKey(orderId);
    }


    /**
     * 初始化下单接口
     *
     * @param fuQianLaPayCommonReqContract
     * @param employeeId
     * @param companyId
     */
    @Transactional(value = "fenbeitong")
    public FuQianLaPayContract createOrder(FuQianLaPayCommonReqContract fuQianLaPayCommonReqContract, String employeeId, String companyId,
                                           CheckOrderPayContract checkOrderPayContract, String appid, String notifyUrl, String env) {
        //检查用户个人帐户是否存在
        PersonAccountExample personAccountExample = new PersonAccountExample();
        personAccountExample.createCriteria().andEmployeeIdEqualTo(employeeId).andStatusNotEqualTo(EmployeeStatus.SOFT_DELETE.getKey());
        List<PersonAccount> personAccountList = personAccountMapper.selectByExample(personAccountExample);
        //个人帐户id
        String personAccountId = RandomUtils.bsonId();
        if (CollectionUtils.isNotEmpty(personAccountList)) {
            personAccountId = personAccountList.get(0).getId();
        } else {
            Employee employee = employeeMapper.selectByPrimaryKey(employeeId);
            if (employee != null) {
                //添加个人帐户信息
                PersonAccount personAccount = new PersonAccount();
                personAccount.setId(personAccountId);
                personAccount.setEmployeeId(employee.getId());
                personAccount.setEmployeeName(employee.getName());
                personAccount.setEmployeePhone(employee.getPhone_num());
                personAccount.setStatus(employee.getStatus());
                personAccount.setCreateTime(new Date());
                personAccount.setBalance(BigDecimal.ZERO);
                personAccount.setAchieveFbbAmount(BigDecimal.ZERO);
                personAccount.setConsumeFbbAmount(BigDecimal.ZERO);
                personAccountMapper.insertSelective(personAccount);

            }
        }
        //处理订单信息（如果有一单支付成功的历史记录，就支付成功）
        List<Integer> values = new ArrayList<>();
        values.add(2);
        values.add(4);
        List<PersonPayRecord> personPayRecordList = this.getPayRecord(fuQianLaPayCommonReqContract.getPersonOrderNo(), personAccountId, values);
        Boolean isNeedPay = false;
        if (CollectionUtils.isNotEmpty(personPayRecordList)) {
            for (PersonPayRecord personPayRecord : personPayRecordList) {
                //支付成功
                if (personPayRecord.getStatus().intValue() == PayConstant.paySuccess.intValue()) {
                    isNeedPay = true;
                    break;
                }
            }
            if (isNeedPay) {
                return null;
            } else {
                return createOrderInit(fuQianLaPayCommonReqContract, fuQianLaPayCommonReqContract.getPersonOrderNo(), personAccountId,
                        employeeId, companyId, appid, notifyUrl, checkOrderPayContract, env);
            }

        } else {
            return createOrderInit(fuQianLaPayCommonReqContract, fuQianLaPayCommonReqContract.getPersonOrderNo(), personAccountId,
                    employeeId, companyId, appid, notifyUrl, checkOrderPayContract, env);
        }
    }


    /**
     * 检测是否有部分支付订单
     *
     * @param employeeId
     * @param taxiOrderNo
     * @return
     */
    public boolean checkPayLess(String employeeId, String taxiOrderNo) {
        //检查用户个人帐户是否存在
        PersonAccountExample personAccountExample = new PersonAccountExample();
        personAccountExample.createCriteria().andEmployeeIdEqualTo(employeeId).andStatusNotEqualTo(EmployeeStatus.SOFT_DELETE.getKey());
        List<PersonAccount> personAccountList = personAccountMapper.selectByExample(personAccountExample);
        String personAccountId = RandomUtils.bsonId();
        if (CollectionUtils.isNotEmpty(personAccountList)) {
            personAccountId = personAccountList.get(0).getId();
        }
        List<Integer> values = new ArrayList<>();
        values.add(4);
        List<PersonPayRecord> personPayRecordList = this.getPayRecord(taxiOrderNo, personAccountId, values);
        if (CollectionUtils.isNotEmpty(personPayRecordList)) {
            return true;
        }
        return false;
    }


    /**
     * 是否有待支付订单
     *
     * @param checkOrderPayContract
     * @return
     */
    private FuQianLaPayContract checkIfHasPay(CheckOrderPayContract checkOrderPayContract, String appId, String notifyUrl, String taxiOrderNo, String personAccountId) {
        List<Integer> values = new ArrayList<>();
        values.add(1);
        List<PersonPayRecord> personPayRecords = this.getPayRecord(taxiOrderNo, personAccountId, values);
        FuQianLaPayContract fuQianLaPayContract = null;
        if (CollectionUtils.isEmpty(personPayRecords)) {
            return null;
        }
        for (PersonPayRecord personPayRecord : personPayRecords) {
            //待支付
            if (personPayRecord.getStatus().intValue() == PayConstant.payInit.intValue()) {
                fuQianLaPayContract = new FuQianLaPayContract();
                fuQianLaPayContract = orderInfoRes(appId, notifyUrl, checkOrderPayContract, personPayRecord.getOrderId(),personPayRecord.getType().toString());
                break;
            }
        }
        logger.info("查询到有待支付的订单：" + JsonUtils.toJson(fuQianLaPayContract));
        return fuQianLaPayContract;
    }

    /**
     * 创建订单信息
     *
     * @param fuQianLaPayCommonReqContract
     * @param fbOrderId
     * @param accountId
     * @param companyId
     */
    @Transactional(value = "fenbeitong")
    public FuQianLaPayContract createOrderInit(FuQianLaPayCommonReqContract fuQianLaPayCommonReqContract, String fbOrderId, String accountId, String employeeId,
                                                String companyId, String appid, String notifyUrl, CheckOrderPayContract checkOrderPayContract, String env) {
        //创建个人订单信息
        PersonOrder personOrder = new PersonOrder();
        Date nowTime = new Date();
        //个人订单号
        String ordeNo = PersonOrderIdInit.getOrderId(env);
        personOrder.setId(ordeNo);
        personOrder.setFbOrderId(fbOrderId);
        personOrder.setClientIp(fuQianLaPayCommonReqContract.getClientIp());
        personOrder.setDevice(fuQianLaPayCommonReqContract.getDevice());
        personOrder.setLiveMode(fuQianLaPayCommonReqContract.getLiveMode() == null ? false : fuQianLaPayCommonReqContract.getLiveMode());
        personOrder.setCurrency(PayConstant.CNY);
        personOrder.setAccountType(checkOrderPayContract.getOrder_type());
        //处理个人支付金额（元转分）
        BigDecimal personal_pay_price = checkOrderPayContract.getPersonal_pay_price();
        Long amount = personal_pay_price.multiply(BigDecimal.valueOf(100)).longValue();
        logger.info("需要个人支付的金额：" + amount);
        personOrder.setAmount(amount);
        personOrder.setSubject(BizType.valueOf(Integer.valueOf(fuQianLaPayCommonReqContract.getOrderType())).getDesc());
        personOrder.setBody(BizType.valueOf(Integer.valueOf(fuQianLaPayCommonReqContract.getOrderType())).getDesc());
        personOrder.setNotifyUrl(notifyUrl);
        personOrder.setVersion(PayConstant.FUQIANLA_VERSION);
        personOrder.setSignType(PayConstant.FUQIANLA_RSA);
        personOrder.setCreateTime(nowTime);
        personOrderMapper.insertSelective(personOrder);
        //创建个人支付记录
        PersonPayRecord personPayRecord = new PersonPayRecord();
        personPayRecord.setId(RandomUtils.bsonId());
        personPayRecord.setOrderId(ordeNo);
        personPayRecord.setEmployeeId(employeeId);
        personPayRecord.setAccountId(accountId);
        personPayRecord.setFbOrderId(fbOrderId);
        personPayRecord.setStatus(PayConstant.payDefault);
        personPayRecord.setAmount(amount);
        personPayRecord.setType(Integer.valueOf(fuQianLaPayCommonReqContract.getOrderType()));
        personPayRecord.setAccountType(checkOrderPayContract.getOrder_type());
        personPayRecord.setCompanyId(companyId);
        personPayRecord.setCreateTime(nowTime);
        personPayRecordMapper.insertSelective(personPayRecord);
        return orderInfoRes(appid, notifyUrl, checkOrderPayContract, ordeNo,fuQianLaPayCommonReqContract.getOrderType());
    }

    /**
     * 处理返回数据
     *
     * @param appid
     * @param notifyUrl
     * @param checkOrderPayContract
     * @return
     */
    private FuQianLaPayContract orderInfoRes(String appid, String notifyUrl, CheckOrderPayContract checkOrderPayContract, String orderNo,String orderType) {
        FuQianLaPayContract fuQianLaPayContract = new FuQianLaPayContract();
        fuQianLaPayContract.setApp_id(appid);
        fuQianLaPayContract.setSign_type(PayConstant.FUQIANLA_RSA);
        FuQianLaPayContract.Charge Charge = new FuQianLaPayContract.Charge();
        Charge.setOrderAmount(checkOrderPayContract.getTotal_pay_price());
        Charge.setCompanyActualAmount(checkOrderPayContract.getCompany_pay_price());
        Charge.setCompanyCardAmount(checkOrderPayContract.getCoupon_amount());
        Charge.setAmount(checkOrderPayContract.getPersonal_pay_price());
        Charge.setCompanyAmount(checkOrderPayContract.getTotal_price());
        Charge.setSubject(BizType.valueOf(Integer.valueOf(orderType)).getDesc());
        Charge.setBody(BizType.valueOf(Integer.valueOf(orderType)).getDesc());
        Charge.setCurrency(PayConstant.CNY);
        Charge.setOrder_no(orderNo);
        Charge.setNotifyUrl(notifyUrl);
        Charge.setOptional(orderType);
        Charge.setLimitPrice(checkOrderPayContract.getLimit_price());
        fuQianLaPayContract.setCharge(Charge);
        return fuQianLaPayContract;
    }

    /**
     * 个人支付成功/部分支付集合
     * 1-待支付、2-支付成功、3-支付失败、4-部分支付
     *
     * @param fbOrderId
     * @param accountId
     * @return
     */
    private List<PersonPayRecord> getPayRecord(String fbOrderId, String accountId, List<Integer> values) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andFbOrderIdEqualTo(fbOrderId).andAccountIdEqualTo(accountId).andStatusIn(values);
        return personPayRecordMapper.selectByExample(personPayRecordExample);
    }


    /**
     * 修改签名信息数据
     *
     * @param signInfoContract 参数信息
     */
    public int updateOrderStatus(SignInfoReqContract signInfoContract, String employeeId) {
        logger.info("签名修改通道和签名信息" + JsonUtils.toJson(signInfoContract));
        //检查用户个人帐户是否存在
        PersonAccountExample personAccountExample = new PersonAccountExample();
        personAccountExample.createCriteria().andEmployeeIdEqualTo(employeeId).andStatusNotEqualTo(EmployeeStatus.SOFT_DELETE.getKey());
        List<PersonAccount> personAccountList = personAccountMapper.selectByExample(personAccountExample);
        if (CollectionUtils.isEmpty(personAccountList)) {
            throw new FinhubException(UcMessageCode.FAIL, "用户信息不存在");
        }

        //处理订单信息（如果有一单支付成功的历史记录，就支付成功）
        List<Integer> values = new ArrayList<>();
        values.add(2);
        values.add(4);
        List<PersonPayRecord> personPayRecordList = this.getPayRecord(signInfoContract.getPersonOrderNo(), personAccountList.get(0).getId(), values);
        Boolean isNeedPay = false;
        Boolean isPayLess = false;
        if (CollectionUtils.isNotEmpty(personPayRecordList)) {
            for (PersonPayRecord personPayRecord : personPayRecordList) {
                //支付成功
                if (personPayRecord.getStatus().intValue() == PayConstant.paySuccess.intValue()) {
                    isNeedPay = true;
                }
                //部分支付
                else if (personPayRecord.getStatus().intValue() == PayConstant.payLess.intValue()) {
                    isPayLess = true;
                }
            }
        }
        //个人已经支付返回 －1
        if (isNeedPay) {
            return -1;
        }
        //只有部分支付 -2
        else if (!isNeedPay && isPayLess) {
            return -2;
        } else {
            PersonPayRecord personPayRecord = new PersonPayRecord();
            personPayRecord.setChannel(signInfoContract.getChannel());
            personPayRecord.setSignInfo(signInfoContract.getSignInfo());
            personPayRecord.setStatus(PayConstant.payInit);

            PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
            personPayRecordExample.createCriteria().andFbOrderIdEqualTo(signInfoContract.getPersonOrderNo()).
                    andOrderIdEqualTo(signInfoContract.getOrderNo());
            return personPayRecordMapper.updateByExampleSelective(personPayRecord, personPayRecordExample);
        }
    }

    /**
     * 修改订单最终支付状态
     *
     * @param fuQianLaPayReturnContract
     * @return
     */
    public int updateOrderFinishStatus(FuQianLaPayReturnContract fuQianLaPayReturnContract, Integer paySuccess) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(fuQianLaPayReturnContract.getOrder_no()).
                andStatusEqualTo(PayConstant.payInit);
        //查询支付订单
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        if (CollectionUtils.isEmpty(personPayRecordList)) {
            logger.error("回调支付未查询到需要支付的订单信息");
            return -1;
        }
        //修改支付状态
        PersonPayRecord personPayRecord = new PersonPayRecord();
        if (PayConstant.orderSuccessStatusCode.equals(fuQianLaPayReturnContract.getRet_code())) {
            logger.info("数据库金额:" + personPayRecordList.get(0).getAmount() + ",实际金额：" + fuQianLaPayReturnContract.getAmount());
            if (personPayRecordList.get(0).getAmount().longValue() == Long.valueOf(fuQianLaPayReturnContract.getAmount().longValue())) {
                personPayRecord.setStatus(PayConstant.paySuccess);
            } else {
                personPayRecord.setStatus(PayConstant.payLess);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        logger.info("出现个人支付金额异常情况，准备发送邮件……");
                        try {
                            //处理公司信息
                            Company company = companyMapper.selectByPrimaryKey(personPayRecordList.get(0).getCompanyId());
                            //处理用户
                            Employee employee = employeeMapper.selectByPrimaryKey(personPayRecordList.get(0).getEmployeeId());
                            FuQianLaMailContract fuQianLaMailContract = new FuQianLaMailContract();
                            BigDecimal actureAmount = BigDecimal.valueOf(fuQianLaPayReturnContract.getAmount()).divide(BigDecimal.valueOf(100));
                            fuQianLaMailContract.setActureAmount(actureAmount);
                            BigDecimal amount = BigDecimal.valueOf(personPayRecordList.get(0).getAmount()).divide(BigDecimal.valueOf(100));
                            fuQianLaMailContract.setAmount(amount);
                            fuQianLaMailContract.setCompanyName(company == null ? "未知公司" : company.getName());
                            fuQianLaMailContract.setCompleteTime(fuQianLaPayReturnContract.getComplete_time());
                            fuQianLaMailContract.setFbOrderNo(personPayRecordList.get(0).getFbOrderId());
                            fuQianLaMailContract.setOrderNo(fuQianLaPayReturnContract.getOrder_no());
                            fuQianLaMailContract.setPhoneNum(employee == null ? "手机号未知" : employee.getPhone_num());
                            fuQianLaMailContract.setUserName(employee == null ? "用户名未知" : employee.getName());
                            sendEmailService.sendPersonErrorMsg(fuQianLaMailContract);
                        } catch (Exception e) {
                            logger.error("发送个人支付邮件异常：" + e.getLocalizedMessage());
                        }
                        logger.info("出现个人支付金额异常情况，邮件发送完成");
                    }
                }).start();
            }
        } else {
            personPayRecord.setStatus(PayConstant.payFail);
        }
        personPayRecord.setReceiveTime(DateUtil.getDateFromString(fuQianLaPayReturnContract.getReceive_time(), "yyyyMMddHHmmss"));
        personPayRecord.setCompleteTime(DateUtil.getDateFromString(fuQianLaPayReturnContract.getComplete_time(), "yyyyMMddHHmmss"));
        personPayRecord.setActualAmount(Long.valueOf(fuQianLaPayReturnContract.getAmount()));
        personPayRecord.setFbOrderStatus(paySuccess);
        personPayRecord.setReturnMsg(JsonUtils.toJson(fuQianLaPayReturnContract));
        personPayRecord.setChargeId(fuQianLaPayReturnContract.getCharge_id());
        personPayRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(personPayRecord, personPayRecordExample);


    }


    /**
     * 查询订单信息
     *
     * @param fuQianLaPayReturnContract
     * @return
     */
    public PersonPayRecord getPersonPayRecordByOrderId(FuQianLaPayReturnContract fuQianLaPayReturnContract) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(fuQianLaPayReturnContract.getOrder_no());
        //查询支付订单
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        if (CollectionUtils.isEmpty(personPayRecordList)) {
            logger.error("查询订单信息，回调支付未查询到订单信息");
            throw new FinhubException(UcMessageCode.FAIL, "订单信息不存在");
        } else {
            return personPayRecordList.get(0);
        }
    }

    /**
     * 获取待支付的订单
     *
     * @return
     */
    @Deprecated
    public List<PersonPayRecord> getPersonPayRecordList() {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andStatusEqualTo(PayConstant.payInit).andChannelLike(FQL_CHANNLE_LIKE);
        return personPayRecordMapper.selectByExample(personPayRecordExample);
    }
    /**
     * 获取退款中的订单
     *
     * @return
     */
    @Deprecated
    public List<PersonRefundRecord> getPersonRefundRecordList() {
        PersonRefundRecordExample personRefundRecordExample = new PersonRefundRecordExample();
        personRefundRecordExample.createCriteria().andRefundStatusEqualTo(PayConstant.payOrderRefundIng).andChannelLike(FQL_CHANNLE_LIKE);
        return refundRecordMapper.selectByExample(personRefundRecordExample);
    }
    /**
     * 修改订单状态
     *
     * @param orderId
     * @param status
     * @return
     */
    public int updatePersonPayRecord(String orderId, Integer status) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderId);
        PersonPayRecord payRecord = new PersonPayRecord();
        payRecord.setStatus(status);
        payRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(payRecord, personPayRecordExample);
    }
    public int updatePersonPayRecordForRefundStatus(String orderId, Integer status) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderId);
        PersonPayRecord payRecord = new PersonPayRecord();
        payRecord.setRefundOrderStatus(status);
        payRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(payRecord, personPayRecordExample);
    }

    public int updatePersonPayRecordByFbOrderId(String fborderId,String txn_id, Integer status) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andFbOrderIdEqualTo(fborderId).andChargeIdEqualTo(txn_id);
        PersonPayRecord payRecord = new PersonPayRecord();
        payRecord.setStatus(status);
        payRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(payRecord, personPayRecordExample);
    }


    /**
     * 修改查询订单最终支付状态
     *
     * @param orderId
     * @param fuQianLaPayReturnStatusContract
     * @param paySuccess
     * @return
     */
    public int updateOrderSearchStatus(String orderId, FuQianLaPayReturnStatusContract fuQianLaPayReturnStatusContract, Integer paySuccess) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderId).
                andStatusNotEqualTo(PayConstant.paySuccess);
        //查询支付订单
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        if (CollectionUtils.isEmpty(personPayRecordList)) {
            logger.error("回调支付未查询到订单信息");
            throw new FinhubException(UcMessageCode.FAIL, "订单信息不存在");
        }
        //修改支付状态
        PersonPayRecord personPayRecord = new PersonPayRecord();
        if (PayConstant.orderSuccessStatusCode.equals(fuQianLaPayReturnStatusContract.getRet_code())) {
            if (personPayRecordList.get(0).getAmount() <= Long.valueOf(fuQianLaPayReturnStatusContract.getRet_data().getAmount())) {
                personPayRecord.setStatus(PayConstant.paySuccess);
            } else {
                personPayRecord.setStatus(PayConstant.payLess);
            }
        } else {
            personPayRecord.setStatus(PayConstant.payFail);
        }
        personPayRecord.setReceiveTime(DateUtil.getDateFromString(fuQianLaPayReturnStatusContract.getRet_data().getReceive_time(), "yyyyMMddHHmmss"));
        personPayRecord.setCompleteTime(DateUtil.getDateFromString(fuQianLaPayReturnStatusContract.getRet_data().getComplete_time(), "yyyyMMddHHmmss"));
        personPayRecord.setActualAmount(Long.valueOf(fuQianLaPayReturnStatusContract.getRet_data().getAmount()));
        personPayRecord.setFbOrderStatus(paySuccess);
        personPayRecord.setReturnMsg(JsonUtils.toJson(fuQianLaPayReturnStatusContract));
        personPayRecord.setUpdateTime(new Date());
        personPayRecord.setChargeId(fuQianLaPayReturnStatusContract.getRet_data().getCharge_id());
        return personPayRecordMapper.updateByExampleSelective(personPayRecord, personPayRecordExample);
    }


    //获取需要通知的订单数据
    @Deprecated
    public List<PersonPayRecord> getNoticePersonPayRecord() {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andStatusEqualTo(PayConstant.paySuccess)
                .andFbOrderStatusEqualTo(PayConstant.taxiInit)
                .andChannelLike(FQL_CHANNLE_LIKE);
        return personPayRecordMapper.selectByExample(personPayRecordExample);
    }

    /**
     * 修改通知个人订单状态
     *
     * @param orderId
     * @param status
     * @return
     */
    public int updateTaxiPersonPayRecord(String orderId, Integer status) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderId);
        PersonPayRecord payRecord = new PersonPayRecord();
        payRecord.setFbOrderStatus(status);
        payRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(payRecord, personPayRecordExample);
    }

    /**
     * 添加日志记录
     *
     * @param personPayCallbackLog
     * @return
     */
    public int savePersonPayCallbackLog(PersonPayCallbackLog personPayCallbackLog) {
        int count = 0;
        try {
            count = personPayCallbackLogMapper.insertSelective(personPayCallbackLog);
        } catch (Exception e) {
            logger.error("添加日志异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return count;
    }

    /**
     * 获取订单信息
     *
     * @param orderNo
     * @return
     */
    public PersonPayRecord getPersonPayRecord(String orderNo) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderNo);
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        return CollectionUtils.isEmpty(personPayRecordList) ? null : personPayRecordList.get(0);
    }
    /**
     * 回调数据组织
     *
     * @param
     * @return
     */
    public  String callBack(PersonRefundRecord personRefundRecord ,FuQianLaRefundQueryBackContract reFundReqContract){

        try {
            Map<String,Object> responseMap=new HashMap<>();
            Map<String,Object> mmp=new HashMap<>();
            responseMap.put("order_id",personRefundRecord.getOrderId());
            responseMap.put("pay_type",PersonPayType.getEnum(personRefundRecord.getChannel()).getValue());
            responseMap.put("personal_pay_price",new BigDecimal(reFundReqContract.getRet_data().getRefund_amount()).divide(new BigDecimal(100)));
            responseMap.put("third_flow_id",reFundReqContract.getRet_data().getTxn_id());
            responseMap.put("pay_success",PayCommonType.getEnum(reFundReqContract.getRet_data().getStatus()).getValue());
            responseMap.put("refund_id",personRefundRecord.getId());
            mmp.put("code",0);
            mmp.put("msg","success");
            mmp.put("data",responseMap);
            String json=JsonUtils.toJson(mmp);
            String msg=JsonUtils.toJson(reFundReqContract);
            logger.info("回调场景日志:"+json);
            return HttpclientUtils.postJSONResult(personRefundRecord.getNotifyurl(), json);
        } catch (Exception e) {
            logger.error("回调退款结果异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return null;

    }
    /**
     * 添加退款记录
     *
     * @param
     * @return
     */
    public PersonRefundRecord saveRefundRecord(ReFundReqContract reFundReqContract,FuQianLaRefundQueryBackContract refundQueryBackContract,Map<String,String> mmp){
        int count=0;
        PersonRefundRecord refundRecord  = new PersonRefundRecord();
        try {
            refundRecord.setChannel(reFundReqContract.getChannel());
            refundRecord.setId(RandomUtils.bsonId());
            refundRecord.setCreateTime(new Date());
            refundRecord.setOrderId(reFundReqContract.getOrderNo());
            refundRecord.setNotifyurl(reFundReqContract.getNotifyUrl());
            refundRecord.setRefundAmount(reFundReqContract.getOrderAmount().intValue());
            refundRecord.setRefundStatus(PayConstant.payOrderRefundIng);
            FuQianLaRefundQueryBackContract.ChargeData chargeData=refundQueryBackContract.getRet_data();
            Map<String,String> requestMap= JsonUtils.toObj(mmp.get(PayConstant.PRFUND_REQUEST),Map.class);
            refundRecord.setRefundNo(requestMap.get("refund_no"));
            refundRecord.setTxnId(requestMap.get("txn_id"));
            refundRecord.setRefundReason(requestMap.get("refund_reason"));
            if(chargeData!=null){
                refundRecord.setExCode(chargeData.getEx_code());
                refundRecord.setExMsg(chargeData.getEx_msg());
                refundRecord.setRefundStatus(PayCommonType.getEnum(chargeData.getStatus()).getValue());
                if(chargeData.getComplete_time()!=null){
                    refundRecord.setCompleteTime(DateUtil.getDateFromString(chargeData.getComplete_time(), "yyyyMMddHHmmss"));
                }
                refundRecord.setReceiveTime(DateUtil.getDateFromString(chargeData.getReceive_time(), "yyyyMMddHHmmss"));
            }else{
                refundRecord.setRefundStatus(PayConstant.payOrderRefundIng);
                refundRecord.setRetryNum(0);
                Date now = new Date();
                Date afterDate = new Date(now.getTime() + 600000);
                refundRecord.setNextRetryTime(afterDate);
            }
            refundRecord.setUpdateTime(new Date());
            count = refundRecordMapper.insertSelective(refundRecord);
        } catch (Exception e) {
            logger.error("增加退款记录异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return refundRecord;

    }
    /**
     * 修改退款记录状态信息等
     *
     * @param
     * @return
     */
    public int updateRefundRecord(PersonRefundRecord personRefundRecord,FuQianLaRefundQueryBackContract refundQueryBackContract){
        int count = 0;
        try {
            FuQianLaRefundQueryBackContract.ChargeData chargeData=refundQueryBackContract.getRet_data();
            PersonRefundRecord refundRecord = new PersonRefundRecord();
            refundRecord.setRefundStatus(PayCommonType.getEnum(chargeData.getStatus()).getValue());
            if(chargeData.getComplete_time()!=null){
                refundRecord.setCompleteTime(DateUtil.getDateFromString(chargeData.getComplete_time(), "yyyyMMddHHmmss"));
            }
            refundRecord.setUpdateTime(new Date());
            refundRecord.setReceiveTime(DateUtil.getDateFromString(chargeData.getReceive_time(), "yyyyMMddHHmmss"));
            refundRecord.setExCode(chargeData.getEx_code());
            refundRecord.setExMsg(chargeData.getEx_msg());
            PersonRefundRecordExample personRefundRecordExample = new PersonRefundRecordExample();
            personRefundRecordExample.createCriteria().andIdEqualTo(personRefundRecord.getId());
            count = refundRecordMapper.updateByExampleSelective(refundRecord,personRefundRecordExample);
        } catch (Exception e) {
            logger.error("修改退款记录异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return count;

    }

    /**
     * 修改退款记录状态信息等
     *
     * @param
     * @return
     */
    public int updateRefundRecord(String orderNo,Integer retryNum,Date nextRetryTime,FuQianLaRefundQueryBackContract refundQueryBackContract ){
        int count = 0;
        try {
            FuQianLaRefundQueryBackContract.ChargeData chargeData=refundQueryBackContract.getRet_data();
            PersonRefundRecord refundRecord = new PersonRefundRecord();
            refundRecord.setRefundStatus(PayCommonType.getEnum(chargeData.getStatus()).getValue());
            if(chargeData.getComplete_time()!=null){
                refundRecord.setCompleteTime(DateUtil.getDateFromString(chargeData.getComplete_time(), "yyyyMMddHHmmss"));
            }
            refundRecord.setUpdateTime(new Date());
            refundRecord.setReceiveTime(DateUtil.getDateFromString(chargeData.getReceive_time(), "yyyyMMddHHmmss"));
            refundRecord.setExCode(chargeData.getEx_code());
            refundRecord.setExMsg(chargeData.getEx_msg());
            refundRecord.setRetryNum(retryNum);
            refundRecord.setNextRetryTime(nextRetryTime);
            refundRecord.setRefundNo(chargeData.getRefund_no());
            PersonRefundRecordExample personRefundRecordExample = new PersonRefundRecordExample();
            personRefundRecordExample.createCriteria().andOrderIdEqualTo(orderNo).andTxnIdEqualTo(chargeData.getTxn_id());
            count = refundRecordMapper.updateByExampleSelective(refundRecord,personRefundRecordExample);
        } catch (Exception e) {
            logger.error("修改退款记录异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return count;

    }
    /**
     * 添加退款日志记录
     *
     * @param
     * @return
     */
    public int savePersonPayRefundLog(PersonPayRefundLog refundLog,ReFundReqContract reFundReqContract,PersonRefundRecord personRefundRecord) {
        int count = 0;
        try {

            refundLog.setId(RandomUtils.bsonId());
            if(reFundReqContract!=null){
                refundLog.setAmount(reFundReqContract.getOrderAmount().intValue());
                refundLog.setOrderNo(reFundReqContract.getOrderNo());
                refundLog.setChannel(reFundReqContract.getChannel());
                refundLog.setNotifyUrl(reFundReqContract.getNotifyUrl());
            }else{
                refundLog.setAmount(personRefundRecord.getRefundAmount());
                refundLog.setOrderNo(personRefundRecord.getOrderId());
                refundLog.setChannel(personRefundRecord.getChannel());
                refundLog.setNotifyUrl(personRefundRecord.getNotifyurl());
            }
            refundLog.setCompleteTime(new Date());
            refundLog.setReceiveTime(new Date());
            count = refundLogMapper.insertSelective(refundLog);
        } catch (Exception e) {
            logger.error("添加日志异常：" + e.getLocalizedMessage());
            e.printStackTrace();
        }
        return count;
    }


}
