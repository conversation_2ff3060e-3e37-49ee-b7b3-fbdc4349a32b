package com.fenbeitong.fenbeipay.core.model.vo.fenbeitong.account;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/8/12.
 */
public class AccountFbbIssuingBillVo {


    private String orderId;

    private Integer accountId;

    private BigDecimal number;

    private BigDecimal recallNumber;

    public BigDecimal getRecallNumber() {
        return recallNumber;
    }

    public void setRecallNumber(BigDecimal recallNumber) {
        this.recallNumber = recallNumber;
    }

    private String id;

    private String name;

    private String phone;

    private String deptId;

    private String deptName;

    private Integer status;

    private String reason;

    private String comment;

    private Date createTime;

    public AccountFbbIssuingBillVo() {
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
