package com.fenbeitong.fenbeipay.core.constant.msg;

/**
 * User: wh
 * Date: 2017/5/4 下午9:12
 * Desc:
 */
public interface CommonConstant {

    public static final String FBB_TASK_GRANT_PUSH_TITLE = "分贝币到账";

    public static final String FBB_TASK_GRANT_PUSH_CONTENT = "您收到{0}个分贝币，点击查看详情";
    public static final String FBB_GRANT_PUSH_CONTENT = "您收到{0}个分贝币，点击查看详情";
    public static final String FBB_GRANT_NOTICE_CONTENT = "您收到{0}个分贝币";
    /**
     * 分贝币流水
     **/
    public static final String FBB_TASK_GRANT_PUSH_MSGTYPE_200 = "200";
    /**
     * 分贝券列表
     **/
    public static final String FBQ_TASK_GRANT_PUSH_MSGTYPE_210 = "210";
    public static final String FBB_TASK_GRANT_PUSH_MSGTYPE_0 = "0";

    public static final String FBB_TASK_RECALL_PUSH_TITLE = "分贝币撤回";

    public static final String FBB_TASK_RECALL_PUSH_CONTENT = "您被撤回{0}个分贝币";

    public static final String CASHIER_REFUND_XE_DESC = "退款交易被作废，需要扣除已退金额";

    public static final String NEXTLINE = "\n";
    int callBackMax = 11;

    public static final String LOCALHOST = "localhost";

    String REFUND_TIP = "注意：退款将原路返回，最晚7个工作日退还到帐";

}
