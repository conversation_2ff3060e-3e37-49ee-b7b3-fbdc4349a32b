package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.finhub.common.constant.BankNameEnum;
import com.fenbeitong.finhub.common.utils.FinhubLogger;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * @ClassName BankConfigUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/27 7:32 下午
 * @Version 1.0
 **/

@Service
public class BankConfigUtil {

    public static final String ZBBANK_SWTICH = "ZBSWITCH_20231109"; // 有key表示切到新版（大账户），无key保持原有版本

    public static final String ZBBANK_SWTICH_COMPANY = "ZBSWITCH_COMPANY"; // 有key表示切到新版（大账户），无key保持原有版本

    public static final String ZBBANK_SWTICH_OPPO_COMPANY = "ZBSWITCH_OPPO"; // 有key表示切到新版（大账户），无key保持原有版本

    private static volatile RedisTemplate<String, Object> redisTemplate;

    private static RedisTemplate<String, Object> getRedisTemplate() {

        if (redisTemplate == null) {
            FinhubLogger.info("redisTemplate init in BankConfigUtil~~");
            redisTemplate = SpringUtils.getBean(RedisTemplate.class);
        }
        return redisTemplate;
    }

    public static boolean needCallBank(String bankName) {
        return BankNameEnum.isZBBank(bankName) ? (!zbSwitch(null)) : BankNameEnum.needCallBank(bankName);
    }

    private static boolean zbSwitch(String companyId) {
        if (StringUtils.isBlank(companyId)) {
            return getRedisTemplate().hasKey(ZBBANK_SWTICH);
        } else {
            return getRedisTemplate().hasKey(ZBBANK_SWTICH) ? !getRedisTemplate().opsForHash().hasKey(ZBBANK_SWTICH_OPPO_COMPANY, companyId) : getRedisTemplate().opsForHash().hasKey(ZBBANK_SWTICH_COMPANY, companyId);
        }
    }

    public static boolean needCallBank(String bankName, String companyId) {
        return BankNameEnum.isZBBank(bankName) ? (!zbSwitch(companyId)) : BankNameEnum.needCallBank(bankName);
    }

}
