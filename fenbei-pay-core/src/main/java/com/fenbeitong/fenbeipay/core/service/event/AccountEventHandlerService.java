package com.fenbeitong.fenbeipay.core.service.event;

import com.fenbeitong.eventbus.event.common.EnumKeyValuePair;
import com.fenbeitong.eventbus.event.crm.AccountChangeEvent;
import com.fenbeitong.eventbus.util.EventBus;
import com.fenbeitong.fenbeipay.core.enums.account.AccountEventType;
import com.fenbeitong.fenbeipay.core.model.dto.account.AccountChangeDTO;
import com.fenbeitong.finhub.common.constant.FundAccountSubType;
import com.fenbeitong.finhub.common.utils.BigDecimalUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.finhub.kafka.msg.pay.KafkaCompanyCardAcctChangeMsg;
import com.fenbeitong.finhub.kafka.producer.IKafkaProducerPublisher;
import com.luastar.swift.base.json.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 企业余额提醒消息发送
 */
@Service
public class AccountEventHandlerService {
    @Autowired
    private IKafkaProducerPublisher iKafkaProducerPublisher;

    /**
     * 企业余额提醒消息发送-新
     *
     * @param companyAcctModelSwitch 是否1+1新账户体系
     * @param accountSubType 子账户类型
     * @param companyId 企业ID
     * @param key 增加/减少
     * @param credit 额度
     * @param orderType 订单类型
     * @param operationAmount 操作金额
     * @param accountModel 账户类型:充值/授信
     */
    public void sendAccountChangeEventPlus(Boolean companyAcctModelSwitch, Integer accountSubType, String companyId, Integer key, BigDecimal credit,Integer orderType,BigDecimal operationAmount,Integer accountModel) {
        //scala低版本消息
        sendAccountChangeEventPlus(companyAcctModelSwitch, accountSubType, companyId, key, credit, orderType, operationAmount, accountModel, null, null);
    }

    public void sendAccountChangeEventPlus(Boolean companyAcctModelSwitch, Integer accountSubType, String companyId,
                                           Integer key, BigDecimal credit,Integer orderType,BigDecimal operationAmount,
                                           Integer accountModel, String bankAcctName, String bankAcctId) {
        try {
            //统一变更
            if (operationAmount != null){
                operationAmount = BigDecimalUtils.fen2yuan(operationAmount);
            }
            // 企业对公账户 需要把企业账户ID和企业账户名称发送给saas
            String data = companyId;
            if (FundAccountSubType.isComPublicCardAccount(accountSubType)||FundAccountSubType.isOverseaAcct(accountSubType)) {
                data = companyId + "," + bankAcctId + "," + bankAcctName;
            }
            AccountChangeEvent accountChangeEvent = new AccountChangeEvent(data, new EnumKeyValuePair(AccountEventType.getEnum(key).getKey(),
                    AccountEventType.getEnum(key).getValue()), scala.math.BigDecimal.javaBigDecimal2bigDecimal(credit),
                    companyAcctModelSwitch, accountSubType,
                    scala.Option.apply(orderType),scala.Option.apply(scala.math.BigDecimal.javaBigDecimal2bigDecimal(operationAmount)),scala.Option.apply(accountModel));
            FinhubLogger.info("[企业余额提醒消息发送],companyId={},credit={},key={},accountChangeEvent={}", companyId, credit, key, accountChangeEvent.toString());
            EventBus.publish(accountChangeEvent);
        } catch (Exception e) {
            FinhubLogger.error("[企业余额提醒消息发送],异常：{}", e.getLocalizedMessage());
        }
    }

    /**
     * 备用金账户动账通知
     * @param companyCardAccountChangeDTO 备用金账户动账通知
     */
    public void sendAccountChangeEvent4CompanyCardAcct(AccountChangeDTO companyCardAccountChangeDTO) {
        try {
            FinhubLogger.info("[备用金账户余额提醒消息发送],companyCardAccountChangeDTO={}",JsonUtils.toJson(companyCardAccountChangeDTO));
            KafkaCompanyCardAcctChangeMsg kafkaCompanyCardAcctChangeMsg = new KafkaCompanyCardAcctChangeMsg();
            kafkaCompanyCardAcctChangeMsg.setAccountModel(companyCardAccountChangeDTO.getAccountModel());
            kafkaCompanyCardAcctChangeMsg.setAccountSubType(companyCardAccountChangeDTO.getAccountSubType());
            kafkaCompanyCardAcctChangeMsg.setKey(companyCardAccountChangeDTO.getKey());
            kafkaCompanyCardAcctChangeMsg.setCompanyId(companyCardAccountChangeDTO.getCompanyId());
            kafkaCompanyCardAcctChangeMsg.setBalance(companyCardAccountChangeDTO.getBalance());
            kafkaCompanyCardAcctChangeMsg.setOrderType(companyCardAccountChangeDTO.getOrderType());
            kafkaCompanyCardAcctChangeMsg.setOperationAmount(companyCardAccountChangeDTO.getOperationAmount());
            kafkaCompanyCardAcctChangeMsg.setBankName(companyCardAccountChangeDTO.getBankName());
            iKafkaProducerPublisher.publish(kafkaCompanyCardAcctChangeMsg);
        } catch (Exception e) {
            FinhubLogger.error("[备用金账户余额提醒消息发送],异常：{}", e.getLocalizedMessage());
        }
    }
}
