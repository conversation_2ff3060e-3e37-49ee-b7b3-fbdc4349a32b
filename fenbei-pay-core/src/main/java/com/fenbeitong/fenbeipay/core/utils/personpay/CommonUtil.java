package com.fenbeitong.fenbeipay.core.utils.personpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.constant.core.UcMessageCode;
import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON>qi<PERSON> on 2018/8/20.
 */
public class CommonUtil {
    public  static Map<String , Object> validLogoutParam(String requestBody){
        JSONObject object= JSON.parseObject(requestBody);
        Object user_id =  object.get("user_id");
        Object company_id =object.get("company_id");
        Object uncheck_apply_config = object.get("uncheck_apply_config");
        analyzeObjects(user_id, company_id, uncheck_apply_config);
        Map<String,Object> params= Maps.newHashMap();

        params.put("user_id",user_id);
        params.put("company_id",company_id);
        params.put("uncheck_apply_config",uncheck_apply_config);
        return params;
    }

    public  static void analyzeObjects(Object... objects) {
        int i = 1;
        for (Object obj : objects) {
            if (obj == null || Objects.equals(obj, "")) {
                throw new FinhubException(UcMessageCode.ILLEGAL_ARGUMENT, "参数错误");
            }
            i++;
        }
    }
    public static int checkOrderStatus(long amount,long refundAmount){
        if(amount==refundAmount){
            return PayConstant.payOrderRefundSuccess;
        }else if(amount>refundAmount){
            return PayConstant.payOrderRefundPartSuccess;
        }else{
            return PayConstant.payOrderRefundIng;
        }
    }

    /***
     * 转换状态
     * @param amount
     * @param refundAmount
     * @return int
     */
    public static int checkPayOrderStatus(BigDecimal amount, BigDecimal refundAmount) {
        switch(amount.compareTo(refundAmount)){
            case 0:
                return PayConstant.payOrderRefundSuccess;
            case 1:
                return PayConstant.payOrderRefundPartSuccess;
            default:
                return PayConstant.payOrderRefundIng;
        }

    }
}
