package com.fenbeitong.fenbeipay.core.model.vo.vouchers;

import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
* @Description:    java类作用描述
* @ClassName:      VoucherPayDTO
* @Author:         zhangga
* @CreateDate:     2018/11/26 下午10:17
* @UpdateUser:
* @UpdateDate:     2018/11/26 下午10:17
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
@Data
public class VoucherPayDTO {
    private String voucherId;
    private BigDecimal balance;

    private BigDecimal currentOperationAmount;
    /**
     * 发券企业ID
     */
    private String voucherCompanyId;

    public VoucherPayDTO() {
    }

    public VoucherPayDTO(String voucherId, BigDecimal balance, BigDecimal currentOperationAmount) {
        this.voucherId = voucherId;
        this.balance = balance;
        this.currentOperationAmount = currentOperationAmount;
    }

    public VoucherPayDTO(String voucherId, BigDecimal balance, BigDecimal currentOperationAmount,String voucherCompanyId) {
        this.voucherId = voucherId;
        this.balance = balance;
        this.currentOperationAmount = currentOperationAmount;
        this.voucherCompanyId = voucherCompanyId;
    }
}
