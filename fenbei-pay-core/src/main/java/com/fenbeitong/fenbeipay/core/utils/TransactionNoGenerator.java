package com.fenbeitong.fenbeipay.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 生成随机编号
 *
 * <AUTHOR>
 */
public class TransactionNoGenerator {
    /**
     * 随机号时间格式
     */
    private String timeFormatter = "yyyyMMddHHmmss";

    private SimpleDateFormat sdf = null;

    private static SimpleDateFormat nextSecond = new SimpleDateFormat("SSS");

    /**
     * 秒（一秒有多少毫秒）
     */
    private long secondUnit = 1000;

    /**
     * 随机数字位数
     */
    private int randomNumLength = 3;

    /**
     * 编号前缀 PayTransactionNoPrefix
     */
    private String prefix = "";

    /**
     * 正向88
     * 逆向66
     */
    private String payWay = "88";

    /**
     * 上一次生成时间
     */
    private String preGenTime = "";

    /**
     * 当前时间最大请求数量
     */
    private double maxRequest;

    /**
     * 数字格式
     */
    private String numFormat;

    /**
     * 当前秒内生成订单号的随机数字
     */
    private Set<Long> currentSecondRandomNos = Collections.synchronizedSet(new HashSet<Long>());

    public TransactionNoGenerator() {
        sdf = new SimpleDateFormat(timeFormatter);
    }

    /**
     * @param randomNumLength 随机数字位数
     */
    public TransactionNoGenerator(int randomNumLength) {
        this.randomNumLength = randomNumLength;
        this.maxRequest = Math.pow(10, this.randomNumLength);
        this.numFormat = MessageFormat.format("%0{0}d", randomNumLength);
    }

    /**
     * @param timeFormatter   时间格式
     * @param randomNumLength 随机数字位数
     * @param payWay         是否颠倒
     */
    public TransactionNoGenerator(String timeFormatter, int randomNumLength, String payWay) {
        if (StringUtils.isNotBlank(timeFormatter)) {
            this.timeFormatter = timeFormatter.trim();
            sdf = new SimpleDateFormat(this.timeFormatter);
        }
        this.randomNumLength = randomNumLength;
        this.payWay = payWay;
        this.maxRequest = Math.pow(10, this.randomNumLength);
        this.numFormat = MessageFormat.format("%0{0}d", randomNumLength);
    }

    /**
     * @param prefix          前缀
     * @param timeFormatter   时间格式
     * @param randomNumLength 随机数字位数
     * @param payWay         是否颠倒
     */
    public TransactionNoGenerator(String prefix, String timeFormatter, int randomNumLength, String payWay) {
        if (StringUtils.isNotBlank(prefix)){
            this.prefix = prefix.trim();
        }
        if (StringUtils.isNotBlank(timeFormatter)) {
            this.timeFormatter = timeFormatter.trim();
            sdf = new SimpleDateFormat(this.timeFormatter);
        }
        this.randomNumLength = randomNumLength;
        this.payWay = payWay;
        this.maxRequest = Math.pow(10, this.randomNumLength);
        this.numFormat = MessageFormat.format("%0{0}d", randomNumLength);
    }

    public TransactionNoGenerator(String prefix,String payWay){
        if (StringUtils.isNotBlank(prefix)){
            this.prefix = prefix.trim();
        }
        this.timeFormatter = "yyyyMMddHHmmssSSS";
        sdf = new SimpleDateFormat(this.timeFormatter);
        this.randomNumLength = 4;
        this.payWay = payWay;
        this.maxRequest = Math.pow(10, this.randomNumLength);
        this.numFormat = MessageFormat.format("%0{0}d", randomNumLength);
    }

    /**
     * 生成随机号
     */
    public  synchronized String genNewId() {
        if (currentSecondRandomNos.size() >= maxRequest) {
            try {
                Thread.currentThread();
                //计算当前秒距离下一秒还有多少毫秒
                long sleepTime = secondUnit - Long.valueOf(nextSecond.format(new Date()));
                // 休息到下一秒
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        String currentTime = sdf.format(new Date());
        if (!preGenTime.equals(currentTime)) {
            preGenTime = currentTime;
            currentSecondRandomNos.clear();
        }
        // 随机数字
        long random = getRandom();
        currentSecondRandomNos.add(random);
        StringBuffer sbTxn = new StringBuffer();
        sbTxn.append(prefix);
        sbTxn.append(payWay);
        sbTxn.append(currentTime);
        sbTxn.append(String.format(numFormat, random));
        return sbTxn.toString();
    }

    /**
     * 获得随机数字
     *
     * @return
     */
    private Long getRandom() {
        Long random = Math.round(Math.random() * maxRequest);
        if (currentSecondRandomNos.add(random)) {
            return random;
        }
        return getRandom();
    }

}  

