package com.fenbeitong.fenbeipay.core.model.dto;

import com.fenbeitong.fenbeipay.core.utils.StereoMessageCode;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.DateUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 账单流水列表页查询
 * 与账单流水导出查询的区别是平台方、业务账户类型、账户类型单选
 */
@SuppressWarnings("serial")
@Getter
@Setter
@NoArgsConstructor
public class BillFlowPageQuery extends BaseQuery {

    /**
     * 平台方
     *
     * @see FundPlatformEnum
     * 1.FBT 2.ZBANK
     */
    private String fundPlatform;

    /**
     * 账户类型，参考 FundAccountSubType
     */
    private Integer accountSubType;


    /**
     * 业务账户类型
     *
     * @see com.fenbeitong.finhub.common.constant.FundAccountModelType
     */
    private Integer accountModel;


    /**
     * 用于区分对应账户（fenbei-pay）的哪个表，不作为查询条件
     * 根据fundPlatform、accountSubType、accountModel 计算得到
     */
    private Integer flowFlag;

    /**
     * 场景
     */
    private List<Integer> orderTypes;

    /**
     * 枚举映射场景 @see CategoryTypeMappingEnum
     */
    private Integer orderType;
    
    /**
     * 对手账户枚举code @see CollectionAccountEntityForBizDebitEnum
     */
    private Integer targetAcctCode;

    /**
     * 银行流水号
     */
    private String bankTransNo;

    /**
     * 请求银行订单号（原：平台上账流水号）
     */
    private String syncBankTransNo;

    /**
     * 相关单号
     */
    private String bizNo;


    /**
     * 企业 id
     */
    private String companyId;

    /**
     * 平台流水号（交易编码）
     */
    private String accountFlowId;

    /**
     * 操作人姓名
     * like
     */
    private String operationUserName;

    /**
     * 操作人所属公司ID
     */
    private String operationUserCompanyId;

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 交易类型
     * stereo 支持多选；web 单选
     *
     * @see FundAcctTradeType
     */
    private List<Integer> tradeTypes;

    /**
     * 业务类型：取里面部分枚举
     *
     * @see FundAcctCreditOptType  授信枚举业务类型
     * @see FundAcctDebitOptType  授信枚举业务类型
     * @see FundAcctGeneralOptType  授信枚举业务类型
     */
    private List<Integer> operationTypes;


    /**
     * -------------------------------------------------------------------------------------------------------------
     * 以下是 web 列表页独有的查询条件
     */

    // 银行名称
    @NotNull
    private String bankName;

    // 银行卡号
    @NotNull
    private String bankAccountNo;

    /**
     * receipt_status
     * costImageStatus 等同 receipt_status
     * <p>
     * 电子回单状态
     * 0-无需生成/1-生成成功/2-生成失败/3-未生成
     */
    private Integer receiptStatus;

    /**
     * 账单编号
     */
    private String billNo;
    
    /**
     * 对手账户
     */
    private List<String> targetAccounts;

    /**
     * -------------------------------------------------------------------------------------------------------------
     * 以上是 web 列表页独有的查询条件
     */


    // 是否来自 stereo 的查询
    private boolean isStereo = false;
    
    /**
     * 直接来自web请求，而不是从vo转化而来
     */
    private boolean directFromWeb;
    
    private String userId;
    
    private String userName;
    
    /**
     * 导出流水需要
     */
    private String taskSrc = "enterprise-web";


    public void checkExportTaskParam() {
        CheckUtils.checkNull(companyId, "companyId不能为空");
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            throw new FinhubException(StereoMessageCode.NO_DATA, "交易区间不能为空");
        }

        //操作时间
        int maxBetweenDays = 31;
        int betweenDays = DateUtils.getDaysBetweenDates(endTime, startTime);
        if (betweenDays > maxBetweenDays) {
            throw new FinhubException(StereoMessageCode.NO_DATA, "交易区间最大为 " + maxBetweenDays + " 天");
        }
    }

}
