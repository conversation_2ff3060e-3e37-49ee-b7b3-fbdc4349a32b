package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * @CreateDate: 2020年05月10日18:07:54
 * @Version: 4.0.0
 * 支付
 */
@Data
@ToString(callSuper = true)
public class SynAcctPublicReqVo extends AcctPublicBaseReqVo {

    /**
     * 操作渠道：
     *@see OperationChannelType
     */
    @NotNull
    private Integer operationChannel;

    /**
     * 操作人Id
     */
    @NotBlank
    private String operationUserId;

    /**
     * 操作人姓名
     */
    private String operationUserName;


}
