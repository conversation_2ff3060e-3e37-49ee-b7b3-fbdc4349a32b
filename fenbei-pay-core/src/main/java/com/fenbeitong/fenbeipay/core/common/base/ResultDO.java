package com.fenbeitong.fenbeipay.core.common.base;

import java.io.Serializable;


/**
 * 结果返回封装
 */
@SuppressWarnings("serial")
public class ResultDO<T> implements Serializable {

    Boolean isSuccess;
    Integer errCode;
    String errMsg;
    String errMsgForClient;
    T model;

    public ResultDO() {
    }

    public ResultDO(Boolean isSuccess, Integer errCode, String errMsg, String errMsgForClient) {
        this.isSuccess = isSuccess;
        this.errCode = errCode;
        this.errMsg = errMsg;
        this.errMsgForClient = errMsgForClient;
    }

    public ResultDO(Boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

    public Boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public Integer getErrCode() {
        return errCode;
    }

    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getErrMsgForClient() {
        return errMsgForClient;
    }

    public void setErrMsgForClient(String errMsgForClient) {
        this.errMsgForClient = errMsgForClient;
    }

    public T getModel() {
        return model;
    }

    public void setModel(T model) {
        this.model = model;
    }

}