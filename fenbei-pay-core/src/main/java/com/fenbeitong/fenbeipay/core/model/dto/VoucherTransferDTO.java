package com.fenbeitong.fenbeipay.core.model.dto;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * @Description: java类作用描述
 * @ClassName: VoucherTransferDTO
 * @Author: zhangga
 * @CreateDate: 2019/1/16 3:27 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/16 3:27 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class VoucherTransferDTO extends PersonTransferDTO {
    /**
     * 源账户ID
     */
    @NotBlank
    private String voucherId;

    /**
     *  v3.7.0 添加
     *  支付需要支付密码：
     *  v3.6.0设置过支付密码必须输入密码。没设置过可以直接通过。
     *  V3.7.0版本和之后版本必须输入密码
     */
    private String employeePwd;

    /**
     * 加密算法类型：01-AES(老版本)  02-RSA(新版本)
     */
    private String encryptType;

    /**
     * 是否打开弹框祝福
     *  0关闭1打开
     */
    private Integer openEnable;

    /**
     *  弹框背景url
     */
    private String backgroundUrl;

    /**
     * 背景图片名称
     */
    private String backgroundName;

    /**
     * 祝福文案
     */
    private String noticeContent;
}
