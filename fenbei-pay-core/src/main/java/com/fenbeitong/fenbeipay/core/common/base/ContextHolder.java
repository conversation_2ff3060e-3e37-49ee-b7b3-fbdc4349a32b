package com.fenbeitong.fenbeipay.core.common.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 
 * <AUTHOR> 
 * @Description: 通过上下文传递的信息可以放在这里维护
 * 
 * @date 2023-02-13 08:43:43 
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContextHolder {

	/**
     * 支付宝的授权码或者微信的登录凭证
     */
	private String authCode;
	
	/**
	 * 用户小程序 appSecret
	 */
	private String appSecret;
	
	/**
	 * 客户端版本 5.1.13
	 */
	private String clientVersion;
	
	/**
	 * 客户端类型 iOS Android WebApp
	 */
	private String clientType;
	
	/**
	 * 客户端ip
	 */
	private String clientIp;
	
	/**
	 * 用户id
	 */
	private String userId;
}
