package com.fenbeitong.fenbeipay.core.model.vo.personpay;

import java.math.BigDecimal;

/**
 * 支付完成调用接口
 * Created by mac on 17/12/29.
 */
public class OrderPayFinishContract {

    private String order_id;
    private BigDecimal personal_pay_price;

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public BigDecimal getPersonal_pay_price() {
        return personal_pay_price;
    }

    public void setPersonal_pay_price(BigDecimal personal_pay_price) {
        this.personal_pay_price = personal_pay_price;
    }
}
