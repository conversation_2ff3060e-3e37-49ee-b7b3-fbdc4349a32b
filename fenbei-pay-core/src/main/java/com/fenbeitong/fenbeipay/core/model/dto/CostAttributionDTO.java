package com.fenbeitong.fenbeipay.core.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/1/28 15:11
 * @description: 分贝券发放费用归属(费控)
 */
@Data
public class CostAttributionDTO implements Serializable {

    private static final long serialVersionUID = -2538629554549337167L;
    /**
     * 费用归属类别code
     */
    private String costTypeCode;

    /**
     * 费用归属类别名称
     */
    private String costTypeName;

    /**
     * 费用归属配置code
     */
    private String costConfCode;

    /**
     * 费用归属配置名称
     */
    private String costConfName;

}
