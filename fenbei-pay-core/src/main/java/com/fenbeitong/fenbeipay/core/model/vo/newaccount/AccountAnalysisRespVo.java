package com.fenbeitong.fenbeipay.core.model.vo.newaccount;


import com.fenbeitong.fenbeipay.api.model.vo.na.AccountRedcouponInfoVO;
import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 我的-账户与消费分析(新账户体系)ResponseVO
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountAnalysisRespVo extends BaseVo {

    private AccountGeneralRespVo accountGeneral;
    /**
     * 商务账户
     */
    private AccountSubRespVo businessAccountSub;
    /**
     * 个人账户
     */
    private AccountSubIndividualRespVo individualAccountSub;


    /**
     * 企业账户
     */
    private AccountSubRespVo enterpriseAccountSub;
    /**
     * 红包券
     */
    private AccountRedcouponInfoVO accountRedcouponInfo;


}
