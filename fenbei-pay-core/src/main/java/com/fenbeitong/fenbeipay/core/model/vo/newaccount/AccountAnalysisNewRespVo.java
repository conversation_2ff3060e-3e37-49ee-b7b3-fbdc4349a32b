package com.fenbeitong.fenbeipay.core.model.vo.newaccount;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountAnalysisNewRespVo extends BaseVo {

    private static final long serialVersionUID = 3671823879335625019L;

    //账户总额
    private AccountGeneralRespVo accountGeneral;

    //商务账户
    private AccountSubRespVo businessAccountSub;

    // 个人账户
    private AccountSubIndividualRespVo individualAccountSub;

    //授信账户
    private AccountCompanyCardRespVo accountCompanyCardRespVo;

    //对公账户
    private List<AccountPublicRespVo> accountPublicRespVos;


}
