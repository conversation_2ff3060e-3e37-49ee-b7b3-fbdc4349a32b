package com.fenbeitong.fenbeipay.core.utils.personpay;


import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayCallbackLog;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.FuQianLaPayReturnContract;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.FuQianLaPayReturnStatusContract;
import com.fenbeitong.fenbeipay.core.utils.DateUtil;
import com.luastar.swift.base.utils.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 日志操作
 * Created by mac on 18/1/10.
 */
public class FuQianLaLogOperateUtil {

    private static final Logger logger = LoggerFactory.getLogger(FuQianLaLogOperateUtil.class);

    /**
     * 处理查询日志
     *
     * @param fuQianLaPayReturnStatusContract
     * @param resBody
     * @return
     */
    public static PersonPayCallbackLog queryPersonPayCallbackLog(FuQianLaPayReturnStatusContract fuQianLaPayReturnStatusContract, String resBody) {
        PersonPayCallbackLog personPayCallbackLog = new PersonPayCallbackLog();
        personPayCallbackLog.setId(RandomUtils.bsonId());
        personPayCallbackLog.setReturnMsg(resBody);
        try {
            if (fuQianLaPayReturnStatusContract != null) {
                personPayCallbackLog.setAmount(Long.valueOf(fuQianLaPayReturnStatusContract.getRet_data().getAmount()));
                personPayCallbackLog.setChargeId(fuQianLaPayReturnStatusContract.getRet_data().getCharge_id());
                personPayCallbackLog.setCompleteTime(fuQianLaPayReturnStatusContract.getRet_data().getComplete_time() == null ? null :
                        DateUtil.getDateFromString(fuQianLaPayReturnStatusContract.getRet_data().getComplete_time(), "yyyyMMddHHmmss"));
                personPayCallbackLog.setReceiveTime(fuQianLaPayReturnStatusContract.getRet_data().getReceive_time() == null ? null :
                        DateUtil.getDateFromString(fuQianLaPayReturnStatusContract.getRet_data().getReceive_time(), "yyyyMMddHHmmss"));
                personPayCallbackLog.setMerchId(fuQianLaPayReturnStatusContract.getRet_data().getMerch_id());
                personPayCallbackLog.setVersion(fuQianLaPayReturnStatusContract.getVersion());
                personPayCallbackLog.setOptional(fuQianLaPayReturnStatusContract.getRet_data().getOptional());
                personPayCallbackLog.setOrderNo(fuQianLaPayReturnStatusContract.getRet_data().getOrder_no());
                personPayCallbackLog.setSignInfo(fuQianLaPayReturnStatusContract.getSign_info());
                personPayCallbackLog.setSignType(fuQianLaPayReturnStatusContract.getSign_type());
                personPayCallbackLog.setRetCode(fuQianLaPayReturnStatusContract.getRet_code());
                personPayCallbackLog.setRetInfo(fuQianLaPayReturnStatusContract.getRet_desc());
                personPayCallbackLog.setCreateTime(new Date());
                personPayCallbackLog.setType(PayConstant.FUQIANLA_TAXI);
                personPayCallbackLog.setOperateType(2);
                personPayCallbackLog.setStatus(fuQianLaPayReturnStatusContract.getRet_data().getStatus());
                personPayCallbackLog.setChannel(fuQianLaPayReturnStatusContract.getRet_data().getChannel());
                personPayCallbackLog.setDevice(fuQianLaPayReturnStatusContract.getRet_data().getDevice());
            }
        } catch (Exception e) {
            logger.error("处理查询日志异常：" + e.getLocalizedMessage());
        }
        return personPayCallbackLog;
    }


    /**
     * 处理回调日志
     *
     * @param fuQianLaPayReturnContract
     * @param requestBody
     * @return
     */
    public static PersonPayCallbackLog callbackLogOperate(FuQianLaPayReturnContract fuQianLaPayReturnContract, String requestBody) {
        PersonPayCallbackLog personPayCallbackLog = new PersonPayCallbackLog();
        personPayCallbackLog.setId(RandomUtils.bsonId());
        personPayCallbackLog.setReturnMsg(requestBody);
        try {
            if (fuQianLaPayReturnContract != null) {
                personPayCallbackLog.setAmount(Long.valueOf(fuQianLaPayReturnContract.getAmount()));
                personPayCallbackLog.setChargeId(fuQianLaPayReturnContract.getCharge_id());
                personPayCallbackLog.setCompleteTime(fuQianLaPayReturnContract.getComplete_time() == null ? null :
                        DateUtil.getDateFromString(fuQianLaPayReturnContract.getComplete_time(), "yyyyMMddHHmmss"));
                personPayCallbackLog.setReceiveTime(fuQianLaPayReturnContract.getReceive_time() == null ? null :
                        DateUtil.getDateFromString(fuQianLaPayReturnContract.getReceive_time(), "yyyyMMddHHmmss"));
                personPayCallbackLog.setMerchId(fuQianLaPayReturnContract.getMerch_id());
                personPayCallbackLog.setVersion(fuQianLaPayReturnContract.getVersion());
                personPayCallbackLog.setOptional(fuQianLaPayReturnContract.getOptional());
                personPayCallbackLog.setOrderNo(fuQianLaPayReturnContract.getOrder_no());
                personPayCallbackLog.setSignInfo(fuQianLaPayReturnContract.getSign_info());
                personPayCallbackLog.setSignType(fuQianLaPayReturnContract.getSign_type());
                personPayCallbackLog.setRetCode(fuQianLaPayReturnContract.getRet_code());
                personPayCallbackLog.setRetInfo(fuQianLaPayReturnContract.getRet_info());
                personPayCallbackLog.setCreateTime(new Date());
                personPayCallbackLog.setType(PayConstant.FUQIANLA_TAXI);
                personPayCallbackLog.setOperateType(1);
            }
        } catch (Exception e) {
            logger.error("处理回调日志异常：" + e.getLocalizedMessage());
        }
        return personPayCallbackLog;
    }

}
