package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.api.model.dto.paycenter.common.CashierPayCommonJsonDto;
import com.fenbeitong.fenbeipay.api.model.vo.BaseVo;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 收银创建+支付流水请求VO
 *
 * <AUTHOR>
 */
@Data
@ToString
@Accessors(chain = true)
public class CashierAcctPublicPayTradeReqVo extends BaseVo {


    @NotBlank
    private String employeeId;
    @NotBlank
    private String companyId;

    /**
     * 场景方订单类型
     */
    @NotNull
    private Integer orderType;

    /**
     * 因公扣款账户2商务3个人4企业
     */
    private Integer accountSubType;

    /**
     * 订单渠道来源
     */
    private Integer orderChannelType;

    @NotBlank
    private String fbOrderId;
    /**
     * 订单名称
     */
    @NotBlank
    private String fbOrderName;
    /**
     * 订单快照信息
     */
    @NotBlank
    private String fbOrderSnapshot;
    /**
     * 订单总金额
     */
    @NotNull
    @Min(0)
    private BigDecimal totalPayPrice = BigDecimal.ZERO;

    //===================个人支付==============

    /**
     * 银行支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal amountBank = BigDecimal.ZERO;
    /**
     * 企业应支付金额
     */
    @NotNull
    @Min(0)
    private BigDecimal companyPayPrice = BigDecimal.ZERO;


    /**
     * 企业支付时，在收银台不结算的金额（如保险，由场景单独扣除保险）单位为分，BigDecimal类型
     */
    private BigDecimal companyNoSettlePrice = BigDecimal.ZERO;

    /**
     * 企业规则中支付超规金额，如果不填，默认为companyPayPrice中的金额
     */
    private BigDecimal companyPayRulePrice;


    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount = BigDecimal.ZERO;


    /**
     * 交易超时时间
     * @since 2.3.0
     */
    private Date deadlineTime;

    /**
     * 公用场景
     */
    @NotNull
    private CashierPayCommonJsonDto commonJson;

    /**
     * 自定义字段（json）
     */
    private String customExt;


    /**
     * 业务模式:1.pop 2托管 3采销
     * @see com.fenbeitong.finhub.common.constant.BusinessModeEnum
     */
    private Integer businessMode;

    /**
     * 是否提供发票:1提供 0不提供
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideStatusEnum
     */
    private Integer invoiceProvideStatus;

    /**
     * 开票类型
     * @see com.fenbeitong.finhub.common.constant.SceneInvoiceTypeEnum
     */
    private Integer sceneInvoiceType;

    /**
     * 开票方类型
     * @see com.fenbeitong.finhub.common.constant.InvoiceProvideTypeEnum
     */
    private Integer invoiceProvideType;

    /**
     * 发票供应商名称
     */
    private String invoiceProvideName;

    /**
     * @see OperationChannelType
     */
    private Integer  operationChannelType = OperationChannelType.NORMAL.getKey();

    /**
     * 交易说明
     */
    private String operationDescription;
    //===================银行卡支付==============
    /**
     * 银行交易流水id
     */
    private String bankTransNo;
    /**
     * 开户：银行卡号
     */
    @NotBlank
    private String bankAccountNo;
    /**
     * 银行名称简写
     */
    @NotBlank
    private String bankName;

    /**
     * 收款方:开户行
     */
    @NotBlank
    private String sellerBankNo;
    /**
     * 收款方:银行开户行名称
     */
    @NotBlank
    private String sellerBankName;

    /**
     * 收款方：收款户名：银行账户名称（有可能人名或公司名）
     */
    @NotBlank
    private String sellerBankAcctName;


    /**
     * 收款方：银行编码(提现至非绑定账户时必填)
     */
    private String sellerAcctBankCode;


    /**
     * 收款方：开户银行联行号(提现至非绑定账户时必填)
     */
    private String sellerBankBrnNo;

    /**
     * 用途
     */
    private String paymentPurpose;

    public void checkReq(CashierAcctPublicPayTradeReqVo createPayTradeReqVo) {
        ValidateUtils.validate(createPayTradeReqVo);
        /*if (createPayTradeReqVo.getTotalPayPrice().compareTo(amountBank)==0){
            throw new FinPayException(ILLEGAL_ARGUMENT);
        }*/
    }
}
