package com.fenbeitong.fenbeipay.core.model.vo.paycenter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 因公批量支付参数信息
 */
public class PublicBatchPayParameterVo {

    private BigDecimal totalAmount;
    private Integer businessType;
    private Integer orderType;
    //当前消费/退款公司id（必填）
    private String currentCompanyId;
    private List<PublicPayParameterVo> orderList;

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getCurrentCompanyId() {
        return currentCompanyId;
    }

    public void setCurrentCompanyId(String currentCompanyId) {
        this.currentCompanyId = currentCompanyId;
    }

    public List<PublicPayParameterVo> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<PublicPayParameterVo> orderList) {
        this.orderList = orderList;
    }
}
