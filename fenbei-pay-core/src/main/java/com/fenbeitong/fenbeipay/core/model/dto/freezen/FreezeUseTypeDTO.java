package com.fenbeitong.fenbeipay.core.model.dto.freezen;

import com.fenbeitong.fenbeipay.api.constant.enums.account.FreezenUseType;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: FreezeUseTypeDTO
 * @ProjectName fenbei-pay
 * @Description: TODO
 * @author: wh
 * @date 2019/3/20 17:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreezeUseTypeDTO {

    private Boolean account=false;

    private Boolean vouchers=false;

    private Boolean business=false;

    private Boolean individual=false;

    private Boolean enterprise=false;

    public static FreezeUseTypeDTO of(FreezenUseType freezenUseType) {
        FreezeUseTypeDTO freezeUseTypeDTO=  new FreezeUseTypeDTO();
        if (ObjUtils.isEmpty(freezenUseType)) {
            return new FreezeUseTypeDTO();
        }
        return freezeUseTypeDTO.makeFreezeUseTypeDTO(freezenUseType);
    }

    private  FreezeUseTypeDTO makeFreezeUseTypeDTO(FreezenUseType freezenUseType) {
        if (FreezenUseType.ACCOUNT_CONSUME.equals(freezenUseType)){
            account =true;
        }
        if (FreezenUseType.INDIVIDUAL_VOUCHERS.equals(freezenUseType)){
            vouchers =true;
        }
        if (FreezenUseType.BUSINESS_CONSUME.equals(freezenUseType)){
            business =true;
        }
        if (FreezenUseType.INDIVIDUAL_CONSUME.equals(freezenUseType)){
            individual =true;
        }
        if (FreezenUseType.ENTERPRISE_CONSUME.equals(freezenUseType)){
            enterprise =true;
        }
        return new FreezeUseTypeDTO(account, vouchers, business, individual, enterprise);
    }

    public boolean isAccountSub() {
        return business||individual||enterprise||vouchers;
    }


}
