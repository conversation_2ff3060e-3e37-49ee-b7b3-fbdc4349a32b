package com.fenbeitong.fenbeipay.core.common.base.export;

import lombok.Data;

/**
 * @Description: java类作用描述
 * @ClassName: Column
 * @Author: zhangga
 * @CreateDate: 2019/4/10 12:27 PM
 * @UpdateUser:
 * @UpdateDate: 2019/4/10 12:27 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class Column {
    /**
     * 标题
     */
    private String title;

    /**
     * 字段[获取的json中的key]
     */
    private String field;

    /**
     * 类型
     */
    private String type;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 默认值
     */
    private String defaultValue;


    private String filter;

    public Column(String field, String title, String type, Integer width, String defaultValue) {
        this.field = field;
        this.title = title;
        this.type = type;
        this.width = width;
        this.defaultValue = defaultValue;
    }
}

