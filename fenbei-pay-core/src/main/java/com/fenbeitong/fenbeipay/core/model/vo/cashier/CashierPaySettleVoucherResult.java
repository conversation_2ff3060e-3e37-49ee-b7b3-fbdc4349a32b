package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;


@Data
@ToString(callSuper = true)
public class CashierPaySettleVoucherResult extends BaseVo {

    BigDecimal voucherTotalPayAmount;
    BigDecimal voucherOrderInvoiceAmount;
    BigDecimal voucherInvoiceAmount;

    /**
     * 红包账户分贝券金额
     */
    BigDecimal voucherRedcouponAmount;
    /**
     * 个人账户分贝券金额
     */
    BigDecimal voucherIndividualAmount;

}
