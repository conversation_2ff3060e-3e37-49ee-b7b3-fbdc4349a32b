package com.fenbeitong.fenbeipay.core.model.vo.acctpublic;

import com.fenbeitong.fenbeipay.api.model.dto.BaseDTO;
import com.fenbeitong.fenbeipay.api.model.dto.acctpublic.resp.AcctPublicSimpleInfoRespRPCDTO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Title: BankCardBaseReqDTO
 * @ProjectName fenbei-pay
 * @Description: 卡的基本信息
 * @author: wh
 * @date 2019/9/20 10:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcctPublicFindRespVo extends BaseDTO {

    /**
     * 启用和停用的卡
     */
    private List<AcctPublicSimpleInfoRespRPCDTO> simpleInfoRespRPCDTOS= Lists.newArrayList();



}
