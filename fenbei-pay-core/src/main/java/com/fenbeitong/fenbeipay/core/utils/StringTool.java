package com.fenbeitong.fenbeipay.core.utils;

import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringTool {
    static final String PATTERNSTR = "[0-9]";

    /**
     * 半角转全角
     *
     * @param input String.
     * @return 全角字符串.
     */
    public static String ToSBC(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == ' ') {
                c[i] = '\u3000';
            } else if (c[i] < '\177') {
                c[i] = (char) (c[i] + 65248);
            }
        }
        return new String(c);
    }

    /**
     * 全角转半角
     *
     * @param input String.
     * @return 半角字符串
     */
    public static String ToDBC(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        char c[] = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        String returnString = new String(c);
        return returnString;
    }

    /**
     * 给字符串去空格
     */
    public static String trimString(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return str;
        }
        return str.trim().replace(" ", "");
    }

    /**
     * 手机或电话号码只显示前3位：157XXXXXXXX
     * 给字符串去空格
     */
    public static String replaceString(String str, String replace) {
        if (Objects.isNull(str) || Objects.isNull(replace)) {
            return null;
        }
        // 只允数字
        Pattern p2 = Pattern.compile(PATTERNSTR);
        Matcher m = p2.matcher(str.substring(3, str.length()));
        //替换与模式匹配的所有字符（即非数字的字符将被""替换）
        String trim = m.replaceAll(replace).trim();
        return str.substring(0, 3) + trim;
    }


    /**
      * @Description: 匹配key是否存在queryString里面
      * @Param: [queryString, key]
      * @return: 存在true 不存在false
      */
    public static Boolean existString(String str,String key){
        if (Objects.isNull(str)){
            return null;
        }
       return str.contains(key);
    }
}
