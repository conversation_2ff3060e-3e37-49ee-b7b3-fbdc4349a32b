package com.fenbeitong.fenbeipay.core.common.base;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/** 
 * <AUTHOR> 
 * @Description: 支付上下文 可以传递信息
 * 
 * @date 2023-02-13 08:43:16 
*/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PayContext {

	private static final ThreadLocal<ContextHolder> CONTEX = ThreadLocal.withInitial(() -> ContextHolder.builder().build());
	
	/**
	 * 设置authCode
	 * @param authCode
	 */
	public static void setAuthCode(String authCode) {
		CONTEX.get().setAuthCode(authCode);
	}
	
	/**
	 * 设置微信小程序 AppSecret
	 * @param secret
	 */
	public static void setAppSecret(String secret) {
		CONTEX.get().setAppSecret(secret);
	}
	
	/**
	 * 初始化客户端ip
	 */
	public static void setClientIp(String ip) {
		CONTEX.get().setClientIp(ip);
	}
	
	/**
	 * 获取客户端IP
	 */
	public static String getClientIp() {
		return CONTEX.get().getClientIp();
	}
	
	/**
	 * 获取微信小程序 AppSecret
	 * @return
	 */
	public static String getAppSecret() {
		return CONTEX.get().getAppSecret();
	}
	
	/**
	 * 获取然后清理AuthCode
	 * @return
	 */
	public static String getAndClearAuthCode() {
		String authCode = CONTEX.get().getAuthCode();
		CONTEX.remove();
		return authCode;
	}
	
	/**
	 * 获取AuthCode
	 * @return
	 */
	public static String getAuthCode() {
		return CONTEX.get().getAuthCode();
	}
	
	/**
	 * 清理上下文
	 */
	public static void clearConext() {
		CONTEX.remove();
	}
	
	/**
	 * 设置客户端信息
	 * @param clientVersion
	 * @param clientType
	 */
	public static void setClientInfor(String clientVersion, String clientType) {
		ContextHolder holder = CONTEX.get();
		holder.setClientType(clientType);
		holder.setClientVersion(clientVersion);
	}
	
	/**
	 * 设置客户端类型
	 * @return
	 */
	public static String getClientType() {
		return CONTEX.get().getClientType();
	}
	
	/**
	 * 设置客户端版本
	 */
	public static String getClientVersion() {
		return CONTEX.get().getClientVersion();
	}
	
	public static String getUserId() {
		return CONTEX.get().getUserId();
	}
	
	public static void setUserId(String userId) {
		CONTEX.get().setUserId(userId);
	}
}
