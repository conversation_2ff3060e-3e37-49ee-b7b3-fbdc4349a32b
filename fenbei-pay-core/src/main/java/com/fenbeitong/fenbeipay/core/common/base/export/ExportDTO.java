package com.fenbeitong.fenbeipay.core.common.base.export;

import com.alibaba.fastjson.JSONObject;
import com.luastar.swift.base.utils.ValidateUtils;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: java类作用描述
 * @ClassName: ExportDTO
 * @Author: zhangga
 * @CreateDate: 2019/4/10 11:45 AM
 * @UpdateUser:
 * @UpdateDate: 2019/4/10 11:45 AM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class ExportDTO {
    /**
     * [必填]创建任务的系统名称
     **/
    @NotBlank
    private String taskSrc;
    /**
     * [必填]导出任务名称
     **/
    @NotBlank
    private String taskName;
    /**
     * 任务分类标识, 各系统可创建自己的分类[例如: 50:外卖订单导出, 10:用户列表导出]
     * 任务分类ID，可根据需求自行维护任务分类，用于任务查询的筛选操作
     **/
    private Integer taskCategory;
    /**
     * 任务分类名称
     **/
    private String taskCategoryName;
    /**
     * 文档说明。此段说明将加在文档头部第一行, 如需换行请用换行符"\n"
     **/
    private String description;
    /**
     * 下载文件的文件名,如果为空将使用任务名(无需加文件后缀)
     **/
    private String fileName;

    /**
     * [必填]创建者ID,例如用户ID
     **/
    @NotBlank
    private String createId;
    /**
     * 数据总量
     **/
    private String totalSize;
    /**
     * 开始页号[默认:1]
     **/
    @NotNull
    @Min(1)
    private Integer page;
    /**
     * 每页条数[默认:100]
     **/
    @NotNull
    @Min(1)
    @Max(200)
    private Integer size;
    /**
     * [必填]表格设置
     **/
    @NotNull
    private List<Column> columns;
    /**
     * [必填]查询体
     **/
    @NotNull
    private JSONObject query;
    /**
     * 设置查询协议类型:json[默认]、post[x-www-form-urlencoded]、get
     **/
    private String queryType;
    /**
     * [必填]分页查询路径,详见备注
     **/
    @NotBlank
    private String queryPath;
    /**
     * 设置返回数据中获取列表数据的根路径,支持子对象路径;默认:data
     **/
    private String dataPath;
    /**
     * 创建者名称
     **/
    @NotBlank
    private String createName;
    /**
     * 员工部门向上爬至跟部门的ID汇总, 用于部门领导查看员工导出任务
     **/
    private String parentIds;
    /**
     * 数据过滤定义
     **/
    private List<Filter> filters;

    public void dtoBuild(JSONObject queryJson, String exportQueryUrl, String taskName, String operationUserId, String operationUserName, String parentIds, List<Column> columns, ExportQuery exportQuery) {
        BeanUtils.copyProperties(exportQuery, this);
        this.setTaskName(taskName);
        this.setCreateId(operationUserId);
        this.setCreateName(operationUserName);
        this.setPage(1);
        this.setSize(200);
        this.setColumns(columns);
        this.setQuery(queryJson);
        this.setQueryType("json");
        this.setQueryPath(exportQueryUrl);
        this.setParentIds(parentIds);
        ValidateUtils.validate(this);
    }

    public List<Column> getAccountColumns() {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("createTime", "交易时间", "String", 20, ""));
        columns.add(new Column("accountFlowId", "交易编号", "String", 30, ""));
        columns.add(new Column("operationTypeName", "交易类型", "String", 20, ""));
        columns.add(new Column("operationAmount", "交易金额", "Number", 10, ""));
        columns.add(new Column("balance", "余额", "String", 15, ""));
        columns.add(new Column("operationUserName", "操作人", "String", 15, ""));
        columns.add(new Column("orderTypeName", "场景", "String", 20, ""));
        columns.add(new Column("bizNo", "关联订单", "String", 20, ""));
        columns.add(new Column("operationDescription", "交易说明", "String", 20, ""));
        return columns;
    }

    public List<Column> getVoucherFlowColumns() {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("voucherFlowId", "消费流水编号", "String", 20, ""));
        columns.add(new Column("createTime", "创建时间", "String", 20, ""));
        columns.add(new Column("voucherName", "分贝券名称", "String", 10, ""));
        columns.add(new Column("businessTypeName", "消费分类", "String", 10, ""));
        columns.add(new Column("amount", "消费金额", "Number", 10, ""));
        columns.add(new Column("employeeName", "员工姓名", "String", 10, ""));
        columns.add(new Column("employeePhone", "手机号", "String", 15, ""));
        columns.add(new Column("employeeDepartmentFull", "部门", "String", 15, ""));
        columns.add(new Column("typeName", "状态", "String", 10, ""));
        columns.add(new Column("writeInvoiceTypeName", "开票类型", "String", 10, ""));
        return columns;
    }

    public List<Column> getVoucherColumns(boolean hasRedcoupon) {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("vouchersTaskId", "发券任务编号", "String", 20, ""));
        columns.add(new Column("employeeDepartmentFull", "部门", "String", 20, ""));
        columns.add(new Column("createTime", "发券时间", "String", 20, ""));
        columns.add(new Column("voucherName", "券名称", "String", 15, ""));
        columns.add(new Column("employeeName", "员工姓名", "String", 10, ""));
        columns.add(new Column("employeePhone", "手机号", "String", 15, ""));
        columns.add(new Column("canTransferName", "允许转赠", "String", 15, ""));
        columns.add(new Column("writeInvoiceTypeName", "开票类型", "String", 15, ""));
        columns.add(new Column("voucherDenomination", "发券金额", "Number", 10, ""));
        columns.add(new Column("voucherExpiryTime", "过期时间", "String", 10, ""));
        columns.add(new Column("recoveryAmount", "回收金额", "Number", 10, ""));
        columns.add(new Column("recoveryReason", "回收原因", "String", 20, ""));
        columns.add(new Column("dateOfExpense", "预算归属日期", "String", 20, ""));
        columns.add(new Column("taskDesc", "发放备注", "String", 25, ""));
        if (hasRedcoupon) {
            columns.add(new Column("deductionAccountTypeName", "扣款来源", "String", 20, ""));
        }
        return columns;
    }
    public List<Column> getRecoveryVoucherColumns() {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("recoveryTime", "回收时间", "String", 20, ""));
        columns.add(new Column("employeeDepartmentFull", "部门", "String", 20, ""));
        columns.add(new Column("voucherName", "券名称", "String", 15, ""));
        columns.add(new Column("operationUserName", "回收人", "String", 10, ""));
        columns.add(new Column("recoveryAmount", "回收金额", "Number", 10, ""));
        columns.add(new Column("recoveryReason", "回收原因", "String", 10, ""));
        columns.add(new Column("recoveryStatusName", "回收状态", "String", 20, ""));
        columns.add(new Column("recoveryTypeName", "回收类型", "String", 20, ""));
        columns.add(new Column("employeeName", "员工姓名", "String", 10, ""));
        columns.add(new Column("employeePhone", "手机号", "String", 15, ""));
        columns.add(new Column("voucherDenomination", "发券金额", "Number", 10, ""));
        columns.add(new Column("vouchersTaskId", "发券任务编号", "String", 15, ""));
        return columns;
    }

    public List<Column> getRedcouponColumns() {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("createTime", "创建时间", "String", 20, ""));
        columns.add(new Column("operationDescription", "交易类型", "String", 20, ""));
        columns.add(new Column("orderTypeName", "场景", "String", 15, ""));
        columns.add(new Column("operationAmount", "交易金额", "Number", 10, ""));
        columns.add(new Column("balance","余额","Number", 10, ""));
        columns.add(new Column("operationUserName", "员工姓名", "String", 10, ""));
        columns.add(new Column("operationUserPhone", "手机号", "String", 15, ""));
        columns.add(new Column("tradeDescription", "交易说明", "String", 15, "--"));
        return columns;
    }


    public List<Column> getStereoVoucherFlowColumns() {
        List<Column> columns = new ArrayList<>();
        columns.add(new Column("id", "流水编号", "String", 20, ""));
        columns.add(new Column("createTime", "操作时间", "String", 20, ""));
        columns.add(new Column("typeName", "操作类型", "String", 10, ""));
        columns.add(new Column("amountStr", "操作金额", "String", 10, ""));
        columns.add(new Column("employeeName", "券持有人姓名", "String", 10, ""));
        columns.add(new Column("employeePhone", "券持有人手机号", "String", 20, ""));
        columns.add(new Column("employeeDepartmentFull", "券持有人层级部门", "String", 40, ""));
        columns.add(new Column("bizNo", "相关编号", "String", 20, ""));
        columns.add(new Column("voucherId", "券ID", "String", 20, ""));
        columns.add(new Column("voucherName", "券名称", "String", 20, ""));
        columns.add(new Column("voucherEffectiveTime", "券有效期开始时间", "String", 20, ""));
        columns.add(new Column("voucherExpiryTime", "券有效期截止时间", "String", 20, ""));
        columns.add(new Column("businessTypeName", "场景", "String", 50, ""));
        columns.add(new Column("writeInvoiceTypeName", "券开票类型", "String", 10, ""));
        columns.add(new Column("marks", "备注", "String", 10, ""));
        return columns;
    }

}
