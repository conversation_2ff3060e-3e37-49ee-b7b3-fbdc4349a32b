package com.fenbeitong.fenbeipay.core.service.redis;

import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.utils.ObjUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Author : ZhangZhiJun
 * Date : 2017/10/12 下午3:51
 * Desc ：
 */
@Service
public class RedisService {

    private static final String LOCK = "start_lock";

    @Autowired
    private RedisDao redisDao;

    /**
     * 将数据放入redis
     *
     * @param key
     * @param redisData
     */
    public void saveToRedis(String key, Map<String, Object> redisData,int timeout) {
        redisDao.getHashOperations().putAll(key, redisData);
        redisDao.getRedisTemplate().expire(key, timeout, TimeUnit.MINUTES);
    }

    /**
     * 从缓存中获取数据
     *
     * @param key
     * @return
     */
    public Map<String, Object> getDataFormRedis(String key) {
        CheckUtils.checkEmpty(key, "参数不能为空！");
        return redisDao.getHashOperations().entries(key);
    }


    /**
     * 存入redis信息
     *
     * @param key
     * @param data
     * @param timeout
     */
    public void setDataToRedis(String key, String data, int timeout) {
        CheckUtils.create().addCheckEmpty(key, "key不能为空！")
                .addCheckEmpty(data, "data不能为空")
                .addCheckEmpty(timeout, "过期时间不能为空")
                .check();
        redisDao.getValueOperations().set(key, data, timeout, TimeUnit.SECONDS);
    }

    /**
     * 获取redis数据
     *
     * @param key
     * @return
     */
    public String queryDateFromReids(String key) {
        CheckUtils.create().addCheckEmpty(key, "key不能为空！")
                .check();
        return String.valueOf(redisDao.getValueOperations().get(key));
    }

    /**
     * 获取过期时间
     *
     * @param key
     * @return
     */
    public Long getExpire(String key) {
        return redisDao.getRedisTemplate().getExpire(key);
    }

    /**
     * 删除验证码
     *
     * @param key
     */
    public void deleteRedisKey(String key) {
        redisDao.getRedisTemplate().delete(key);
    }


    /**
     * 对redisLockKey增加锁，如果锁成功，说明无锁，锁失败，说明有锁
     * true加锁成功，false加锁失败
     * @param redisLockKey
     * @param lockSecond 锁定时长
     * @return
     */
    public boolean addAndCheckLock(String redisLockKey,int lockSecond) {
        //加锁成功，返回ok,否则为空
        String ok = redisDao.setDataToRedisNX(redisLockKey, LOCK, lockSecond);
        if (ObjUtils.isNotEmpty(ok)) {
            FinhubLogger.info("【Redis锁】addAndCheckLock，key:{},加锁成功{}", redisLockKey, ok);
            return true;
        }
        FinhubLogger.info("【Redis锁】addAndCheckLock，key:{},加锁失败，已有锁", redisLockKey);
        return false;
    }

    public RedisDao getRedisDao() {
        return this.redisDao;
    }
}
