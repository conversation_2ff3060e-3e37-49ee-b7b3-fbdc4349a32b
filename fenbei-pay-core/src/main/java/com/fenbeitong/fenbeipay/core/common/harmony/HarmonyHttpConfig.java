package com.fenbeitong.fenbeipay.core.common.harmony;

import com.jakewharton.retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

/**
 * <AUTHOR>
 */
//@Configuration
public class HarmonyHttpConfig {
//
//    @Value("${host.harmony}")
//    private String serverHarmony;
//
//    @Value("${okhttp3.logging.level:NONE}")
//    private HttpLoggingInterceptor.Level level;
//
//    @Autowired
//    @Qualifier("harmonyHttpRetrofit")
//    private Retrofit harmonyHttpRetrofit;
//
//    @Bean
//    public HarmonyHttpService getHarmonyHttpService() {
//        return harmonyHttpRetrofit.create(HarmonyHttpService.class);
//    }
//
//    @Bean(name="harmonyHttpRetrofit")
//    public Retrofit getRetrofit() {
//        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
//        logging.setLevel(level);
//        OkHttpClient client = new OkHttpClient.Builder()
//                .addInterceptor(logging)
//                .build();
//        Retrofit retrofit = new Retrofit.Builder()
//                .baseUrl(serverHarmony)
//                .client(client)
//                .addConverterFactory(JacksonConverterFactory.create())
//                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
//                .build();
//        return retrofit;
//    }
}