package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CashierRefundFinishCallBizVo extends BaseVo {



    private int refundPayStatus;
    /***
     * 分贝订单id
     */
    private String fbOrderId;
    /***
     * 收银台退款流水号
     */
    private String refundTxnId;

    /**
     * 售后场景逆向订单号
     */
    private String refundOrderId;

    /***
     * 分贝员工id
     */
    private String employeeId;
    /**
     * 公司id
     */
    private String companyId;


    /**
     * 企业支付总金额
     */
    private BigDecimal amountPublic = BigDecimal.ZERO;

    /**
     * 企业退款金额
     */
    private BigDecimal amountCompany;

    /**
     * 红包券退款金额
     */
    private BigDecimal amountRedcoupon;

    /**
     * 个人退款总金额
     */
    private BigDecimal amountPersonal = BigDecimal.ZERO;

    /**
     * 分贝币退款金额
     */
    private BigDecimal amountFbb = BigDecimal.ZERO;

    /**
     * 分贝券付款金额(单位：分)
     */
    private BigDecimal amountVoucher = BigDecimal.ZERO;

    /**
     * Database Column Remarks:
     *   个人账户分贝券
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cashier_order_refund_settlement.amount_voucher_individual
     *
     * @mbg.generated
     */
    private BigDecimal amountVoucherIndividual= BigDecimal.ZERO;

    /**
     * Database Column Remarks:
     *   红包账户分贝券
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column cashier_order_refund_settlement.amount_voucher_redcoupon
     *
     * @mbg.generated
     */
    private BigDecimal amountVoucherRedcoupon= BigDecimal.ZERO;

    /***
     * 使用分贝券数
     */
    private Integer countVoucher;

    /**
     * 三方付款金额(单位：分)
     */
    private BigDecimal amountThird = BigDecimal.ZERO;

    /**
     * 分贝券--订单开票(后开票)金额
     */
    private BigDecimal voucherOrderInvoiceAmount= BigDecimal.ZERO;

    /**
     * 分贝券--分贝券开票(先开票)金额
     */
    private BigDecimal voucherInvoiceAmount= BigDecimal.ZERO;

    /**
     *   报销金额
     */
    private BigDecimal refundAmountReimburseCompany = BigDecimal.ZERO;

    /**
     *   自费金额
     */
    private BigDecimal refundAmountReimburseSelf = BigDecimal.ZERO;

    /***
     * 退款渠道
     */
    private String thirdPayChannel;


    private String remark;

    /**
     * 账户模式：1授信模式2充值模式
     */
    private Integer accountModel;


    /**
     * 子账户id
     */
    private String accountSubId;

    /**
     * 个人银行卡退款金额
      */
    private BigDecimal bankPersonalRefundAmount;

    /**
     * 交易号
     */
    private String tradeNo;

    /**
     * 对应cashier_order_refund_settlement.refund_txn_id
     */
    private String cashierPayTxnId;
}
