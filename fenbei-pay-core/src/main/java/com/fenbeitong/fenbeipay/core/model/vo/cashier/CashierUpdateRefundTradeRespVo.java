package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;
import lombok.ToString;


@Data
@ToString(callSuper = true)
public class CashierUpdateRefundTradeRespVo extends CashierRefundTradeRespVo {

    //更新结果描述
    private String  updateResultDesc;

    /**
     *
     */
    private String  amountResultDesc;


    /**
     * 仍需退款金额描述
     * @return
     */
    public void toRefundString(CashierRefundSettlementRemainReturnStatVo remainRefundStat){
        StringBuffer sb = new StringBuffer();
        sb.append("退款订单号：");
        sb.append(remainRefundStat.getFbOrderId());
        sb.append("仍需退款总金额：");
        sb.append(remainRefundStat.getRefundRemainAmount());
        sb.append("企业仍需退款金额：");
        sb.append(remainRefundStat.getRefundPublicRemainAmount());
        sb.append("个人仍需退款金额：");
        sb.append(remainRefundStat.getRefundPersonalRemainAmount());
        amountResultDesc = sb.toString();
    }

}
