package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收银台-企业结算金额
 *
 * <AUTHOR>
 * @date 2019年12月11日15:59:31
 */
@Data
@ToString(callSuper = true)
public class CashierPayPublicCompanyBankEntVO extends BaseVo {
    /**
     * 订单号
     */
    private String fbOrderId;
    /**
     * 子账户ID
     */
    private String accountSubId;

    /**
     * 收方账号
     */
    private String receiveBankAccountNo;

    /**
     * 收款户名
     */
    private String receiveBankAccountName;

    /**
     * 收款开户行号
     */
    private String receiveBankCode;

    /**
     * 收款开户行名
     */
    private String receiveBankName;

    /**
     * 操作人姓名
     */
    private String operationUserName;

    /**
     * 操作人id
     */
    private String operationUserId;

    /**
     * 交易金额（分）
     */
    private BigDecimal transAmt;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 风控标志 1 need 2 not need
     */
    private Boolean needRisk;
    /**
     * 付款目的
     */
    private String remark;

    /**
     * 业务单据号多个
     */
    private String bizApplyNo;

}
