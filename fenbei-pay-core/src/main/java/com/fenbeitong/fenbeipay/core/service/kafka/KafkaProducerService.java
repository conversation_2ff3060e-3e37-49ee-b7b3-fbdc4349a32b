package com.fenbeitong.fenbeipay.core.service.kafka;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.finhub.common.constant.FinhubMessageCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.luastar.swift.base.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.FailureCallback;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.SuccessCallback;

import java.text.MessageFormat;


/**
 * kafka生产者service
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10
 */
@Slf4j
@Service
public class KafkaProducerService {

    @Autowired
    @Qualifier("kafkaTemplate")
    private KafkaTemplate<String, String> lowKafkaTemplate;

    /**
     * 分贝券结果推送消息
     * @param msg
     */
    public void sendVoucherResultNoticeMsg(KafkaVoucherResultNoticeMsg msg) {
        String messageJson = JsonUtils.toJson(msg);
        try {
            sendMessage("topic_voucher_result_notice", messageJson);
        } catch (Exception e) {
            log.error("[分贝券结果推送消息]向kafka发送分贝券发放结果消息,error:[{}],params:[{}]", e.getMessage(), messageJson, e);
        }
    }


    private void sendMessage(String topic, String data) {
        log.info("[分贝券结果推送消息]向kafka发送分贝券发放结果消息,topic:[{}],params:[{}]", topic,data);
        ListenableFuture<SendResult<String, String>> listenableFuture = lowKafkaTemplate.send(topic, data);
        //发送成功回调
        SuccessCallback<SendResult<String, String>> successCallback = result -> {
            //成功业务逻辑
            FinhubLogger.info(MessageFormat.format("【Kafka消息发放：Spring-Kafka MSG OK】topic:{},data:{},result:{}", topic, data, JSON.toJSONString(result)));
        };
        //发送失败回调
        FailureCallback failureCallback = ex -> {
            //失败业务逻辑
            String errorInfo = MessageFormat.format("【Kafka消息发放：Spring-Kafka MSG FAIL】topic:{},data:{}", topic, data);
            FinhubLogger.error(errorInfo, ex);
            throw new FinhubException(FinhubMessageCode.EXCEPTION, errorInfo);
        };
        listenableFuture.addCallback(successCallback, failureCallback);
    }
}
