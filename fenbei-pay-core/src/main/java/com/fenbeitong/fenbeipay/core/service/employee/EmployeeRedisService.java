package com.fenbeitong.fenbeipay.core.service.employee;


import com.fenbeitong.fenbeipay.core.dao.fenbeitong.redis.RedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

/**
 * Created by xiaoxiong on 2017/9/29.
 */
@Service
public class EmployeeRedisService {

    private final int timeout =8*60*60;
    @Autowired
    private RedisDao redisDao;

    @Autowired
    private RedisTemplate<Serializable, Serializable> redisTemplate;
    /**
     * 存入redis，设置超时时间
     * @param key
     * @param data
     */
    public void setEmployeeSave(String key,String data,int time){
        if(time==-1){
            redisDao.getValueOperations().set(key,data,timeout, TimeUnit.SECONDS);
        }else {
            redisDao.getValueOperations().set(key,data);
        }

    }
    /**
     * 获取存入的值
     * @param key
     * @return
     */
    public String getEmployeeData(String key ){
        return String.valueOf(redisDao.getValueOperations().get(key));
    }

    public RedisTemplate<Serializable, Serializable> getRedisTemplate(){
        return  redisTemplate;
    }
}
