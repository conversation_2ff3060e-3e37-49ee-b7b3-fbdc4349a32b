package com.fenbeitong.fenbeipay.core.service.file;

/**
 * 静态页面枚举
 */
public enum StaticPageType {
    /**
     *
     **/
    REDCOUPON_INSTRUCTIONS(0, "红包券使用说明", 1, "redcoupon_instructions.txt"),
    NEW_RED_COUPON_INSTRUCTIONS(1, "红包券使用说明", 1, "new_redcoupon_instructions.txt"),
    BUSINESS_RECHARGE_TRANS_CREDIT_INSTRUCTIONS(2, "商务账户充值转授信", 1, "business_recharge2credit.txt"),
    BUSINESS_CREDIT_TRANS_RECHARGE_INSTRUCTIONS(3, "商务账户授信转充值", 1, "business_credit2recharge.txt"),
    PERSON_RECHARGE_TRANS_CREDIT_INSTRUCTIONS(4, "个人账户充值转授信", 1, "person_recharge2credit.txt"),
    PERSON_CREDIT_TRANS_RECHARGE_INSTRUCTIONS(5, "个人账户授信转充值", 1, "person_credit2recharge.txt"),
    BLANK_INSTRUCTIONS(6, "空白", 1, "blank.txt"),
    ACCOUNT_EFFECTIVE_INSTRUCTIONS(7, "账户切换生效文案说明", 1, "account_effective.txt"),

    ;


    private int key;
    private String desc;
    private int type;
    private String page;

    StaticPageType(Integer key, String desc, int type, String page) {
        this.key = key;
        this.desc = desc;
        this.type = type;
        this.page = page;
    }


    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public int getType() {
        return type;
    }

    public String getPage() {
        return page;
    }
}
