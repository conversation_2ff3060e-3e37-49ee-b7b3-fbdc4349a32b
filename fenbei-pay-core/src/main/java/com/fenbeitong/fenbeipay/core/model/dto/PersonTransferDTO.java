package com.fenbeitong.fenbeipay.core.model.dto;

import com.fenbeitong.finhub.common.validation.constraints.BigDecimalNotPoint;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Description: java类作用描述
 * @ClassName: PersonTransferDTO
 * @Author: zhangga
 * @CreateDate: 2019/1/14 8:29 PM
 * @UpdateUser:
 * @UpdateDate: 2019/1/14 8:29 PM
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
@Data
public class PersonTransferDTO {
    private String id;
    /**
     * 目的账户ID
     */
    private String targetId;

    /**
     * 转赠金额（单位分）
     */
    @BigDecimalNotPoint
    private BigDecimal transferAmount;

    /**
     * 赠送人ID
     */
    private String fromEmployeeId;
    private String fromEmployeePhone;

    /**
     * 赠送人姓名
     */
    private String fromEmployeeName;

    /**
     * 赠送人公司ID
     */
    private String fromCompanyId;

    /**
     * 被赠送人ID
     */
    private String toEmployeeId;

    /**
     * @Description 被赠送人手机号
     **/
    @NotNull
    @Length(min = 11, max = 11)
    private String toEmployeePhone;

    /**
     * 被赠送人I姓名
     */
    private String toEmployeeName;

    /**
     * 被赠送人公司ID
     */
    private String toCompanyId;

    private String toEmployeeDept;
    private String toEmployeeDeptFull;
    private String toEmployeeDeptId;

}
