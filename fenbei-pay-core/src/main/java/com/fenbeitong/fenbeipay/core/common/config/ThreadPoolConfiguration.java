package com.fenbeitong.fenbeipay.core.common.config;

import cn.hutool.core.thread.RejectPolicy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 22:19:12
 */
@Slf4j
@Configuration
public class ThreadPoolConfiguration {

    @Bean
    public ThreadPoolExecutor batchThreadPoolExecutor() {
        return new ThreadPoolExecutor(10,
            100,
            60L,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(12000),
            RejectPolicy.CALLER_RUNS.getValue());
    }

}
