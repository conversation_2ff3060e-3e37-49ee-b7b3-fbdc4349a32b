package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;


@Data
@ToString(callSuper = true)
public class CashierRefundSettleVoucherResult extends BaseVo {

    BigDecimal voucherOrderInvoiceRefundAmount;
    BigDecimal voucherInvoiceRefundAmount;
    BigDecimal voucherRefundAmount;
    int voucherCount;
}
