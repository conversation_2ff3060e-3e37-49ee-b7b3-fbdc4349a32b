package com.fenbeitong.fenbeipay.core.service.auth;

import com.alibaba.fastjson.JSON;
import com.fenbeitong.fenbeipay.api.service.acctpublic.IAcctPublicSearchService;
import com.fenbeitong.fenbeipay.core.enums.auth.AuthObjMenuCodeEnum;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.service.dingding.DingDingMsgService;
import com.fenbeitong.fenbeipay.core.utils.FinPayException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthObjReqDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthObjResDTO;
import com.fenbeitong.usercenter.api.model.dto.privilege.AuthPermissionResDTO;
import com.fenbeitong.usercenter.api.service.privilege.IRPrivilegeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 权限处理
 * <AUTHOR>
 * @date 8/23/22 8:32 PM
 * @version 0.0.1
 */
@Slf4j
@Component
public class DataAuthService {
    @Autowired
    private IRPrivilegeService iRPrivilegeService;
    @Autowired
    private DingDingMsgService dingDingMsgService;
    @Autowired
    private IAcctPublicSearchService iAcctPublicSearchService;

    /**
     * 获取权限数据
     */
    public AuthObjResDTO getDataAuth(AuthObjMenuCodeEnum authObjMenuCodeEnum, String companyId, String userId) {
        FinhubLogger.info("获取数据权限信息, 入参, authObjMenuCodeEnum: {}, currentCompanyId: {}, currentUserId: {}", authObjMenuCodeEnum, companyId, userId);
        if (StringUtils.isBlank(companyId) || StringUtils.isBlank(userId)) {
            dingDingMsgService.sendMsg("获取数据权限信息, 没有获取到用户及公司id, 需关注. authObjMenuCodeEnum:  " + authObjMenuCodeEnum);
            throw new FinPayException(GlobalResponseCode.NOT_GET_LOGIN_INFO_ERROR);
        }
        AuthObjReqDTO dto = new AuthObjReqDTO();
        dto.setCompanyId(companyId);
        dto.setEmployeeId(userId);
        dto.setMenuCode(authObjMenuCodeEnum.getMenuCode());
        //数据权限目前不分端，menutype不用传
        // dto.setMenuType(MenuType.WebMenu.getKey());
        FinhubLogger.info("获取数据权限信息, 请求参数: {}", JSON.toJSONString(dto));

        List<AuthObjResDTO> authObjResDTOList;
        try {
            authObjResDTOList = iRPrivilegeService.queryAuthObjDTOByCompanyIdAndEmployeeId(dto);
            FinhubLogger.info("获取数据权限信息, 响应结果: {}", JSON.toJSONString(authObjResDTOList));
            if (CollectionUtils.isEmpty(authObjResDTOList)) {
                dingDingMsgService.sendMsg("获取数据权限信息, 没有查询到权限数据，需关注。 权限请求参数:  " + JSON.toJSONString(dto));
                return buildDefaultAuthObjResDTO(authObjMenuCodeEnum, userId);
            }
            AuthObjResDTO authObjResDTO = authObjResDTOList.get(0);
            // null给赋值默认值
            authObjResDTO.setEmployee(authObjResDTO.getEmployee() == null ? buildDefaultAuthPermissionResDTO() : authObjResDTO.getEmployee());
            authObjResDTO.setDepartment(authObjResDTO.getDepartment() == null ? buildDefaultAuthPermissionResDTO() : authObjResDTO.getDepartment());
            authObjResDTO.setArchive(authObjResDTO.getArchive() == null ? buildDefaultAuthPermissionResDTO() : authObjResDTO.getArchive());
            authObjResDTO.setCostCenter(authObjResDTO.getCostCenter() == null ? buildDefaultAuthPermissionResDTO() : authObjResDTO.getCostCenter());
            authObjResDTO.setAccount(authObjResDTO.getAccount() == null ? buildDefaultAuthPermissionResDTO() : authObjResDTO.getAccount());
            return authObjResDTO;
        } catch (Exception e) {
            FinhubLogger.error("获取数据权限信息, 获取权限数据异常, 组装默认权限对象, 请求参数: {}", JSON.toJSONString(dto), e);
            dingDingMsgService.sendMsg("获取数据权限信息, 获取权限数据异常，需关注。 权限请求参数:  " + JSON.toJSONString(dto));
            return buildDefaultAuthObjResDTO(authObjMenuCodeEnum, userId);
        }
    }

    /**
     * 组装默认权限对象
     */
    private AuthObjResDTO buildDefaultAuthObjResDTO(AuthObjMenuCodeEnum authObjMenuCodeEnum, String userId) {
        AuthObjResDTO authObjResDTO = AuthObjResDTO.builder()
                .authObjCode(authObjMenuCodeEnum.getMenuCode())
                .employee(buildDefaultAuthPermissionResDTO(userId))
                .account(buildDefaultAuthPermissionResDTO())
                .archive(buildDefaultAuthPermissionResDTO())
                .authObjCode(authObjMenuCodeEnum.getMenuCode())
                .costCenter(buildDefaultAuthPermissionResDTO())
                .department(buildDefaultAuthPermissionResDTO())
                .build();
        FinhubLogger.info("获取数据权限信息, 获取权限数据异常, 组装默认权限对象, 结果, authObjResDTO: {}", JSON.toJSONString(authObjResDTO));
        return authObjResDTO;
    }

    /**
     * 组装默认权限对象
     */
    private AuthPermissionResDTO buildDefaultAuthPermissionResDTO(String... dataIdList) {
        return AuthPermissionResDTO.builder().allData(false).partDataIdList(Lists.newArrayList(dataIdList)).build();
    }

    /**
     * 获取资金账户列表（兼容平台全部账户权限 allData=true）
     */
    public List<String> getCompanyAuthChangeAccountIdList(String companyId, AuthObjResDTO authObjResDTO) {
        if (!authObjResDTO.getAccount().getAllData()) {
            return authObjResDTO.getAccount().getPartDataIdList();
        }
        // 查询全部账户
        return iAcctPublicSearchService.queryAllAuthAccountByCompanyId(companyId);
    }
}
