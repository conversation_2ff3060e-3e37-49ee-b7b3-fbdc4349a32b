package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.activity.api.service.fbb.model.vo.BaseReqDTO;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.RefundTradeCommonRPCVo;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.ObjUtils;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ToString
@Accessors(chain = true)
public class CashierBatchRefundTradeReqVo extends BaseReqDTO {
    /**
      * @Description: 公司id
      */
    @NotBlank
    private String companyId;
    /**
     * @Description: 订单类型
     */
    @NotNull
    private Integer orderType;

    /**
     * 批量退款总的退款单
     */
    private String batchRefundOrderId;

    /**
     * 批量总退款金额
     */
    @NotNull
    @Min(0)
    private BigDecimal totalRefundAmount = BigDecimal.ZERO;

    @NotNull
    private List<RefundCommonVo> refundList= Lists.newArrayList();

    /**
      * @Description: refundList对象负值
      * @Param: [refundList]
      * @return: void
      * @Author: wh
      * @Date: 2019/5/14 5:57 PM
      */
    public void addRefundList(List<RefundTradeCommonRPCVo> refundList) {
        if (ObjUtils.isBlank(refundList)){
            throw new FinhubException(GlobalResponseCode.FBB_PAY_OPERATE_ERROR.getCode(), GlobalResponseCode.FBB_PAY_OPERATE_ERROR.getType(), GlobalResponseCode.FBB_PAY_OPERATE_ERROR.getMsg());
        }
        List<RefundCommonVo> RefundList = refundList.stream().map(
                refundTradeCommonRPCVo -> {
                    RefundCommonVo refundCommonVo = new RefundCommonVo();
                    BeanUtils.copyProperties(refundTradeCommonRPCVo, refundCommonVo);
                    return refundCommonVo;
                }
        ).collect(Collectors.toList());
        this.refundList=RefundList;
    }
}
