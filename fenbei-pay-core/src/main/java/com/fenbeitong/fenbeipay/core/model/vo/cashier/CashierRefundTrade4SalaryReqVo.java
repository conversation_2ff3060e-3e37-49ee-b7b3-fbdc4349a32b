package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CashierRefundTrade4SalaryReqVo implements Serializable {
    /**
     * 退款总额
     */
    private BigDecimal refundAmountAll;
    /**
     * 发薪侧付款失败金额
     */
    private BigDecimal refundAmountBank;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 退款ID
     */
    private String refundOrderId;
}
