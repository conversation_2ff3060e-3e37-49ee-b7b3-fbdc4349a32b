package com.fenbeitong.fenbeipay.core.common.base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ExportElectronicTaskDTO implements Serializable {
    private static final long serialVersionUID = 4711575022523870921L;
    private String taskSrc;//必填 创建任务的系统名称
    private String taskName;//必填 导出任务名称
    private Integer taskCategory;//非必填  任务分类ID，可根据需求自行维护任务分类，用于任务查询的筛选操作
    private String taskCategoryName;//非必填  任务分类名称
    private Integer totalSize;//数据总量用于进度显示
    private String fileName;
    private String createId;//必填 创建者ID
    private String createName;//创建者名称
}
