package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: liyi
 * @Date: 2022/3/30 下午4:08
 */
@Data
@ToString(callSuper = true)
public class CashierChangePriceTradeRespVo extends BaseVo {
    private static final long serialVersionUID = 4373979675505447864L;
    /**
     * 场景订单ID
     */
    private String fbOrderId;

    /**
     * 交易流水号
     */
    private String fbTradeId;

    /**
     * 请求单号
     */
    private String fbRequestNo;

    /**
     * 改价结果  成功:success  失败:fail
     */
    private String changePriceResult;

    /**
     * 改价失败原因
     */
    private String changePriceErrMsg;
}
