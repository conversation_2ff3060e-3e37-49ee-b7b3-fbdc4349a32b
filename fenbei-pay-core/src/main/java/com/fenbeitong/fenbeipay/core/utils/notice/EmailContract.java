package com.fenbeitong.fenbeipay.core.utils.notice;

import java.util.Map;
import java.util.Set;

/**
 * Created by zhuminghua on 2017/6/17.
 */
public class EmailContract {
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 邮箱服务器id
     */
    private String serverId;
    /**
     * 收件人
     */
    private Set<String> toList;
    /**
     * 抄送人
     */
    private Set<String> ccList;
    /**
     * 密送人
     */
    private Set<String> bccList;
    /**
     * 标题
     */
    private String subject = "";
    /**
     * 普通邮件
     */
    private String text = "";
    /**
     * 模板 id
     */
    private String templateId;

    /**
     * 模板数据
     */
    private Map<String, Object> data;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public Set<String> getToList() {
        return toList;
    }

    public void setToList(Set<String> toList) {
        this.toList = toList;
    }

    public Set<String> getCcList() {
        return ccList;
    }

    public void setCcList(Set<String> ccList) {
        this.ccList = ccList;
    }

    public Set<String> getBccList() {
        return bccList;
    }

    public void setBccList(Set<String> bccList) {
        this.bccList = bccList;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

}
