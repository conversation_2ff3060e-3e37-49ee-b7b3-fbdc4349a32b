package com.fenbeitong.fenbeipay.core.service.personpay;

import com.fenbeitong.fenbeipay.core.constant.personpay.PayConstant;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecordExample;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonRefundRecordExample;
import com.fenbeitong.fenbeipay.core.model.vo.personpay.ReFundReqContract;
import com.fenbeitong.fenbeipay.core.service.redis.RedisService;
import com.luastar.swift.base.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by zhanglei on 2018/10/23.
 */
@Service
public class PersonPayService extends FuQianLaService {

    @Autowired
    private RedisService redisService;

    public Map<String,Object > validateMsg(ReFundReqContract reFundReqContract){
        Map<String,Object> retMap=new HashMap<>();
        List<Integer> values=new ArrayList<>();
        values.add(2);
        values.add(4);
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andFbOrderIdEqualTo(reFundReqContract.getOrderNo()).andStatusIn(values);
        String retMsg="成功";
        Integer retCode= PayConstant.payOrderRefundIng;
        //查询支付订单
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        if (CollectionUtils.isEmpty(personPayRecordList)) {
            logger.error("没有找到退款的订单信息，或订单正在退款中");
            retMsg="没有找到退款的订单信息，或订单正在退款中";
            retCode=PayConstant.payOrderRefundError;
        }else{
            //查找退款记录并插入新记录
            PersonRefundRecordExample personRefundRecordExample=new PersonRefundRecordExample();
            personRefundRecordExample.createCriteria().andOrderIdEqualTo(reFundReqContract.getOrderNo()).andRefundStatusNotEqualTo(9);
            List<PersonRefundRecord> personRefundRecords=refundRecordMapper.selectByExample(personRefundRecordExample);
            Map<String,Object> map=new ConcurrentHashMap<>();
            String totalAmountKey=PayConstant.PRFUND_TOTAL+reFundReqContract.getOrderNo();
            String refundSuccessKey=PayConstant.PRFUND_SUCCESS+reFundReqContract.getOrderNo();
            String refundIngKey=PayConstant.PRFUND_ING+reFundReqContract.getOrderNo();
            long amount = personPayRecordList.get(0).getAmount();
            long refundSuccessAmounts = 0;
            long refundIngAmounts = 0;
            if(!CollectionUtils.isEmpty(personRefundRecords)){
                for (PersonRefundRecord refundRecord : personRefundRecords) {
                    if(refundRecord.getRefundStatus()==8){
                        refundSuccessAmounts+=refundRecord.getRefundAmount();
                    }else if(refundRecord.getRefundStatus()==7){
                        refundIngAmounts+=refundRecord.getRefundAmount();
                    }
                }
                if (amount >= (refundSuccessAmounts+refundIngAmounts + reFundReqContract.getOrderAmount().longValue())) {
                    map.put(totalAmountKey,amount);
                    map.put(refundSuccessKey,refundSuccessAmounts);
                    map.put(refundIngKey,refundIngAmounts);
                    redisService.saveToRedis(reFundReqContract.getOrderNo(),map,60*24);
                } else {
                    retMsg = "退款金额超过支付总金额";
                    retCode = PayConstant.payOrderRefundError;
                }
                //首次也保存
            }else{
                map.put(totalAmountKey,amount);
                map.put(refundSuccessKey,refundSuccessAmounts);
                map.put(refundIngKey,refundIngAmounts);
                redisService.saveToRedis(reFundReqContract.getOrderNo(),map,60*24);
            }
        }

        //不支持阿里的退款
        if(reFundReqContract.getChannel().equals(PayConstant.ali_pay_app)){
            retMsg="不支持阿里退款";
            retCode=PayConstant.payOrderRefundError;
        }
        retMap.put(PayConstant.PRFUND_ORDERID,reFundReqContract.getOrderNo());
        retMap.put(PayConstant.PRFUND_ORDERAMOUNT,reFundReqContract.getOrderAmount().divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP));
        retMap.put(PayConstant.PRFUND_STATUS,retCode);
        retMap.put(PayConstant.PRFUND_RETMSG,retMsg);
        return retMap;
    }
    /**
     * 个人支付成功/部分支付集合
     * 1-待支付、2-支付成功、3-支付失败、4-部分支付
     *
     * @param fbOrderId
     * @return
     */
    public  PersonPayRecord getPayRecordList(String fbOrderId) {
        List<Integer> values=new ArrayList<>();
        values.add(2);
        values.add(4);
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andFbOrderIdEqualTo(fbOrderId).andStatusIn(values);
        return personPayRecordMapper.selectByExample(personPayRecordExample).get(0);
    }
    public Map<String,Object> personRefundRecordMap(String fbOrderNo){
        PersonRefundRecordExample personRefundRecordExample=new PersonRefundRecordExample();
        personRefundRecordExample.createCriteria().andOrderIdEqualTo(fbOrderNo);
        List<PersonRefundRecord> personRefundRecords=refundRecordMapper.selectByExample(personRefundRecordExample);
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andFbOrderIdEqualTo(fbOrderNo);
        //查询支付订单
        List<PersonPayRecord> personPayRecordList = personPayRecordMapper.selectByExample(personPayRecordExample);
        Long refundSuccessAmounts = 0L;
        Long refundIngAmounts = 0L;
        Long amount =personPayRecordList.get(0).getAmount();
        String successKey=PayConstant.PRFUND_SUCCESS+fbOrderNo;
        String ingKey=PayConstant.PRFUND_ING+fbOrderNo;
        String totalKey=PayConstant.PRFUND_TOTAL+fbOrderNo;
        Map<String,Object> map=new ConcurrentHashMap<>();
        if(!CollectionUtils.isEmpty(personRefundRecords)){
            for (PersonRefundRecord refundRecord : personRefundRecords) {
                if(refundRecord.getRefundStatus()==8){
                    refundSuccessAmounts+=refundRecord.getRefundAmount();
                }else if(refundRecord.getRefundStatus()==7){
                    refundIngAmounts+=refundRecord.getRefundAmount();
                }
            }
        map.put(successKey,refundSuccessAmounts);
        map.put(ingKey,refundIngAmounts);
        map.put(totalKey,amount);
        }
        return map;
    }
    /**
     * 修改退款状态
     *
     * @param orderId
     * @param status
     * @return
     */
    public int updateRefunStatus(String orderId, Integer status) {
        PersonPayRecordExample personPayRecordExample = new PersonPayRecordExample();
        personPayRecordExample.createCriteria().andOrderIdEqualTo(orderId);
        PersonPayRecord payRecord = new PersonPayRecord();
        payRecord.setRefundOrderStatus(status);
        payRecord.setUpdateTime(new Date());
        return personPayRecordMapper.updateByExampleSelective(payRecord, personPayRecordExample);
    }

}
