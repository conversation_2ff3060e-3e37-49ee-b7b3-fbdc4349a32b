package com.fenbeitong.fenbeipay.core.utils;

import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.google.common.collect.Lists;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.PropertyValue;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.beans.factory.support.ManagedMap;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.util.StringValueResolver;

import java.beans.PropertyDescriptor;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 功能描述：
 * 在spring实例化RedisMessageListenerContainer类之后，初始化setMessageListeners(Map)方法之前，动态修改setMessageListeners(Map)的入参。
 * 达到让spring-redis配置文件<redis:listener>节点支持topic=“${xx.xx}”配置的目地
 * <p>
 * 背景：项目中使用<redis:listener ref="grantTaskConsumer" method="test" topic="${redis.topic}"/>时，${redis.topic}无法正确替换为properties中配置的值
 * <p>
 * 目标：让topic的值可以通${redis.topic}从properties中读取
 * <p>
 * 步骤：
 * 1、查看<redis:listener> 的schema信息， @see xmlns:redis="http://www.springframework.org/schema/redis"。
 * 发现listener是通过自己义schema加载，继续查看schema的解析类RedisListenerContainerParser类。
 *

 * @see org.springframework.data.redis.config.RedisListenerContainerParser#doParse(org.w3c.dom.Element, org.springframework.beans.factory.xml.ParserContext, org.springframework.beans.factory.support.BeanDefinitionBuilder)
 * 发现因为在doParse()方法中直接通过解析xml来取得topic的值，所以在配置文件中写什么，topic值就是什么；
 * <p>
 * 2、思路一、此时最简单的改动就是下载spring-data-redis-1.8.16.RELEASE.jar源码，修改其doParse()方法，在取到topic值之后替换为properties中配置的值
 * 但因为此方法涉及到修改开源框架的源代码，对以后系统架构的整体升级，及其它项目包依赖产生的影响较大，不好评估具体影响范围，遂放弃；
 * <p>
 * 3、思路二、为不同环境增加resources目录，如resources.dev、resources.prod，目录中放各环境的spring-redis.xml，同时修改pom.xml打包时根据不同的环境覆盖为对应的spring-redis.xml。因为要增加环境配置文件，再修改pom.xml,感觉麻烦，做为planB吧，再看看有没有别的办法；
 * <p>
 * 4、思路三、既然无法修改源码，又不想增加配置文件，那么能不能想办法在sping创建lister的过程中来动态替换为实际值呢？
 * 继续查看RedisListenerContainerParser类发现的getBeanClass()方法
 * @see org.springframework.data.redis.config.RedisListenerContainerParser#getBeanClass(org.w3c.dom.Element)
 * 实际是创建了RedisMessageListenerContainer类，并调用
 * @see org.springframework.data.redis.listener.RedisMessageListenerContainer#setMessageListeners(java.util.Map)
 * 方法传入topic来注册监听器的
 * <p>
 * 那么我们只需要在该方法被调用前修改topic值就可以；
 * 接下来查看spring创建bean的整个过程，发现Spring内部提供了一个BeanPostProcessor接口，这个接口的作用在于对于新构造的实例可以做一些自定义的修改。
 * 比如如何构造、属性值的修改、构造器的选择等等。只要我们实现了这个接口，便可以对构造的bean进行自定义的修改。
 * BeanPostProcessor接口还有一些子接口的定义：InstantiationAwareBeanPostProcessor接口继承自BeanPostProcessor接口，他有一个postProcessPropertyValues的方法。
 * @see ReplaceRedisTopicProcessor#postProcessPropertyValues(org.springframework.beans.PropertyValues, java.beans.PropertyDescriptor[], java.lang.Object, java.lang.String)
 * 这个方法的作用是在调用bean的setter方法之前调用该方法，可以修改setter方法的入参。
 * 这样的话就满足了我们在lister创建过程中动态修改topic的值了；
 *
 * <AUTHOR>
 * @since 2019-01-14 AM11:59
 */

@Service
@Lazy(false)
public class ReplaceRedisTopicProcessor implements InstantiationAwareBeanPostProcessor, EmbeddedValueResolverAware {


    private StringValueResolver resolver;

    /**
     * 如果bean实现了接口 EmbeddedValueResolverAware,会调用方法setEmbeddedValueResolver() 用于获取properties值
     *
     * @param stringValueResolver
     */
    @Override
    public void setEmbeddedValueResolver(StringValueResolver stringValueResolver) {
        resolver = stringValueResolver;
    }

    /**
     * 实例化Bean之前调用 （Bean构造函数前）
     * postProcessBeforeInstantiation方法的作用在目标对象被实例化之前调用的方法，可以返回目标实例的一个代理用来代替目标实例
     *
     * @param beanClass 目标对象的类型
     * @param beanName  目标实例在Spring容器中的name
     * @return 如果返回的是非null对象，接下来除了postProcessAfterInitialization方法会被执行以外，其它bean构造的那些方法都不再执行。否则那些过程以及postProcessAfterInitialization方法都会执行
     * @throws BeansException
     */
    @Override
    public Object postProcessBeforeInstantiation(Class<?> beanClass, String beanName) throws BeansException {
        return null;
    }

    /**
     * 实例化Bean之后调用
     * postProcessAfterInstantiation方法的作用在目标对象被实例化之后并且在属性值被populate之前调用
     *
     * @param bean     目标实例(这个时候目标对象已经被实例化但是该实例的属性还没有被设置)
     * @param beanName 是目标实例在Spring容器中的name
     * @return 如果返回true，目标实例内部的返回值会被populate，否则populate这个过程会被忽视
     * @throws BeansException
     */
    @Override
    public boolean postProcessAfterInstantiation(Object bean, String beanName) throws BeansException {
        return true;
    }

    /**
     * 在属性中被设置到目标实例之前调用，可以修改属性的设置
     *
     * @param pvs      表示参数属性值(从BeanDefinition中获取)
     * @param pds      参数的描述信息(比如参数名，类型等描述信息)
     * @param bean     目标实例
     * @param beanName 目标实例在Spring容器中的name
     * @return 返回值是PropertyValues，可以使用一个全新的PropertyValues代替原先的PropertyValues用来覆盖属性设置或者直接在参数pvs上修改。如果返回值是null，那么会忽略属性设置这个过程(所有属性不论使用什么注解，最后都是null)
     * @throws BeansException
     */
    @Override
    @SuppressWarnings("unchecked")
    public PropertyValues postProcessPropertyValues(PropertyValues pvs, PropertyDescriptor[] pds, Object bean, String beanName) throws BeansException {
        try {
            //spring实例化RedisMessageListenerContainer的setter方法
            if (bean instanceof RedisMessageListenerContainer) {
                //取出setMessageListeners()方法的入参
                PropertyValue propertyValue = pvs.getPropertyValue("messageListeners");
                ManagedMap<GenericBeanDefinition, List<Topic>> stringValue = (ManagedMap) propertyValue.getValue();
                for (Map.Entry<GenericBeanDefinition, List<Topic>> entry : stringValue.entrySet()) {
                    //得到原来的topic列表
                    List<Topic> oldList = entry.getValue();
                    List<Topic> reduce = oldList.stream().map(t -> {
                        //替换${redis.topic}的值为properties中配置的值
                        String newValue = resolver.resolveStringValue(t.getTopic());
                        //按RedisListenerContainerParser的逻辑解析新的topic
                        String[] array = StringUtils.delimitedListToStringArray(newValue, " ");
                        return Stream.of(array).map(m -> m.contains("*") ? new PatternTopic(m) : new ChannelTopic(m)).collect(Collectors.toList());
                    }).reduce(Lists.newArrayList(), ListUtils::sum);
                    //修改pvs的值为替换后的新值
                    entry.setValue(reduce);
                }
            }
        } catch (Throwable e) {
            FinhubLogger.warn("尝试替换<redis:listener>的topic出错,放弃替换.<redis:listener>配置无法使用占位符${}，请注意检查您的配置！");
        }

        return pvs;
    }

    /**
     * postProcessBeforeInitialization 方法会在Bean构造完成后（构造方法执行完成），初始化方法（init-method）方法调用之前被调用。
     *
     * @param bean     刚刚由Spring实例化出来的Bean
     * @param beanName 在Spring配置元数据中Bean的名称（id or name）
     * @return 该返回值会被Spring容器作为处理后的Bean注册到容器中。如果你在postProcessBeforeInitialization方法中重新构造了一个Bean进行返回，而不是返回参数中的bean；那么你返回的Bean将会被注册到Spring容器中。而原来在Spring中配置的Bean（被Spring实例化的Bean）将会被覆盖。(覆盖后新的bean中init-method继续被调用)
     * @throws BeansException
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    /**
     * postProcessAfterInitialization 方法会在Bean的初始化方法（init-method）被容器调用之后执行。
     *
     * @param bean     刚刚由Spring容器调用过初始化方法（init-method）的Bean
     * @param beanName 在Spring配置元数据中Bean的名称（id or name）
     * @return 该方法的返回值会被Spring容器作为处理后的Bean注册到容器中。如果你在postProcessAfterInitialization 方法中重新构造了一个Bean进行返回，而不是返回参数中的bean；那么你返回的Bean将会被注册到Spring容器中。而原来在Spring中配置的Bean（被Spring实例化的Bean）将会被覆盖。
     * @throws BeansException
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }
}
