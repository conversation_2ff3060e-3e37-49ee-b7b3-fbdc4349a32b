package com.fenbeitong.fenbeipay.core.constant.paycenter;

/**
 * 支付相关常量
 */
public interface PayCenterConstant {

    //支付账户锁定提示信息
    String PAY_LOCK_REASON = "正在操作账户信息，请稍后再试";

    public static final String  PAY_EMAIL_SUBJECT_PREFIX = "项目【fenbei-pay】";

    public static final String  PAY_EMAIL_SUBJECT_REFUND_ERROR_PREFIX = PAY_EMAIL_SUBJECT_PREFIX+"【退款失败】";

    public static final String  PAY_EMAIL_SUCCESS_AND_SUCCESS_PREFIX = PAY_EMAIL_SUBJECT_PREFIX+"【订单已取消，用户支付成功！】";

    public static final String  PAY_BACKCALL_MSG= "支付回调通知";

    public static final String  PAY_QUERY_MSG= "支付查询信息";

    public static final String  CASHIER_CANCEL_DEFAULT= "系统默认取消！";

    public static final String  CASHIER_CANCEL_SYSTEM_TIME_DONE= "支付交易到期，自动取消交易！";

    public static final String  CASHIER_CRONTAB_HEAD= "【支付系统-时间程序】";

    public static final String USER_ID = "userId";
    public static final String USER_NAME = "userName";
    public static final String USER_PHONE = "userPhone";
    public static final String COMPANY_ID = "companyId";
    public static final String BANK_ACCOUNT_NO = "bankAccountNo";

    public static final int FBQ_PAY_EXIST_AUTH = 1;
    public static final int PWD_PAY_NO_EXIST_AUTH = 0;
    public static final int PWD_PAY_EXIST_AUTH = 1;

    public static final String QUERY_COMPANYEMPLOYEEAUTH_VALUE = "fbt_coupon";


}
