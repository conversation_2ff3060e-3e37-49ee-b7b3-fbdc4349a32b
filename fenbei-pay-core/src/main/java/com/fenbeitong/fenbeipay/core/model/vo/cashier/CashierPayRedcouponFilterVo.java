package com.fenbeitong.fenbeipay.core.model.vo.cashier;


import com.fenbeitong.fenbeipay.api.constant.enums.account.OperationChannelType;
import com.fenbeitong.fenbeipay.core.model.vo.BaseVo;
import com.fenbeitong.finhub.common.constant.PayModelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

import static com.fenbeitong.finhub.common.constant.CategoryTypeEnum.*;

/**
 * 红包券可用场景过滤器
 *
 * <AUTHOR>
 * @date 2019年12月11日15:59:31
 */
@Data
@NoArgsConstructor
public class CashierPayRedcouponFilterVo extends BaseVo {


    /**
     * 不能使用红包券的场景
     */
    private static Set<Integer> categoryTypesNO = new  HashSet<Integer>();

    /**
     * 不能使用红包券的渠道
     */
    private static Set<Integer> operationChannelTypesNO = new  HashSet<Integer>();

    /**
     * 不能使用红包券的支付方式
     */
    private static Set<Integer> orderPayModelTypesNO = new  HashSet<Integer>();


    static{
        categoryTypesNO.add(Train.getCode());
        categoryTypesNO.add(BANK_INDIVIDUAL.getCode());
        categoryTypesNO.add(ECard.getCode());
        categoryTypesNO.add(BANK_COURTESY.getCode());
        categoryTypesNO.add(Ultraman.getCode());
//        categoryTypesNO.add(Insurance.getCode());
    }

    /**
     * stereo操作不支持
     */
    static{
        operationChannelTypesNO.add(OperationChannelType.STEREO.getKey());
    }

    /**
     * 个人垫付不支持
     */
    static{
        orderPayModelTypesNO.add(PayModelEnum.PERSONAL_PREPAY.getCode());
    }


    /**
     * 该场景是否可以使用红包券
     * 该操作渠道是否可以使用红包券
     * true可以
     * false不可以
     * @param categoryType
     * @param operationChannel
     * @return
     */
    public static boolean checkRedcouponUse(Integer categoryType,Integer operationChannel){
        return checkRedcouponUseByCategoryType(categoryType)&&checkRedcouponUseByOperationChannel(operationChannel);
    }


    /**
     * 该场景是否可以使用红包券
     * 该操作渠道是否可以使用红包券
     * true可以
     * false不可以
     * @param categoryType
     * @param operationChannel
     * @return
     */
    public static boolean checkRedcouponUse(Integer categoryType,Integer operationChannel,Integer orderPayModel){
        return checkRedcouponUseByCategoryType(categoryType)
                &&checkRedcouponUseByOrderPayModel(orderPayModel);
    }




    /**
     * 该场景是否可以使用红包券
     * true可以
     * false不可以
     * @param categoryType
     * @return
     */
    public static boolean checkRedcouponUseByCategoryType(Integer categoryType){
        if(categoryType == null ){
            //为空默认可以用红包券
            return true;
        }
        return !categoryTypesNO.contains(categoryType);
    }

    /**
     * 该操作渠道是否可以使用红包券
     * true可以
     * false不可以
     * @param operationChannel
     * @return
     */
    public static boolean checkRedcouponUseByOperationChannel(Integer operationChannel){
        if(operationChannel == null ){
            //为空默认可以用红包券
            return true;
        }
        return !operationChannelTypesNO.contains(operationChannel);

    }

    /**
     * 该支付类型是否可以使用红包券
     * true可以
     * false不可以
     * @param orderPayModel
     * @return
     */
    public static boolean checkRedcouponUseByOrderPayModel(Integer orderPayModel){
        if(orderPayModel == null ){
            //为空默认可以用红包券
            return true;
        }
        return !orderPayModelTypesNO.contains(orderPayModel);

    }





}
