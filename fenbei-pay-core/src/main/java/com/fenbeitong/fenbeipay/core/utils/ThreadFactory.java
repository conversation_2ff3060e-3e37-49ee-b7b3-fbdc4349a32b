package com.fenbeitong.fenbeipay.core.utils;


import com.luastar.swift.base.utils.SpringUtils;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池工厂
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/15
 */
public class ThreadFactory {
    private ThreadFactory() {
    }


    /**
     * 获取批量执行线程
     * @return
     */
    public static ThreadPoolExecutor getBatchThreadPoolExecutor() {
        return SpringUtils.getBean(ThreadPoolExecutor.class,"batchThreadPoolExecutor");
    }

}
