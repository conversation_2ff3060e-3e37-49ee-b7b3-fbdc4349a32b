package com.fenbeitong.fenbeipay.core.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Create by l<PERSON><PERSON> on 2020/6/9 20:33
 */
@Getter
@Setter
public abstract class BaseQuery implements Serializable {
    //当前页   默认第一页
    private Integer pageIndex = 1;
    //条数 默认20
    private Integer pageSize = 20;

    private Integer pageNo;

    public Integer getPageNo() {
        return (pageIndex - 1) * pageSize;
    }
}
