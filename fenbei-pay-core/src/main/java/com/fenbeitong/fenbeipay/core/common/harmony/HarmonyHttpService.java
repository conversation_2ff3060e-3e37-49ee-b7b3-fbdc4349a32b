package com.fenbeitong.fenbeipay.core.common.harmony;

import com.fenbeitong.fenbeipay.core.utils.notice.SmsContract;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * <AUTHOR>
 * @date：2019-01-05
 */
public interface HarmonyHttpService {


    /**
     * 发送短信消息
     * @param msgBody
     * @return
     */
    @POST("/v1/sms")
    Call<String> sendSms(@Body SmsContract msgBody );
}
