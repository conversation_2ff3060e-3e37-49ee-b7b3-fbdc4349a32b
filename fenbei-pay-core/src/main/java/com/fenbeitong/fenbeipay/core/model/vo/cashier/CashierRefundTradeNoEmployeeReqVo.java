package com.fenbeitong.fenbeipay.core.model.vo.cashier;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ToString
@Accessors(chain = true)
public class CashierRefundTradeNoEmployeeReqVo extends CashierBasePersonVo {

    @NotBlank
    private String fbOrderId;

    /**
     * 售后场景逆向订单号
     */
    private String refundOrderId;

    /**
     * 退款金额
     */
    @NotNull
    @Min(0)
    private BigDecimal totalRefundAmount = BigDecimal.ZERO;
    @NotNull
    @Min(0)
    private BigDecimal publicRefundAmount = BigDecimal.ZERO;

    //===================个人支付==============
    /**
     * 通知场景方，退款已经完成
     */
    private String bizCallbackUrl;

    /**
     * 退款原因
     */
    private String refundReason = "其他原因";

    /**
     * 因公退款金额是否超过了canMaxRefund
     * @param canMaxRefund
     * @return
     */
    public boolean overPublicRefund(BigDecimal canMaxRefund){
        return publicRefundAmount.compareTo(canMaxRefund) > 0;
    }
    /**
     * 退款总额金额是否超过了canMaxRefund
     * @param canMaxRefund
     * @return
     */
    public boolean overTotalRefund(BigDecimal canMaxRefund){
        return totalRefundAmount.compareTo(canMaxRefund) > 0;
    }
}