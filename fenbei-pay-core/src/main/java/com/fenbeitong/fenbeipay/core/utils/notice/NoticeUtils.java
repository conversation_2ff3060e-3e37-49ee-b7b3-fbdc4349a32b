package com.fenbeitong.fenbeipay.core.utils.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fenbeitong.fenbeipay.core.utils.HttpclientUtils;
import com.fenbeitong.finhub.common.utils.CheckUtils;
import com.luastar.swift.base.config.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 短信，邮件，push等通知
 */
public class NoticeUtils {

    private static Logger LOGGER = LoggerFactory.getLogger(NoticeUtils.class);
    private static final String HARMONY_URL = PropertyUtils.getString("host.harmony", "");

    /**
     * 发送语音短信
     *
     * @param voiceContract
     */
    public static void sendVoice(VoiceContract voiceContract) {
        CheckUtils.checkNull(voiceContract, "发送语音短信参数不能为空!");
        CheckUtils.create()
                .addCheckEmpty(voiceContract.getMobile(), "手机号不能为空！")
                .addCheckEmpty(voiceContract.getMsg(), "验证码不能为空！")
                .check();
        try {
            String voiceUrl = HARMONY_URL + "/v1/voice/sms";
            JSONObject bodyJSON = new JSONObject();
            bodyJSON.put("tel",
                    new JSONObject()
                            .fluentPut("nationcode", voiceContract.getNationcode())
                            .fluentPut("mobile", voiceContract.getMobile()));
            bodyJSON.put("msg", voiceContract.getMsg());
            bodyJSON.put("ext", voiceContract.getExt());
            String bodyString = bodyJSON.toJSONString();
            LOGGER.info("发送语音短信内容：{}", bodyString);
            String result = HttpclientUtils.postJSONResult(voiceUrl, bodyString);
            LOGGER.info("发送语音短信结果：{}", result);
        } catch (Exception e) {
            LOGGER.error("发送语音短信异常：" + e.getMessage(), e);
        }
    }

    /**
     * 发送邮件
     *
     * @param emailContract
     */
    public static void sendEmail(EmailContract emailContract) {
        CheckUtils.checkNull(emailContract, "发送邮件参数不能为空!");
        CheckUtils.checkEmpty(emailContract.getToList(), "收件箱不能为空！");
        try {
            String mailUrl = HARMONY_URL + "/harmony/mail";
            Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("customerId", Optional.ofNullable(emailContract.getCustomerId()).orElse("saas"));
            bodyMap.put("serverId", emailContract.getServerId());
            bodyMap.put("toList", emailContract.getToList());
            bodyMap.put("ccList", emailContract.getCcList());
            bodyMap.put("bccList", emailContract.getBccList());
            bodyMap.put("subject", emailContract.getSubject());
            bodyMap.put("text", emailContract.getText());
            if (StringUtils.isNotEmpty(emailContract.getTemplateId())) {
                Map<String, Object> htmlMap = new LinkedHashMap<>();
                htmlMap.put("templateId", emailContract.getTemplateId());
                htmlMap.put("data", emailContract.getData());
                bodyMap.put("html", htmlMap);
            }
            String emailBody = JSON.toJSONString(bodyMap);
            LOGGER.info("发送邮件内容：{}", emailBody);
            String result = HttpclientUtils.postJSONResult(mailUrl, emailBody);
            LOGGER.info("发送邮件结果：{}", result);
        } catch (Exception e) {
            LOGGER.error("发送邮件异常：" + e.getMessage(), e);
        }
    }

}
