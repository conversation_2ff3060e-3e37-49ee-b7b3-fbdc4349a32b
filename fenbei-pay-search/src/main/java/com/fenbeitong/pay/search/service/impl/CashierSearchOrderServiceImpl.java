package com.fenbeitong.pay.search.service.impl;

import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessCreditFlowService;
import com.fenbeitong.fenbeipay.acctdech.service.AcctBusinessDebitFlowService;
import com.fenbeitong.fenbeipay.api.base.ResponsePage;
import com.fenbeitong.fenbeipay.api.constant.enums.bank.BankApplyType;
import com.fenbeitong.fenbeipay.api.constant.enums.cashier.*;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponOperationType;
import com.fenbeitong.fenbeipay.api.constant.enums.redcoupon.RedcouponType;
import com.fenbeitong.fenbeipay.api.constant.enums.vouchers.VoucherFlowType;
import com.fenbeitong.fenbeipay.api.model.dto.SaturnCashierOrRefundRespRPCDTO;
import com.fenbeitong.fenbeipay.api.model.dto.cashier.*;
import com.fenbeitong.fenbeipay.api.model.vo.cashier.*;
import com.fenbeitong.fenbeipay.awplus.service.personpayservice.PersonPayRecordService;
import com.fenbeitong.fenbeipay.cashier.manager.CashierOrderSettlementRelationManager;
import com.fenbeitong.fenbeipay.cashier.settlement.impl.CashierOrderSettlementAbstractService;
import com.fenbeitong.fenbeipay.core.constant.msg.CommonConstant;
import com.fenbeitong.fenbeipay.core.enums.common.GlobalResponseCode;
import com.fenbeitong.fenbeipay.core.enums.paycenter.PersonPayBusinessType;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderCostAttribution;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderRefundSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlement;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.cashier.CashierOrderSettlementRelation;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.thirdpay.PersonPayRecord;
import com.fenbeitong.fenbeipay.core.model.po.fenbeitong.vouchers.VouchersOperationFlow;
import com.fenbeitong.fenbeipay.core.utils.BigDecimalUtils;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessCreditFlow;
import com.fenbeitong.fenbeipay.dto.acctdech.AcctBusinessDebitFlow;
import com.fenbeitong.fenbeipay.dto.bank.BankCardApplyFlow;
import com.fenbeitong.fenbeipay.vouchers.service.VouchersOperationFlowService;
import com.fenbeitong.finhub.common.constant.*;
import com.fenbeitong.finhub.common.exception.FinhubException;
import com.fenbeitong.finhub.common.utils.FinhubLogger;
import com.fenbeitong.pay.search.service.AccountRedcouponSearchService;
import com.fenbeitong.pay.search.service.CashierSearchOrderService;

import com.google.common.collect.Lists;
import com.luastar.swift.base.utils.CollectionUtils;
import com.luastar.swift.base.utils.ObjUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CashierSearchOrderServiceImpl extends CashierOrderSettlementAbstractService implements CashierSearchOrderService {
    @Autowired
    private PersonPayRecordService personPayRecordService;
    @Autowired
    private AcctBusinessDebitFlowService acctBusinessDebitFlowService;
    @Autowired
    private AcctBusinessCreditFlowService acctBusinessCreditFlowService;
    @Autowired
    private AccountRedcouponSearchService accountRedcouponSearchService;
    @Autowired
    private VouchersOperationFlowService vouchersOperationFlowService;

    @Autowired
    private CashierOrderSettlementRelationManager cashierOrderSettlementRelationManager;


    @Override
    public ResponsePage<CashierSearchMultipleTradeRPCDTO> searchMultipleBatchByPage(CashierSearchBatchPageReqRPCVo queryReqRPCVo) {
        return cashierOrderSettlementManager.searchMultipleBatchByPage(queryReqRPCVo);
    }
    
    @Override
    public List<CashierSearchMultipleTradeRPCDTO> queryByOptimizedParam(CashierSearchBatchPageReqRPCVo queryReqRPCVo) {
    	return cashierOrderSettlementManager.queryByOptimizedParam(queryReqRPCVo);
    }


    @Override
    public CashierMultipleTradeListRPCDTO queryCashierMultipleSettlementList(CashierMultipleQueryReqRPCVo reqRPCVo){
        String companyId = reqRPCVo.getCompanyId();
        String employeeId = reqRPCVo.getEmployeeId();
        String fbOrderId = reqRPCVo.getFbOrderId();
        List<CashierOrderSettlement> cashierOrderSettlements = cashierOrderSettlementManager.selectCashierSettlementsByFbOrderIdAndEmpId(fbOrderId, employeeId);
        List<CashierMultipleTradeRPCDTO> tradeRPCDTOS = new ArrayList<>();
        CashierMultipleTradeListRPCDTO listRPCDTO = new CashierMultipleTradeListRPCDTO(employeeId,companyId,fbOrderId);
        if(ObjUtils.isNotEmpty(cashierOrderSettlements)){
            CashierMultipleTradeRPCDTO tradeRPCDTO;
            BigDecimal listAll = BigDecimal.ZERO;
            for (CashierOrderSettlement cashier: cashierOrderSettlements) {
                tradeRPCDTO = new CashierMultipleTradeRPCDTO();
                BeanUtils.copyProperties(cashier,tradeRPCDTO);
                CashierTradeEnum cashierTradeEnum = CashierTradeEnum.valueOf(cashier.getCashierTradeType());
                BigDecimal allYuan =  BigDecimalUtils.fenToYuan(cashier.getAmountAll());
                allYuan = allYuan.multiply(cashierTradeEnum.getPlusMinus().getKey());
                if(CashierTradeEnum.isRefund(cashier.getCashierTradeType())){
                    listRPCDTO.setTip(CommonConstant.REFUND_TIP);
                }
                CashierMultipleTradeEnum multipleTradeEnum = CashierMultipleTradeEnum.getEnum(cashier.getPayStatus(),cashier.getCashierTradeType());
                tradeRPCDTO.setPayStatusDesc(multipleTradeEnum.getTradeName());
                tradeRPCDTO.setTradeNameColor(multipleTradeEnum.getTradeNameColor());
                tradeRPCDTO.setAmountAll(allYuan);
                tradeRPCDTO.setAmountAllStr(NumberFormatUtil.moneyFormart(allYuan));
                listAll = listAll.add(allYuan);
                tradeRPCDTOS.add(tradeRPCDTO);
            }
            listRPCDTO.setTradeRPCDTOS(tradeRPCDTOS);
            listRPCDTO.setAmountListAll(listAll);
            listRPCDTO.setAmountListAllStr(NumberFormatUtil.moneyFormart(listAll));
        }
        return listRPCDTO;
    }


    @Override
    public CashierSearchMultipleTradeRPCDTO queryCashierMultipleDistinctFbOrderIdDetail(CashierMultipleQueryReqRPCVo cashierMultipleQueryReqRPCVo) {
        CashierQueryReqRPCVo cashierQueryReqRPCVo = new CashierQueryReqRPCVo();
        BeanUtils.copyProperties(cashierMultipleQueryReqRPCVo,cashierQueryReqRPCVo);
        return cashierOrderSettlementManager.queryCashierDistinctFbOrderIdDetail4Multiple(cashierQueryReqRPCVo);
    }

    @Override
    public List<CashierSearchMultipleTradeRPCDTO> searchBatchMultiplePayListGroupByFbOrderId(CashierSearchBatchOrderIdReqRPCVo searchBatchOrderIdReqRPCVo) {
        return cashierOrderSettlementManager.searchBatchMultiplePayListGroupByFbOrderId(searchBatchOrderIdReqRPCVo);
    }

    @Override
    public CashierSearchMultipleTradeDetailRPCDTO searchMultiplePayDetailGroupByFbOrderId(CashierSearchDetailReqRPCVo searchDetailReqRPCVo) {
        return cashierOrderSettlementManager.searchMultiplePayDetailGroupByFbOrderId(searchDetailReqRPCVo);
    }

    @Override
    public List<CashierQueryRPCDTO> searchPayDetail(CashierQueryReqRPCVo cashierQueryReqRPCVo) {
        //先按照接口参数查询，如果为空，反向查询
        List<CashierQueryRPCDTO> queryRPCDTOS = cashierOrderSettlementManager.searchPayDetail(cashierQueryReqRPCVo);
        //无子单查主单
        if(ObjUtils.isEmpty(queryRPCDTOS)){
            //反着查
            Integer orderRootType = cashierQueryReqRPCVo.getOrderRootType();
            if(OrderRootTypeEnum.isRoot(orderRootType)){
                orderRootType = OrderRootTypeEnum.CHILD_ORDER.getKey();
            }else{
                orderRootType = OrderRootTypeEnum.ROOT_ORDER.getKey();
            }
            cashierQueryReqRPCVo.setOrderRootType(orderRootType);
            queryRPCDTOS =  cashierOrderSettlementManager.searchPayDetail(cashierQueryReqRPCVo);
        }
        return queryRPCDTOS;
    }

    @Override
    public List<CashierBatchQueryRPCDTO> searchBatchPayList(CashierBatchQueryReqRPCVo cashierBatchQueryReqRpcVo) {
        List<CashierBatchQueryRPCDTO> cashierBatchQueryRPCDTOS;
        if(OrderRootTypeEnum.isRoot(cashierBatchQueryReqRpcVo.getOrderRootType())){
            cashierBatchQueryRPCDTOS = cashierOrderSettlementManager.searchBatchPayList(cashierBatchQueryReqRpcVo);
        }else {
            //先查子单，剩余再查主单
            String[] fbOrderIds = cashierBatchQueryReqRpcVo.getFbOrderIds();
            List<String> oriOrderIds = Lists.newArrayList(fbOrderIds);
            cashierBatchQueryRPCDTOS = cashierOrderSettlementManager.searchBatchPayList(cashierBatchQueryReqRpcVo);
            List<String> childOrderIds = cashierBatchQueryRPCDTOS.stream().map(CashierBatchQueryRPCDTO::getFbOrderId).collect(Collectors.toList());
            oriOrderIds.removeAll(childOrderIds);
            if(ObjUtils.isNotEmpty(oriOrderIds)){
                cashierBatchQueryReqRpcVo.setFbOrderIds(oriOrderIds.toArray(new String [oriOrderIds.size()]));
                cashierBatchQueryReqRpcVo.setOrderRootType(OrderRootTypeEnum.ROOT_ORDER.getKey());
                List<CashierBatchQueryRPCDTO> rootRPCDTOs = cashierOrderSettlementManager.searchBatchPayList(cashierBatchQueryReqRpcVo);
                cashierBatchQueryRPCDTOS.addAll(rootRPCDTOs);
            }
        }
        return cashierBatchQueryRPCDTOS;
    }

    @Override
    public List<CostAttributionRPCDTO> searchCostAttributionByOrderId(String orderId) {

        CashierOrderRefundSettlement refundSettlement = cashierOrderRefundSettlementManager.queryCashierRefundSettlementByRefundOrderId(orderId);
        List<CashierOrderCostAttribution> costAttributions = Lists.newArrayList();
        if(null == refundSettlement || StringUtils.isBlank(refundSettlement.getFbOrderId())){
             costAttributions = cashierOrderCostAttributionManager.getCashierOrderCostAttributionByFbOrderId(orderId);
        }else {
            costAttributions = cashierOrderCostAttributionManager.getCashierOrderCostAttributionByFbOrderId(refundSettlement.getFbOrderId());
        }
        List<CostAttributionRPCDTO> costAttributionRPCDTOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(costAttributions)){
            for (CashierOrderCostAttribution  costAttribution : costAttributions){
                CostAttributionRPCDTO dto = new CostAttributionRPCDTO();
                BeanUtils.copyProperties(costAttribution,dto);
                dto.setOrderTime(costAttribution.getCreateTime());
                costAttributionRPCDTOList.add(dto);
            }
        }
        return costAttributionRPCDTOList;
    }

    @Override
    public CashierPayDetailRPCDTO searchCashierPayDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        //查询 主单 支付信息
        CashierPayDetailRPCDTO cashierPayDetail = new CashierPayDetailRPCDTO();
        try {
            buildCashierPayDetailRPCDTOByCashierOrderSettlement(cashierPayDetailRPCVo,cashierPayDetail);
            buildCashierPayDetailRPCDTOByCashierOrderRefundSettlement(cashierPayDetailRPCVo,cashierPayDetail);
            buildTaxiOrderWithFinish(cashierPayDetailRPCVo,cashierPayDetail);
        }catch (Exception e){
            FinhubLogger.error("searchCashierPayDetail orderId:{},error:{}",cashierPayDetailRPCVo.getFbOrderId(),e.getMessage());
        }
        return cashierPayDetail;
    }

    private  void buildCashierPayDetailRPCDTOByCashierOrderSettlement(CashierPayDetailRPCVo cashierPayDetailRPCVo,CashierPayDetailRPCDTO cashierPayDetail){
        CashierOrderSettlement cashierOrder = cashierOrderSettlementManager.queryCashierSettlementByOrderIdAndTradeId(cashierPayDetailRPCVo.getFbOrderId(), cashierPayDetailRPCVo.getOrderRootType(),null);
        if(null == cashierOrder || StringUtils.isBlank(cashierOrder.getCashierTxnId())){
            cashierOrder = cashierOrderSettlementManager.queryCashierSettlementByOrderIdAndTradeId(cashierPayDetailRPCVo.getFbOrderId(), OrderRootTypeEnum.ROOT_ORDER.getKey(),null);
        }
        if(null != cashierOrder){
            checkCashierDetail(cashierOrder);
            BeanUtils.copyProperties(cashierOrder,cashierPayDetail);
            cashierPayDetail.setAmountIndividualVoucher(cashierOrder.getAmountVoucherIndividual());
            cashierPayDetail.setAmountRedcouponVoucher(cashierOrder.getAmountVoucherRedcoupon());
            cashierPayDetail.setAmountAll(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
            cashierPayDetail.setAmountAllNoSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()).subtract(cashierOrder.getCompanyNoSettlePrice()));
            cashierPayDetail.setAmountAllHasSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
            if(OrderType.BANK_INDIVIDUAL.getKey() == cashierOrder.getOrderType() || OrderType.ACCT_PUBLIC_PAY.getKey() == cashierOrder.getOrderType()){
                cashierPayDetail.setAmountCompany(cashierOrder.getAmountBank());
                cashierPayDetail.setAmountAll(cashierPayDetail.getAmountPersonal().add(cashierOrder.getAmountBank()));
            }
            cashierPayDetail.setCompanyId(cashierOrder.getCompanyId());
            cashierPayDetail.setAccountType(cashierOrder.getAccountType());
        }
    }

    private void buildCashierPayDetailRPCDTOByCashierOrderRefundSettlement(CashierPayDetailRPCVo cashierPayDetailRPCVo,CashierPayDetailRPCDTO cashierPayDetail){
        List<CashierOrderRefundSettlement> refundSettlements = cashierOrderRefundSettlementManager.queryCashierOrderRefundSettlementByRootOrderId(cashierPayDetailRPCVo.getFbOrderId(),cashierPayDetailRPCVo.getOrderRootType());
        if(null == cashierPayDetail.getAmountRedcouponVoucher()){
            cashierPayDetail.setAmountRedcouponVoucher(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountIndividualVoucher()){
            cashierPayDetail.setAmountIndividualVoucher(BigDecimal.ZERO);
        }
        if(CollectionUtils.isNotEmpty(refundSettlements)){
            List<CashierOrderRefundSettlement> cashierOrderRefundSettlementList = refundSettlements.stream().filter(refundSettlement -> XeRefundStatus.NO_XE.getKey() == refundSettlement.getXeStatus()).collect(Collectors.toList());
            //有退款 加和支付能力
            for(CashierOrderRefundSettlement refundSettlement : cashierOrderRefundSettlementList){
                checkRefundDetail(refundSettlement);
                refundSettlement.setRefundAmountAll(refundSettlement.getRefundAmountPersonal().add(refundSettlement.getRefundAmountCompany()).add(refundSettlement.getRefundAmountRedcoupon()));
                if(OrderType.BANK_INDIVIDUAL.getKey() == refundSettlement.getOrderType() || OrderType.ACCT_PUBLIC_PAY.getKey() == refundSettlement.getOrderType()){
                    refundSettlement.setRefundAmountCompany(refundSettlement.getRefundAmountBank());
                    refundSettlement.setRefundAmountAll(refundSettlement.getRefundAmountPersonal().add(refundSettlement.getRefundAmountBank()).add(refundSettlement.getRefundAmountRedcoupon()));
                }
                cashierPayDetail.setAmountAll(cashierPayDetail.getAmountAll().subtract(refundSettlement.getRefundAmountAll()));
                cashierPayDetail.setAmountPersonal(cashierPayDetail.getAmountPersonal().subtract(refundSettlement.getRefundAmountPersonal()));
                cashierPayDetail.setAmountCompany(cashierPayDetail.getAmountCompany().subtract(refundSettlement.getRefundAmountCompany()));
                cashierPayDetail.setAmountFbb(cashierPayDetail.getAmountFbb().subtract(refundSettlement.getRefundAmountFbb()));
                cashierPayDetail.setAmountIndividualVoucher(cashierPayDetail.getAmountIndividualVoucher().subtract(null == refundSettlement.getAmountVoucherIndividual() ? BigDecimal.ZERO :refundSettlement.getAmountVoucherIndividual()));
                cashierPayDetail.setAmountRedcoupon(cashierPayDetail.getAmountRedcoupon().subtract(refundSettlement.getRefundAmountRedcoupon()));
                cashierPayDetail.setAmountThird(cashierPayDetail.getAmountThird().subtract(refundSettlement.getRefundAmountThird()));
                cashierPayDetail.setAmountVoucher(cashierPayDetail.getAmountVoucher().subtract(refundSettlement.getRefundAmountVoucher()));
                cashierPayDetail.setAmountRedcouponVoucher(cashierPayDetail.getAmountRedcouponVoucher().subtract(null == refundSettlement.getAmountVoucherRedcoupon() ? BigDecimal.ZERO : refundSettlement.getAmountVoucherRedcoupon()));
                cashierPayDetail.setAmountAllNoSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
                cashierPayDetail.setAmountAllHasSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
                cashierPayDetail.setCompanyId(refundSettlement.getCompanyId());
                cashierPayDetail.setAccountType(refundSettlement.getAccountType());
            }
        }
    }

    private void buildTaxiOrderWithFinish(CashierPayDetailRPCVo cashierPayDetailRPCVo,CashierPayDetailRPCDTO cashierPayDetail){
        CashierOrderSettlement finishCashierOrder = cashierOrderSettlementManager.queryCashierSettlementByOrderIdAndTradeId(cashierPayDetailRPCVo.getFbOrderId(),OrderRootTypeEnum.ROOT_ORDER.getKey(),"finish");
        if(null != finishCashierOrder){
            checkCashierDetail(finishCashierOrder);
            if(RefundStatus.REFUND_SUCCESS_DONE.getKey() == finishCashierOrder.getPayStatus()){
                cashierPayDetail.setAmountAll(cashierPayDetail.getAmountAll().subtract(finishCashierOrder.getAmountAll()));
                cashierPayDetail.setAmountPersonal(cashierPayDetail.getAmountPersonal().subtract(finishCashierOrder.getAmountPersonal()));
                cashierPayDetail.setAmountCompany(cashierPayDetail.getAmountCompany().subtract(finishCashierOrder.getAmountCompany()));
                cashierPayDetail.setAmountFbb(cashierPayDetail.getAmountFbb().subtract(finishCashierOrder.getAmountFbb()));
                cashierPayDetail.setAmountIndividualVoucher(cashierPayDetail.getAmountIndividualVoucher().subtract(null == finishCashierOrder.getAmountVoucherIndividual() ? BigDecimal.ZERO :finishCashierOrder.getAmountVoucherIndividual()));
                cashierPayDetail.setAmountRedcoupon(cashierPayDetail.getAmountRedcoupon().subtract(finishCashierOrder.getAmountRedcoupon()));
                cashierPayDetail.setAmountThird(cashierPayDetail.getAmountThird().subtract(finishCashierOrder.getAmountThird()));
                cashierPayDetail.setAmountVoucher(cashierPayDetail.getAmountVoucher().subtract(finishCashierOrder.getAmountVoucher()));
                cashierPayDetail.setAmountRedcouponVoucher(cashierPayDetail.getAmountRedcouponVoucher().subtract(null == finishCashierOrder.getAmountVoucherRedcoupon() ? BigDecimal.ZERO : finishCashierOrder.getAmountVoucherRedcoupon()));
                cashierPayDetail.setAmountAllNoSettlePrice(cashierPayDetail.getAmountAll().subtract(finishCashierOrder.getAmountAll()));
                cashierPayDetail.setAmountAllHasSettlePrice(cashierPayDetail.getAmountAll().subtract(finishCashierOrder.getAmountAll()));
                cashierPayDetail.setCompanyId(finishCashierOrder.getCompanyId());
                cashierPayDetail.setAccountType(finishCashierOrder.getAccountType());
            }else if(CashierPayStatus.CASHIER_SUCCESS_DONE.getKey() == finishCashierOrder.getPayStatus()){
                cashierPayDetail.setAmountAll(cashierPayDetail.getAmountAll().add(finishCashierOrder.getAmountAll()));
                cashierPayDetail.setAmountPersonal(cashierPayDetail.getAmountPersonal().add(finishCashierOrder.getAmountPersonal()));
                cashierPayDetail.setAmountCompany(cashierPayDetail.getAmountCompany().add(finishCashierOrder.getAmountCompany()));
                cashierPayDetail.setAmountFbb(cashierPayDetail.getAmountFbb().add(finishCashierOrder.getAmountFbb()));
                cashierPayDetail.setAmountIndividualVoucher(cashierPayDetail.getAmountIndividualVoucher().add(null == finishCashierOrder.getAmountVoucherIndividual() ? BigDecimal.ZERO :finishCashierOrder.getAmountVoucherIndividual()));
                cashierPayDetail.setAmountRedcoupon(cashierPayDetail.getAmountRedcoupon().add(finishCashierOrder.getAmountRedcoupon()));
                cashierPayDetail.setAmountThird(cashierPayDetail.getAmountThird().add(finishCashierOrder.getAmountThird()));
                cashierPayDetail.setAmountVoucher(cashierPayDetail.getAmountVoucher().add(finishCashierOrder.getAmountVoucher()));
                cashierPayDetail.setAmountRedcouponVoucher(cashierPayDetail.getAmountRedcouponVoucher().add(null == finishCashierOrder.getAmountVoucherRedcoupon() ? BigDecimal.ZERO : finishCashierOrder.getAmountVoucherRedcoupon()));
                cashierPayDetail.setAmountAllNoSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
                cashierPayDetail.setAmountAllHasSettlePrice(cashierPayDetail.getAmountPersonal().add(cashierPayDetail.getAmountCompany()).add(cashierPayDetail.getAmountRedcoupon()));
                cashierPayDetail.setCompanyId(finishCashierOrder.getCompanyId());
                cashierPayDetail.setAccountType(finishCashierOrder.getAccountType());
            }
        }
    }

    @Override
    public CashierPayDetailRPCDTO searchCashierPayFlowDetail(String orderId) {
        //查询 支付流水 企业支付  红包券支付 分贝券支付 分贝币支付 三方支付
        // 红包券支付 分贝券支付 消费退款金额都是正
        CashierPayDetailRPCDTO cashierPayFlowDetail = new CashierPayDetailRPCDTO();
        try {
            //虚拟卡 对公付款单独处理
            CashierOrderSettlement cashierOrder = cashierOrderSettlementManager.queryCashierSettlementByOrderIdAndTradeId(orderId,OrderRootTypeEnum.ROOT_ORDER.getKey(),null);
            Integer orderType;
            if(ObjUtils.isNull(cashierOrder)){
                //兼容减免单
                List<CashierOrderRefundSettlement> refundSettlements = cashierOrderRefundSettlementManager.queryCashierOrderRefundSettlementByRootOrderId(orderId,OrderRootTypeEnum.ROOT_ORDER.getKey());
                if(CollectionUtils.isEmpty(refundSettlements)){
                    return cashierPayFlowDetail;
                }
                orderType = refundSettlements.get(0).getOrderType();
            }else {
                orderType = cashierOrder.getOrderType();
            }
            if(OrderType.BANK_INDIVIDUAL.getKey() == orderType){
                List<BankCardApplyFlow> bankCardApplyFlowList = bankCardApplyFlowManger.queryBankCardFlowByBizNo(orderId);
                BigDecimal bankConsume = BigDecimal.ZERO;
                BigDecimal bankRefund = BigDecimal.ZERO;
                for (BankCardApplyFlow flow : bankCardApplyFlowList){
                    if(BankApplyType.CONSUMPTION.getKey() ==flow.getOperationType()){
                        bankConsume = bankConsume.add(flow.getOperationAmount());
                    }else if(BankApplyType.REFUND.getKey() ==flow.getOperationType() || BankApplyType.RETURN.getKey() ==flow.getOperationType()){
                        bankRefund = bankRefund.add(flow.getOperationAmount());
                    }
                }
                BigDecimal amountThird = cashierThirdPayManager.queryPayRecordListByOrderId(orderId);
                if(null != amountThird){
                    cashierPayFlowDetail.setAmountThird(amountThird);
                }
                cashierPayFlowDetail.setAmountAll(bankConsume.subtract(bankRefund));
                cashierPayFlowDetail.setAmountCompany(bankConsume.subtract(bankRefund));
                return cashierPayFlowDetail;
            }else if(OrderType.ACCT_PUBLIC_PAY.getKey() == orderType){
                BigDecimal amount = acctPublicPayManager.queryTotalConsumeAmountByOrderId(orderId).negate();
                cashierPayFlowDetail.setAmountAll(amount);
                cashierPayFlowDetail.setAmountCompany(amount);
                return cashierPayFlowDetail;
            }
            BigDecimal amountCompany = uAcctCommonService.queryAccountSubFlowByBizNo(orderId);
            //新账户根据账户类型模式判断账户流水
            if(null != amountCompany){
                cashierPayFlowDetail.setAmountCompany(amountCompany.negate());
            }
            BigDecimal amountFbb = personAccountFowService.queryPersonAccountFlowByOrderId(orderId);
            if(null != amountFbb){
                cashierPayFlowDetail.setAmountFbb(amountFbb);
            }
            BigDecimal amountThird = cashierThirdPayManager.queryPayRecordListByOrderId(orderId);
            if(null != amountThird){
                cashierPayFlowDetail.setAmountThird(amountThird);
            }
            List<VouchersOperationFlow> vouchersOperationFlowList = vouchersOperationFlowService.queryVouchersFlowByOrderId(orderId);
            BigDecimal amountVoucher = BigDecimal.ZERO;
            if(CollectionUtils.isNotEmpty(vouchersOperationFlowList)){
                BigDecimal amountRedcouponVoucher = BigDecimal.ZERO;
                BigDecimal amountRedcouponVoucherConsume = BigDecimal.ZERO;
                BigDecimal amountRedcouponVoucherRefund = BigDecimal.ZERO;
                BigDecimal amountVoucherConsume = BigDecimal.ZERO;
                BigDecimal amountVoucherRefund = BigDecimal.ZERO;
                for(VouchersOperationFlow vouchersOperationFlow : vouchersOperationFlowList){
                    if(VoucherFlowType.CONSUMPTION.getValue() == vouchersOperationFlow.getType()){
                        amountVoucherConsume = amountVoucherConsume.add(vouchersOperationFlow.getAmount());
                        if(FundAccountSubType.isRedcouponAccount(vouchersOperationFlow.getDeductionAccountType())){
                            amountRedcouponVoucherConsume = amountRedcouponVoucherConsume.add(vouchersOperationFlow.getAmount());
                        }
                    }
                    if(VoucherFlowType.REFUND.getValue() == vouchersOperationFlow.getType()){
                        amountVoucherRefund = amountVoucherRefund.add(vouchersOperationFlow.getAmount());
                        if(FundAccountSubType.isRedcouponAccount(vouchersOperationFlow.getDeductionAccountType())){
                            amountRedcouponVoucherRefund = amountRedcouponVoucherRefund.add(vouchersOperationFlow.getAmount());
                        }
                    }
                }
                amountRedcouponVoucher = amountRedcouponVoucherConsume.subtract(amountRedcouponVoucherRefund);
                amountVoucher = amountVoucherConsume.subtract(amountVoucherRefund);
                cashierPayFlowDetail.setAmountVoucher(amountVoucher);
                cashierPayFlowDetail.setAmountRedcouponVoucher(amountRedcouponVoucher);
            }
        }catch (Exception e){
            FinhubLogger.error("searchCashierPayFlowDetail orderId:{},error:{}",orderId,e.getMessage());
        }
        return cashierPayFlowDetail;
    }

    @Override
    public CashierSearchPayStatusRPCDTO searchPayStatus(CashierQueryReqRPCVo cashierQueryReqRPCVo) {
        if(ObjUtils.isEmpty(cashierQueryReqRPCVo)||ObjUtils.isBlank(cashierQueryReqRPCVo.getFbOrderId())){
            return null;
        }
        //只查询主单支付状态
        cashierQueryReqRPCVo.setIsFindThird(false);
        cashierQueryReqRPCVo.setIsFindPayRecord(false);
        List<CashierOrderSettlement> orderSettlements = cashierOrderSettlementManager.queryCashierOrderSettlements(cashierQueryReqRPCVo);
        if(ObjUtils.isEmpty(orderSettlements)){
            return null;
        }
        CashierSearchPayStatusRPCDTO rpcdto = new CashierSearchPayStatusRPCDTO();
        CashierOrderSettlement cashierOrderSettlement = orderSettlements.get(0);
        BeanUtils.copyProperties(cashierOrderSettlement,rpcdto);
        rpcdto.setCashierPayStatus(cashierOrderSettlement.getPayStatus());
        return rpcdto;
    }

    @Override
    public CashierSearchPayAmountRpcDTO searchPayAmount(CashierQueryReqRPCVo reqRPCVo) {
        List<CashierOrderSettlement> orderSettlements = cashierOrderSettlementManager.queryCashierOrderSettlements(reqRPCVo);
        if(ObjUtils.isEmpty(orderSettlements)){
            return null;
        }
        return getCashierSearchPayAmountRpcDTOByCashierOrderSettlement(orderSettlements);
    }

    private CashierSearchPayAmountRpcDTO getCashierSearchPayAmountRpcDTOByCashierOrderSettlement(List<CashierOrderSettlement> cashierOrderSettlementList){
        CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementList.get(0);
        CashierSearchPayAmountRpcDTO cashierSearchPayAmountRpcDTO = new CashierSearchPayAmountRpcDTO();
        cashierSearchPayAmountRpcDTO.setFbOrderId(cashierOrderSettlement.getFbOrderId());
        cashierSearchPayAmountRpcDTO.setCashierPayStatus(cashierOrderSettlement.getPayStatus());
        cashierSearchPayAmountRpcDTO.setCashierTxnId(cashierOrderSettlement.getCashierTxnId());
        cashierSearchPayAmountRpcDTO.setAmountAll(cashierOrderSettlement.getAmountAll());

        if (CashierPayStatus.cashierCompanyPreSuccess(cashierOrderSettlement.getPayStatus()) ||CashierPayStatus.cashierNoNeedPay(cashierOrderSettlement.getPayStatus())){
            cashierSearchPayAmountRpcDTO.setPaidAmount(cashierOrderSettlement.getAmountAll());
        } else if (CashierPayStatus.cashierThirdPartyTrading(cashierOrderSettlement.getPayStatus()) ||CashierPayStatus.cashierCashierNotEnough(cashierOrderSettlement.getPayStatus())){
            BigDecimal paidAmount = cashierOrderSettlement.getAmountCompany()
                    .add(cashierOrderSettlement.getAmountRedcoupon())
                    .add(cashierOrderSettlement.getAmountFbb())
                    .add(cashierOrderSettlement.getAmountVoucher());
            if (cashierOrderSettlement.getAmountBank() !=null){
                paidAmount = paidAmount.add(cashierOrderSettlement.getAmountBank());
            }
            if (cashierOrderSettlement.getBankCompanyAmount() !=null){
                paidAmount = paidAmount.add(cashierOrderSettlement.getBankCompanyAmount());
            }
            if (cashierOrderSettlement.getBankRedcouponAmount() !=null){
                paidAmount = paidAmount.add(cashierOrderSettlement.getBankRedcouponAmount());
            }
            cashierSearchPayAmountRpcDTO.setPaidAmount(paidAmount);
        }

        return cashierSearchPayAmountRpcDTO;
    }

    @Override
    public CashierPayDetailRPCDTO searchCashierPayDetailByOrderId(String fbOrderId, Integer orderRootType) {
        if (ObjUtils.isBlank(fbOrderId) || ObjUtils.isNull(orderRootType)) {
            return null;
        }
        if (OrderRootTypeEnum.isChild(orderRootType)) {
            CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementManager.queryCashierOrderSettlementByChildOrderId(fbOrderId);
            if (cashierOrderSettlement == null) {
                return null;
            }
            CashierPayDetailRPCDTO dto = new CashierPayDetailRPCDTO();
            BeanUtils.copyProperties(cashierOrderSettlement, dto);
            return dto;
        }
        List<CashierOrderSettlement> cashierOrderSettlements = cashierOrderSettlementManager.queryCashierOrderSettlementByRootOrderId(fbOrderId);
        if (ObjUtils.isEmpty(cashierOrderSettlements)) {
            return null;
        }
        CashierPayDetailRPCDTO dto = new CashierPayDetailRPCDTO();

        List<CashierOrderSettlement> insuranceOrderSettlements = new ArrayList<>();
        CashierPayDetailRPCDTO businessDto = new CashierPayDetailRPCDTO();
        BigDecimal amountAllR = BigDecimal.ZERO;
        BigDecimal amountCompany = BigDecimal.ZERO;
        BigDecimal amountRedcoupon = BigDecimal.ZERO;
        BigDecimal amountFbb = BigDecimal.ZERO;
        BigDecimal amountVoucher = BigDecimal.ZERO;
        BigDecimal amountRedcouponVoucher =  BigDecimal.ZERO;
        BigDecimal amountThird =  BigDecimal.ZERO;
        BigDecimal amountBank = BigDecimal.ZERO;
        String companyId = null;
        for (CashierOrderSettlement cashierOrderSettlement : cashierOrderSettlements) {
            if (cashierOrderSettlement == null) {
                continue;
            }
            Integer rootType = cashierOrderSettlement.getOrderRootType();
            Integer orderType = cashierOrderSettlement.getOrderType();
            BigDecimal amountAll = cashierOrderSettlement.getAmountAll();
            if (OrderRootTypeEnum.isRoot(rootType)) {
                dto.setOrderType(cashierOrderSettlement.getOrderType());
                dto.setPayStatus(cashierOrderSettlement.getPayStatus());

                //用车兼容
                if (CashierTradeEnum.isRefund(cashierOrderSettlement.getCashierTradeType())){
                    amountAllR = amountAllR.subtract(cashierOrderSettlement.getAmountAll());
                    amountCompany = amountCompany.subtract(cashierOrderSettlement.getAmountCompany());
                    amountRedcoupon = amountRedcoupon.subtract(cashierOrderSettlement.getAmountRedcoupon());
                    amountFbb = amountFbb.subtract(cashierOrderSettlement.getAmountFbb());
                    amountVoucher = amountVoucher.subtract(cashierOrderSettlement.getAmountVoucher());
                    amountRedcouponVoucher = amountRedcouponVoucher.subtract(cashierOrderSettlement.getAmountVoucherRedcoupon());
                    amountThird = amountThird.subtract(cashierOrderSettlement.getAmountThird());
                    BigDecimal tAmountBank = cashierOrderSettlement.getAmountBank() == null?BigDecimal.ZERO:cashierOrderSettlement.getAmountBank();
                    amountBank = amountBank.subtract(tAmountBank);
                }
                if (CashierTradeEnum.isPay(cashierOrderSettlement.getCashierTradeType()) ||CashierTradeEnum.isPrePay(cashierOrderSettlement.getCashierTradeType())){
                    amountAllR = amountAllR.add(cashierOrderSettlement.getAmountAll());
                    amountCompany = amountCompany.add(cashierOrderSettlement.getAmountCompany());
                    amountRedcoupon = amountRedcoupon.add(cashierOrderSettlement.getAmountRedcoupon());
                    amountFbb = amountFbb.add(cashierOrderSettlement.getAmountFbb());
                    amountVoucher = amountVoucher.add(cashierOrderSettlement.getAmountVoucher());
                    amountRedcouponVoucher = amountRedcouponVoucher.add(cashierOrderSettlement.getAmountVoucherRedcoupon());
                    amountThird = amountThird.add(cashierOrderSettlement.getAmountThird());
                    BigDecimal tAmountBank = cashierOrderSettlement.getAmountBank() == null?BigDecimal.ZERO:cashierOrderSettlement.getAmountBank();
                    amountBank = amountBank.add(tAmountBank);
                }
                dto.setAmountAll(amountAllR);
                dto.setAmountCompany(amountCompany);
                dto.setAmountRedcoupon(amountRedcoupon);
                dto.setAmountFbb(amountFbb);
                dto.setAmountVoucher(amountVoucher);
                dto.setAmountRedcouponVoucher(amountRedcouponVoucher);
                dto.setAmountThird(amountThird);
                dto.setAmountBank(amountBank);
                dto.setAmountAllHasSettlePrice(amountAllR);
                dto.setAmountAllNoSettlePrice(amountAllR);
                dto.setAmountTotalNoSettlePrice(getTotalAmount(dto));
                dto.setAmountTotalHasSettlePrice(getTotalAmount(dto));
                dto.setAccountType(cashierOrderSettlement.getAccountType());
                dto.setCompanyId(cashierOrderSettlement.getCompanyId());
                continue;
            }
            if (OrderRootTypeEnum.isChild(rootType)
                    && orderType != CategoryTypeEnum.Insurance.getCode()) {
                BeanUtils.copyProperties(cashierOrderSettlement, businessDto);
                businessDto.setAmountRedcouponVoucher(cashierOrderSettlement.getAmountVoucherRedcoupon());
                dto.setAmountAllNoSettlePrice(amountAll);
                dto.setAmountTotalNoSettlePrice(getTotalAmount(businessDto));
                dto.setAccountType(cashierOrderSettlement.getAccountType());
                dto.setCompanyId(cashierOrderSettlement.getCompanyId());
                continue;
            }
            insuranceOrderSettlements.add(cashierOrderSettlement);
            companyId = cashierOrderSettlement.getCompanyId();
        }

        List<CashierPayDetailRPCDTO> insuranceDTOS = new ArrayList<>();
        insuranceOrderSettlements.stream().filter(Objects::nonNull).forEach(e -> {
            CashierPayDetailRPCDTO insuranceDTO = new CashierPayDetailRPCDTO();
            BeanUtils.copyProperties(e, insuranceDTO);
            insuranceDTO.setAmountRedcouponVoucher(e.getAmountVoucherRedcoupon());
            insuranceDTOS.add(insuranceDTO);
        });
        dto.setBusinessDTO(businessDto);
        dto.setInsuranceDTOS(insuranceDTOS);
        dto.setCompanyId(companyId);
        return dto;
    }
    private BigDecimal getTotalAmount(CashierPayDetailRPCDTO cashierOrderSettlement) {
        return addOpt(cashierOrderSettlement.getAmountCompany(),
                cashierOrderSettlement.getAmountRedcoupon(),
                cashierOrderSettlement.getAmountVoucher(),
                cashierOrderSettlement.getAmountFbb(),
                cashierOrderSettlement.getAmountThird(),
                cashierOrderSettlement.getAmountBank()
        );
    }
    private BigDecimal addOpt(BigDecimal... amounts) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (BigDecimal amount : amounts) {
            totalAmount = totalAmount.add(amount == null ? BigDecimal.ZERO : amount);
        }
        return totalAmount;
    }

    @Override
    public CashierPayDetailRPCDTO searchAccountPayFlowDetail(CashierPayDetailRPCVo cashierPayDetailRPCVo) {
        CashierPayDetailRPCDTO dto = new CashierPayDetailRPCDTO();
        if(ObjUtils.isNull(cashierPayDetailRPCVo)){
            return dto;
        }
        String fbOrderId = cashierPayDetailRPCVo.getFbOrderId();
        Integer orderRootType = cashierPayDetailRPCVo.getOrderRootType();
        Integer accountType = cashierPayDetailRPCVo.getAccountType();
        if(ObjUtils.isBlank(fbOrderId)||ObjUtils.isNull(orderRootType)||ObjUtils.isNull(accountType)){
            return dto;
        }
        //分贝币
        dto.setAmountFbb(personAccountFowService.queryTotalAmountByOrderIdAndType(fbOrderId, PersonPayBusinessType.ConsumerExp.getKey()));
        //三方
        PersonPayRecord personPayRecord = personPayRecordService.querySuccessPayRecordByOrderId(fbOrderId);
        if (personPayRecord != null) {
            Long payRecordAmount = personPayRecord.getAmount();
            dto.setAmountThird(payRecordAmount == null ? BigDecimal.ZERO : new BigDecimal(payRecordAmount));
        }
        if(AccountTypeEnum.isPublic(accountType)){
            CashierOrderSettlement settlement = cashierOrderSettlementManager.selectCashierSettlementByFbOrderId(fbOrderId, OrderRootTypeEnum.ROOT_ORDER.getKey());
            if (ObjUtils.isNull(settlement)) {
                throw new FinhubException(GlobalResponseCode.CASHIER_PAY_SETTLE_NOT_EXIST.getCode(), GlobalResponseCode.CASHIER_PAY_SETTLE_NOT_EXIST.getMsg());
            }
            Integer accountModel = settlement.getAccountModel();
            //商务账户支付
            BigDecimal amountCompany = BigDecimal.ZERO;
            if (FundAccountModelType.isRecharge(accountModel)) {
                List<AcctBusinessDebitFlow> acctBusinessDebitFlows = acctBusinessDebitFlowService.queryAccountSubFlowByBizNo(fbOrderId, FundAcctCreditOptType.PUBLIC_CONSUME.getKey());
                if (ObjUtils.isNotEmpty(acctBusinessDebitFlows)) {
                    for (AcctBusinessDebitFlow flow : acctBusinessDebitFlows) {
                        if (flow != null && flow.getOperationAmount() != null) {
                            amountCompany = amountCompany.add(flow.getOperationAmount());
                        }
                    }
                }
            }
            if (FundAccountModelType.isCredit(accountModel)) {
                List<AcctBusinessCreditFlow> acctBusinessCreditFlows = acctBusinessCreditFlowService.queryAccountSubFlowByBizNo(fbOrderId, FundAcctDebitOptType.PUBLIC_CONSUME.getKey());
                if (ObjUtils.isNotEmpty(acctBusinessCreditFlows)) {
                    for (AcctBusinessCreditFlow flow : acctBusinessCreditFlows) {
                        if (flow != null && flow.getOperationAmount() != null) {
                            amountCompany = amountCompany.add(flow.getOperationAmount());
                        }
                    }
                }
            }
            dto.setAmountCompany(amountCompany);
            //红包券支付
            BigDecimal amountRedcoupon = accountRedcouponSearchService.queryAccountCouponOperationAmountByBizNo(fbOrderId, RedcouponType.REDCOUPON.getKey(), Lists.newArrayList(RedcouponOperationType.PUBLIC_CONSUME.getKey()));
            dto.setAmountRedcoupon(amountRedcoupon);
        }
        if (AccountTypeEnum.isPersonal(accountType)) {
            List<VouchersOperationFlow> flowList = vouchersOperationFlowService.queryVouchersFlowByOrderIds(Lists.newArrayList(fbOrderId), VoucherFlowType.CONSUMPTION.getValue());
            //分贝券支付流水
            BigDecimal amountVoucher = BigDecimal.ZERO;
            //红包分贝券支付流水
            BigDecimal amountVoucherRedcoupon = BigDecimal.ZERO;
            if (ObjUtils.isNotEmpty(flowList)) {
                for (VouchersOperationFlow flow : flowList) {
                    if (flow != null && flow.getAmount() != null) {
                        amountVoucher = amountVoucher.add(flow.getAmount());
                    }
                    if (flow != null && flow.getAmount() != null && flow.getDeductionAccountType() != null && FundAccountSubType.isRedcouponAccount(flow.getDeductionAccountType())) {
                        amountVoucherRedcoupon = amountVoucherRedcoupon.add(flow.getAmount());
                    }
                }
            }
            dto.setAmountVoucher(amountVoucher);
            dto.setAmountRedcouponVoucher(amountVoucherRedcoupon);
        }
        return dto;
    }

    /**
     * 只查主单信息
     * @param saturnCashierOrRefundReqRPCVO saturn 查询收银单
     * @return
     */
    @Override
    public SaturnCashierOrRefundRespRPCDTO searchCashierOrRefundInfo(SaturnCashierOrRefundReqRPCVO saturnCashierOrRefundReqRPCVO) {
        SaturnCashierOrRefundRespRPCDTO cashierOrRefundRespRPCVO = new SaturnCashierOrRefundRespRPCDTO();
        List<CashierOrderSettlement> cashierOrderSettlements = cashierOrderSettlementManager.queryRootCashierSettlementByRootOrderId(saturnCashierOrRefundReqRPCVO.getBizNo());
        if (cashierOrderSettlements == null){
            CashierOrderSettlement cashierOrderSettlement = cashierOrderSettlementManager.queryCashierOrderSettlementByChildOrderId(saturnCashierOrRefundReqRPCVO.getBizNo());
            if (cashierOrderSettlement != null){
                cashierOrderSettlements = cashierOrderSettlementManager.queryRootCashierSettlementByRootOrderId(cashierOrderSettlement.getRootOrderId());
            }
        }
        CashierOrderRefundSettlement refundSettlement = cashierOrderRefundSettlementManager.queryCashierRefundSettlementByRefundOrderId(saturnCashierOrRefundReqRPCVO.getBizNo());
        if (refundSettlement != null){
            cashierOrderSettlements = cashierOrderSettlementManager.queryRootCashierSettlementByRootOrderId(refundSettlement.getRootOrderId());
            cashierOrRefundRespRPCVO.setIsRefund(true);
            cashierOrRefundRespRPCVO.setRefundAmount(refundSettlement.getRefundAmountAll());
            cashierOrRefundRespRPCVO.setRefundStatus(refundSettlement.getRefundStatus());
            cashierOrRefundRespRPCVO.setRefundOrderId(refundSettlement.getRefundOrderId());
        }
        //信息在收银台查不到
        if (ObjUtils.isEmpty(cashierOrderSettlements)){
            return null;
        }
        cashierOrRefundRespRPCVO.setRootOrderId(cashierOrderSettlements.get(0).getRootOrderId());
        cashierOrRefundRespRPCVO.setFbOrderId(cashierOrderSettlements.get(0).getFbOrderId());
        cashierOrRefundRespRPCVO.setOrderType(cashierOrderSettlements.get(0).getOrderType());
        cashierOrRefundRespRPCVO.setAccountType(cashierOrderSettlements.get(0).getAccountType());
        cashierOrRefundRespRPCVO.setCompanyId(cashierOrderSettlements.get(0).getCompanyId());
        cashierOrRefundRespRPCVO.setPayStatus(cashierOrderSettlements.get(0).getPayStatus());

        if (cashierOrderSettlements.size() > 1){
            BigDecimal amountAll = BigDecimal.ZERO;
            BigDecimal amountVoucher = BigDecimal.ZERO;
            for(CashierOrderSettlement cashierOrderSettlement:cashierOrderSettlements){
                if (OrderRootTypeEnum.isRoot(cashierOrderSettlement.getOrderRootType())){
                    if (cashierOrderSettlement.getPayStatus() == RefundStatus.REFUND_SUCCESS_DONE.getKey()){
                        amountAll = BigDecimalUtils.addPrice(cashierOrderSettlement.getAmountAll(),amountAll);
                        amountVoucher = BigDecimalUtils.addPrice(cashierOrderSettlement.getAmountAll().negate(),amountVoucher);
                    }else {
                        amountAll = BigDecimalUtils.addPrice(cashierOrderSettlement.getAmountAll(),amountAll);
                        amountVoucher = BigDecimalUtils.addPrice(cashierOrderSettlement.getAmountAll(),amountVoucher);
                    }

                    if (CashierPayStatus.cashierHadDone(cashierOrderSettlement.getPayStatus())){
                        cashierOrRefundRespRPCVO.setPayStatus(cashierOrderSettlement.getPayStatus());
                    }
                }
            }
            cashierOrRefundRespRPCVO.setPayAmount(amountAll);
            cashierOrRefundRespRPCVO.setAmountVoucher(amountVoucher);
        }else {
            cashierOrRefundRespRPCVO.setPayAmount(cashierOrderSettlements.get(0).getAmountAll());
            cashierOrRefundRespRPCVO.setAmountVoucher(cashierOrderSettlements.get(0).getAmountVoucher());
        }

        return cashierOrRefundRespRPCVO;
    }


    //=====================================Private Method=======================


    private void checkCashierDetail(CashierOrderSettlement cashierPayDetail){

        if(null == cashierPayDetail.getAmountVoucherRedcoupon()){
            cashierPayDetail.setAmountVoucherRedcoupon(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountVoucherIndividual()){
            cashierPayDetail.setAmountVoucherIndividual(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountCompany()){
            cashierPayDetail.setAmountCompany(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountRedcoupon()){
            cashierPayDetail.setAmountRedcoupon(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountFbb()){
            cashierPayDetail.setAmountFbb(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountVoucher()){
            cashierPayDetail.setAmountVoucher(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountPersonal()){
            cashierPayDetail.setAmountPersonal(BigDecimal.ZERO);
        }
        if(null == cashierPayDetail.getAmountThird()){
            cashierPayDetail.setAmountThird(BigDecimal.ZERO);
        }
    }

    private void checkRefundDetail(CashierOrderRefundSettlement refundSettlement){
        if(ObjUtils.isNull(refundSettlement.getRefundAmountRedcoupon())){
            refundSettlement.setRefundAmountRedcoupon(BigDecimal.ZERO);
        }
        if(ObjUtils.isNull(refundSettlement.getRefundAmountCompany())){
            refundSettlement.setRefundAmountCompany(BigDecimal.ZERO);
        }
        if(null == refundSettlement.getRefundAmountFbb()){
            refundSettlement.setRefundAmountFbb(BigDecimal.ZERO);
        }
        if(null == refundSettlement.getRefundAmountVoucher()){
            refundSettlement.setRefundAmountVoucher(BigDecimal.ZERO);
        }
        if(null == refundSettlement.getRefundAmountPersonal()){
            refundSettlement.setRefundAmountPersonal(BigDecimal.ZERO);
        }
        if(null == refundSettlement.getRefundAmountThird()){
            refundSettlement.setRefundAmountThird(BigDecimal.ZERO);
        }
        if(null == refundSettlement.getRefundAmountBank()){
            refundSettlement.setRefundAmountBank(BigDecimal.ZERO);
        }
    }

    public PublicPayResult queryPublicPayOrderResult(String fbOrderId) {
        CashierOrderSettlement cashierOrder = this.cashierOrderSettlementManager.queryCashierSettlementByOrderIdAndTradeId(fbOrderId, OrderRootTypeEnum.ROOT_ORDER.getKey(), null);
        if(cashierOrder == null){
            throw new FinhubException(GlobalResponseCode.CASHIER_PAY_SETTLE_NOT_EXIST.getCode(), "found no order while queryPublicPayOrderResult, " + fbOrderId);
        }
        if (CashierPayStatus.CASHIER_DEFAULT.getKey() == cashierOrder.getPayStatus() || CashierPayStatus.CASHIER_FAILED.getKey() == cashierOrder.getPayStatus()) {
            return PublicPayResult.FAIL;
        } else if (CashierPayStatus.CASHIER_TRADING.getKey() == cashierOrder.getPayStatus() || CashierPayStatus.CASHIER_SUCCESS_PAY.getKey() == cashierOrder.getPayStatus()) {
            return PublicPayResult.PROCESSING;
        } else if (CashierPayStatus.CASHIER_SUCCESS_DONE.getKey() == cashierOrder.getPayStatus()) {
            CashierOrderRefundSettlement refundOrder = this.cashierOrderRefundSettlementManager.queryCashierRefundSettlementByOrderIdAndRefundOrderId(fbOrderId, null);
            if (refundOrder == null) {
                return PublicPayResult.SUCC;
            } else if (refundOrder.getRefundStatus() == RefundStatus.REFUND_SUCCESS_DONE.getKey()) {
                return PublicPayResult.FAIL;
            } else {
                return PublicPayResult.PROCESSING;
            }
        } else {
            throw new FinhubException(GlobalResponseCode.CASHIER_PUBLIC_PAYMENT_INVALID_STATUS.getCode(), "unexpected order status while queryPublicPayOrderResult, " + fbOrderId);
        }
    }

    @Override
    public CashierPayDetail4MainOrderRPCDTO searchCashierPayDetail4MainOrder(CashierPayDetail4MainOrderRPCVO cashierPayDetailRPCVo) {
        List<CashierOrderSettlementRelation> cashierOrderSettlementRelations = cashierOrderSettlementRelationManager.queryByFbOrderId(cashierPayDetailRPCVo.getFbOrderId());

        if (CollectionUtils.isNotEmpty(cashierOrderSettlementRelations) && cashierOrderSettlementRelations.size() > 0){
            CashierPayDetail4MainOrderRPCDTO cashierPayDetail4MainOrderRPCDTO = new CashierPayDetail4MainOrderRPCDTO();
            String fbMainOrderId = cashierOrderSettlementRelations.get(0).getFbMainOrderId();
            cashierPayDetail4MainOrderRPCDTO.setFbMainOrderId(fbMainOrderId);
            List<CashierOrderSettlementRelation> cashierOrderSettlementRelationsAll = cashierOrderSettlementRelationManager.queryByFbOrderMainId(fbMainOrderId);
            BigDecimal amountAll = BigDecimal.ZERO;
            BigDecimal amountCompany = BigDecimal.ZERO;
            BigDecimal amountRedCoupon = BigDecimal.ZERO;
            for (CashierOrderSettlementRelation cashierOrderSettlementRelation: cashierOrderSettlementRelationsAll) {
                CashierPayDetailRPCDTO cashierPayDetailRPCDTO =  this.searchCashierPayDetailByOrderId(cashierOrderSettlementRelation.getFbOrderId(), OrderRootTypeEnum.ROOT_ORDER.getKey());
                if (cashierPayDetailRPCDTO != null){
                    amountAll = amountAll.add(cashierPayDetailRPCDTO.getAmountAll());
                    amountCompany = amountRedCoupon.add(cashierPayDetailRPCDTO.getAmountCompany());
                    amountRedCoupon = amountRedCoupon.add(cashierPayDetailRPCDTO.getAmountRedcoupon());
                }
            }
            cashierPayDetail4MainOrderRPCDTO.setAmountAll(amountAll);
            cashierPayDetail4MainOrderRPCDTO.setAmountCompany(amountCompany);
            cashierPayDetail4MainOrderRPCDTO.setAmountRedcoupon(amountRedCoupon);
            cashierPayDetail4MainOrderRPCDTO.setOrderType(cashierOrderSettlementRelations.get(0).getOrderType());
            return cashierPayDetail4MainOrderRPCDTO;
        }
        return null;
    }
}
